#!/usr/bin/env bash

WORK_DIR=$(pwd)
HARE_HOME=$(cd "$(dirname "$0")/.." && pwd)

usage() {
  echo "Usage:"
  echo "  build.sh [-p profile] [-v version]"
  echo ""
  echo "Description:"
  echo "    -p profile: the runtime environment. Can be one of the following: dev, prod, test; default dev."
  echo "    -v version: build version, default is the tag name of current git branch."
  echo ""
  echo "Example:"
  echo "    Build:                                  ./build.sh"
  echo "    Specify version:                        ./build.sh -v 0.0.20"
  echo "    Specify environment:                    ./build.sh -v 0.0.20 -p test"
  echo ""
  exit 0
}

while getopts 'p:v:h' OPT; do
  case $OPT in
  p) BUILD_PROFILE=$(echo "$OPTARG" | tr "[A-Z]" "[a-z]") ;;
  v) VERSION="$OPTARG" ;;
  h) usage ;;
  ?) usage ;;
  esac
done

BUILD_PROFILE="${BUILD_PROFILE:=prod}"
GIT_COMMIT_VERSION=$(cd "$HARE_HOME" && git describe --long HEAD)
VERSION="${VERSION:=${GIT_COMMIT_VERSION%-*}}"
DIST_DIR="$HARE_HOME/dist"
RELEASE_NAME="hare-$VERSION"
RELEASE_DIR="$DIST_DIR/$RELEASE_NAME"

cat <<_END_
Worker directory:              $WORK_DIR
Hare home directory:           $HARE_HOME

Build profile:                 $BUILD_PROFILE
Build version:                 $VERSION
Git commit version:            $GIT_COMMIT_VERSION

Output directory:              $DIST_DIR
Package name:                  $RELEASE_NAME
Package directory:             $RELEASE_DIR

_END_

if [ ! -d "$DIST_DIR" ]; then
  mkdir "$DIST_DIR"
fi
if [ -d "$RELEASE_DIR" ]; then
  echo "Remove exists directory $RELEASE_DIR"
  rm -rf "$RELEASE_DIR"
  echo ""
fi

function replace_all() {
  if [ "$(uname)" == "Darwin" ]; then
    sed -i '' "s/$1/$2/g" "$3"
  elif [[ "$(uname)" == "Linux" ]]; then
    sed -i "s/$1/$2/g" "$3"
    sed -i "s/$1/$2/g" "$3"
  elif [[ "$(uname)" =~ "MINGW64_NT" ]]; then
      sed -i "s|$1|$2|g" "$3"
  else
    echo "WARNING!!: The replaceAll method is not implemented on the $(uname) platform."
  fi
}

function notice_msg() {

  if ! [ -x "$(command -v say)" ]; then
    echo "$1"
  else
    say "$1"
  fi

}

build_component() {

  cd "$HARE_HOME" || exit 1

  echo "Update version to $VERSION..."
  mvn versions:set -DnewVersion="$VERSION"

  mvn clean verify "-P$BUILD_PROFILE"

  result="$?"

  mvn versions:revert

  if [ "$result" = "1" ]; then
    notice_msg "构建 Hare 失败"
    exit 1
  fi
}

build_container() {
  cp -r "$HARE_HOME/container/docker-compose-deploy.yml" "$RELEASE_DIR/docker-compose.yml"

  # shellcheck disable=SC2045
  for FILENAME in $(ls "$RELEASE_DIR/")
  do
    FILEPATH="$RELEASE_DIR/$FILENAME"
    if [ -f "$RELEASE_DIR/$FILENAME" ]; then
      replace_all "{version}" "$VERSION" "$FILEPATH"
    fi

  done

}

build_component
build_container

notice_msg "构建 Hare 完成"