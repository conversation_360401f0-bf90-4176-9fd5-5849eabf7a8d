#!/bin/bash

WORK_DIR=$(pwd)
HARE_HOME=$(cd "$(dirname "$0")/.." && pwd)

function sonarScan() {
  cd "$HARE_HOME" || exit 1
  
  if [[ -n "$HARE_SONAR_QUBE_TOKEN" && -n "$HARE_SONAR_QUBE" ]]; then
    mvn clean verify sonar:sonar \
      -Dsonar.projectKey=Hare \
      -Dsonar.host.url=$HARE_SONAR_QUBE \
      -Dsonar.login=$HARE_SONAR_QUBE_TOKEN
  else
    echo "SonarQube uri or token not set, cancel scan the code. "
  fi
}

sonarScan
