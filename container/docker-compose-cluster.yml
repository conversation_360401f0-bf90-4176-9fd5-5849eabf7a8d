version: "3.4"
services:
  node1:
    hostname: node1
    image: openjdk:17
    restart: always
    stop_grace_period: 10s
    volumes:
      - ../hare-cluster/target/hare-cluster-0.0.1-SNAPSHOT-jar-with-dependencies.jar:/hare-cluster-0.0.1-SNAPSHOT-jar-with-dependencies.jar
    command:
      - java
      - -jar
      - /hare-cluster-0.0.1-SNAPSHOT-jar-with-dependencies.jar
      - node1
    networks:
      hare-network:

  node2:
    hostname: node1
    image: openjdk:17
    restart: always
    stop_grace_period: 10s
    volumes:
      - ../hare-cluster/target/hare-cluster-0.0.1-SNAPSHOT-jar-with-dependencies.jar:/hare-cluster-0.0.1-SNAPSHOT-jar-with-dependencies.jar
    command:
      - java
      - -jar
      - /hare-cluster-0.0.1-SNAPSHOT-jar-with-dependencies.jar
      - node2
    networks:
      hare-network:

  node3:
    hostname: node1
    image: openjdk:17
    restart: always
    stop_grace_period: 10s
    volumes:
      - ../hare-cluster/target/hare-cluster-0.0.1-SNAPSHOT-jar-with-dependencies.jar:/hare-cluster-0.0.1-SNAPSHOT-jar-with-dependencies.jar
    command:
      - java
      - -jar
      - /hare-cluster-0.0.1-SNAPSHOT-jar-with-dependencies.jar
      - node3
    networks:
      hare-network:

networks:
  hare-network:
    driver: bridge