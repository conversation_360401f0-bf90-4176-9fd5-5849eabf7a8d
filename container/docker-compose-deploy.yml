version: "3.4"
services:
  mongo:
    hostname: mongo
    image: mongodb/mongodb-community-server:6.0.6-ubi8
    restart: always
    stop_grace_period: 10s
    environment:
      profile: prod
    ports:
      - "17017:27017"
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      start_period: 5s
      timeout: 10s
    networks:
      hare-network:

  server:
    hostname: server
    image: openjdk
    restart: always
    stop_grace_period: 10s
    depends_on:
      mongo:
        condition: service_healthy
    ports:
      - "18080-18090:8080"
    volumes:
      - "./server-{version}:/opt/server-{version}"
    command:
      - /opt/server-{version}/bin/hare.sh
      - run
    healthcheck:
      test: curl http://localhost:8080/health || exit 1
      interval: 10s
      retries: 10
      start_period: 5s
      timeout: 10s
    networks:
      hare-network:
    deploy:
      replicas: 1
      mode: replicated
      endpoint_mode: vip

  engine:
    hostname: engine
    image: openjdk
    restart: always
    volumes:
      - "./engine-{version}:/opt/engine-{version}"
    command:
      - /opt/engine-{version}/bin/hare.sh
      - run
    healthcheck:
      test: jps -mlv | grep "/opt/engine-{version}" | grep "com.qz.hare.core.HareEngine"
      interval: 10s
      retries: 10
      start_period: 5s
      timeout: 10s
    networks:
      hare-network:

networks:
  hare-network:
    driver: bridge