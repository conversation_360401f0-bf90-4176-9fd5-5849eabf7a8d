version: '3'

services:
  doris-fe:
    image: ghcr.io/tapdata/doris-tapdata-fe:1.1
    networks:
      dorisnetwork:
        ipv4_address: ********
    restart: always
    ports:
      - "8030:8030"
      - "9020:9020"
      - "9030:9030"
      - "9010:9010"
    environment:
      JVM_ARGS: '-Xmx2048m -Xms512m'
    volumes:
      - ./fe:/doris
    entrypoint: ["bash", "/doris/fe.sh"]
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 2G

  doris-be-1:
    image: ghcr.io/tapdata/doris-tapdata-be:1.1
    networks:
      dorisnetwork:
        ipv4_address: ********
    restart: always
    ports:
      - "9060:9060"
      - "8040:8040"
      - "9050:9050"
      - "8060:8060"
    environment:
      JVM_ARGS: '-Xmx2048m -Xms512m'
    volumes:
      - ./be1:/doris
      - ./data/be1:/data
    entrypoint: ["bash", "/doris/be.sh"]
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 2G

  doris-be-2:
    image: ghcr.io/tapdata/doris-tapdata-be:1.1
    networks:
      dorisnetwork:
        ipv4_address: ********
    restart: always
    ports:
      - "19060:19060"
      - "18040:18040"
      - "19050:19050"
      - "18060:18060"
    environment:
      JVM_ARGS: '-Xmx2048m -Xms512m'
    volumes:
      - ./be2:/doris
      - ./data/be2:/data
    entrypoint: ["bash", "/doris/be.sh"]
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 2G

networks:
  dorisnetwork:
    driver: bridge
    ipam:
      config:
        - subnet: 10.0.0.0/24
