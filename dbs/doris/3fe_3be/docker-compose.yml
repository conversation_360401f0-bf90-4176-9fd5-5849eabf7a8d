version: '3'
services:
  docker-fe-01:
    image: "apache/doris:2.0.0_alpha-fe-x86_64"
    container_name: "doris-fe-01"
    hostname: "fe-01"
    environment:
      - FE_SERVERS=fe1:***********:9010,fe2:***********:9010,fe3:***********:9010
      - FE_ID=1
    ports:
      - 8031:8030
      - 9031:9030
    volumes:
      - ./data/fe-01/conf/fe.conf:/opt/apache-doris/fe/conf/fe.conf
      - ./data/fe-01/doris-meta:/opt/apache-doris/fe/doris-meta
      - ./data/fe-01/log:/opt/apache-doris/fe/log
    networks:
      doris_net:
        ipv4_address: ***********
  docker-fe-02:
    image: "apache/doris:2.0.0_alpha-fe-x86_64"
    container_name: "doris-fe-02"
    hostname: "fe-02"
    environment:
      - FE_SERVERS=fe1:***********:9010,fe2:***********:9010,fe3:***********:9010
      - FE_ID=2
    ports:
      - 8032:8030
      - 9032:9030
    volumes:
      - ./data/fe-01/conf/fe.conf:/opt/apache-doris/fe/conf/fe.conf
      - ./data/fe-02/doris-meta:/opt/apache-doris/fe/doris-meta
      - ./data/fe-02/log:/opt/apache-doris/fe/log
    networks:
      doris_net:
        ipv4_address: ***********
  docker-fe-03:
    image: "apache/doris:2.0.0_alpha-fe-x86_64"
    container_name: "doris-fe-03"
    hostname: "fe-03"
    environment:
      - FE_SERVERS=fe1:***********:9010,fe2:***********:9010,fe3:***********:9010
      - FE_ID=3
    ports:
      - 8033:8030
      - 9033:9030
    volumes:
      - ./data/fe-01/conf/fe.conf:/opt/apache-doris/fe/conf/fe.conf
      - ./data/fe-03/doris-meta:/opt/apache-doris/fe/doris-meta
      - ./data/fe-03/log:/opt/apache-doris/fe/log
    networks:
      doris_net:
        ipv4_address: ***********
  docker-be-01:
    image: "apache/doris:2.0.0_alpha-be-x86_64"
    container_name: "doris-be-01"
    hostname: "be-01"
    depends_on:
      - docker-fe-01
      - docker-fe-02
      - docker-fe-03
    environment:
      - FE_SERVERS=fe1:***********:9010,fe2:***********:9010,fe3:***********:9010
      - BE_ADDR=***********:9050
    ports:
      - 8041:8040
    volumes:
      - ./data/be-01/storage:/opt/apache-doris/be/storage
      - ./data/be-01/script:/docker-entrypoint-initdb.d
      - ./data/be-01/log:/opt/apache-doris/be/log
    networks:
      doris_net:
        ipv4_address: ***********
  docker-be-02:
    image: "apache/doris:2.0.0_alpha-be-x86_64"
    container_name: "doris-be-02"
    hostname: "be-02"
    depends_on:
      - docker-fe-01
      - docker-fe-02
      - docker-fe-03
    environment:
      - FE_SERVERS=fe1:***********:9010,fe2:***********:9010,fe3:***********:9010
      - BE_ADDR=***********:9050
    ports:
      - 8042:8040
    volumes:
      - ./data/be-02/storage:/opt/apache-doris/be/storage
      - ./data/be-02/script:/docker-entrypoint-initdb.d
      - ./data/be-02/log:/opt/apache-doris/be/log
    networks:
      doris_net:
        ipv4_address: ***********
  docker-be-03:
    image: "apache/doris:2.0.0_alpha-be-x86_64"
    container_name: "doris-be-03"
    hostname: "be-03"
    depends_on:
      - docker-fe-01
      - docker-fe-02
      - docker-fe-03
    environment:
      - FE_SERVERS=fe1:***********:9010,fe2:***********:9010,fe3:***********:9010
      - BE_ADDR=***********:9050
    ports:
      - 8043:8040
    volumes:
      - ./data/be-03/storage:/opt/apache-doris/be/storage
      - ./data/be-03/script:/docker-entrypoint-initdb.d
      - ./data/be-03/log:/opt/apache-doris/be/log
    networks:
      doris_net:
        ipv4_address: ***********
networks:
  doris_net:
    ipam:
      config:
        - subnet: ***********/24