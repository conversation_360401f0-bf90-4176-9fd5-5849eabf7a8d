version: "3.3"
services:
  lightweight:
    image: apache/hive:3.1.3
    hostname: hive1
    ports:
      - "10000:10000"
      - "10002:10002"
    environment:
      SERVICE_NAME: "hiveserver2"
    restart: always
    networks:
      hivenetwork:

#  metastore:
#    image: apache/hive:3.1.3
#    hostname: node2
#    networks:
#      hivenetwork:
#    ports:
#      - "19083:9083"
#    environment:
#      SERVICE_NAME: "metastore"
#    restart: always
#    volumes:
#      - "./data:/data"
#      - "./conf:/etc/hive"

networks:
  hivenetwork: