version: '3.3'
services:
  rs-node-a:
    image: mongo:4.2.24
    container_name: rs-node-a
    hostname: rs-node-a
    command: mongod --port 27017 --replSet rs
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27000:27017
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s
  rs-node-b:
    image: mongo:4.2.24
    container_name: rs-node-b
    hostname: rs-node-b
    command: mongod --port 27017 --replSet rs
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27001:27017
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s
  rs-node-c:
    image: mongo:4.2.24
    container_name: rs-node-c
    hostname: rs-node-c
    command: mongod --port 27017 --replSet rs
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27002:27017
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s

  # Setup mongodb sharding
  setup:
    image: mongo:4.2.24
    container_name: setup
    restart: no
    depends_on:
      rs-node-a:
        condition: service_started
      rs-node-b:
        condition: service_started
      rs-node-c:
        condition: service_started
    entrypoint:
      - bash
      - /initdb.d/setup.sh
    volumes:
      - ./initdb.d:/initdb.d
    networks:
      mongoCluster:

networks:
  mongoCluster:
    driver: bridge
