MongoDB cluster minimize
=========================================

* Config Server: `configsvr01`
* 2 Shards (each a 2 member `PSS` replica set):
    * `shard01-a`,`shard01-b`, `shard01-c`
    * `shard02-a`,`shard02-b`, `shard02-c`
* 1 Routers (mongos): `router01`

### 👉 Step 1
>Note: Startup all service and initial cluster.

```bash
cd databases/mongodb/shard/minimize
docker-compose up -d
```

### 👉 Step 2
>Note: Wait for the sharded cluster initialization to complete.

```bash
docker compose ps

NAME                IMAGE               COMMAND                  SERVICE             CREATED             STATUS                   PORTS
mongo-config-01     mongo:6.0           "docker-entrypoint.s…"   configsvr01         2 minutes ago       Up 2 minutes (healthy)   0.0.0.0:27119->27017/tcp
router-01           mongo:6.0           "docker-entrypoint.s…"   router01            2 minutes ago       Up 2 minutes (healthy)   0.0.0.0:27117->27017/tcp
shard-01-node-a     mongo:6.0           "docker-entrypoint.s…"   shard01-a           2 minutes ago       Up 2 minutes (healthy)   0.0.0.0:27122->27017/tcp
shard-01-node-b     mongo:6.0           "docker-entrypoint.s…"   shard01-b           2 minutes ago       Up 2 minutes (healthy)   0.0.0.0:27123->27017/tcp
shard-01-node-c     mongo:6.0           "docker-entrypoint.s…"   shard01-c           2 minutes ago       Up 2 minutes (healthy)   0.0.0.0:27124->27017/tcp
shard-02-node-a     mongo:6.0           "docker-entrypoint.s…"   shard02-a           2 minutes ago       Up 2 minutes (healthy)   0.0.0.0:27125->27017/tcp
shard-02-node-b     mongo:6.0           "docker-entrypoint.s…"   shard02-b           2 minutes ago       Up 2 minutes (healthy)   0.0.0.0:27126->27017/tcp
shard-02-node-c     mongo:6.0           "docker-entrypoint.s…"   shard02-c           2 minutes ago       Up 2 minutes (healthy)   0.0.0.0:27127->27017/tcp
```

---
### ✔️ Done !!!

#### But before you start inserting data you should verify them first
---

## 📋 Verify [🔝](#-table-of-contents)

### ✅ Verify the status of the sharded cluster [🔝](#-table-of-contents)

```bash
docker-compose exec router01 mongosh --port 27017
sh.status()
```

### ✅ Verify status of replica set for each shard [🔝](#-table-of-contents)
> You should see 1 PRIMARY, 2 SECONDARY

```bash
docker-compose exec shard01-a bash -c "echo 'rs.status()' | mongosh --port 27017" 
docker-compose exec shard02-a bash -c "echo 'rs.status()' | mongosh --port 27017" 
```

### ✅ Verify status of replica set for config server [🔝](#-table-of-contents)
> You should see 1 PRIMARY

```bash
docker-compose exec configsvr01 bash -c "echo 'rs.status()' | mongosh --port 27017" 
```

### ✅ Check database status
```bash
docker-compose exec router01 mongosh "mongodb://localhost:27017/MyDatabase"
db.stats()
db.MyCollection.getShardDistribution()
```




## !!! If you want to add new shard to existed cluster, please go on

### 👉 Step 1
>Note: Startup shard 03 replicaset cluster.
```bash
docker compose  -f docker-compose-shard-03.yml up
```

### 👉 Step 2
>Note: Init replicas for new shard

```bash
docker-compose exec shard03-a sh -c "mongosh < /initdb.d/init-shard03.js"
```

### 👉 Step 3
>Note: Add new shard

exec to a router node

```bash
docker-compose exec router01 mongosh --port 27017
```

and run:

```bash
sh.addShard("rs-shard-03/shard-03-node-a:27017,shard-03-node-b:27017,shard-03-node-c:27017")
```

### ✅ Verify and run mongo balancer [🔝](#-table-of-contents)

```bash
docker-compose exec router01 mongosh --port 27017
> sh.status()
> sh.startBalancer()
> db.stats()
```