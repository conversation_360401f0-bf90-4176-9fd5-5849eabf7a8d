version: '3.3'
services:

  ## Shards
  ## Shards 03
  shard03-a:
    image: mongo:6.0
    container_name: shard-03-node-a
    hostname: shard-03-node-a
    command: mongod --port 27017 --shardsvr --replSet rs-shard-03
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27128:27017
    networks:
      mongoCluster:
    depends_on:
      shard03-b:
        condition: service_started
      shard03-c:
        condition: service_started
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s
  shard03-b:
    image: mongo:6.0
    container_name: shard-03-node-b
    hostname: shard-03-node-b
    command: mongod --port 27017 --shardsvr --replSet rs-shard-03
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27129:27017
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s
  shard03-c:
    image: mongo:6.0
    container_name: shard-03-node-c
    hostname: shard-03-node-c
    command: mongod --port 27017 --shardsvr --replSet rs-shard-03
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27130:27017
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s

networks:
  mongoCluster:
    driver: bridge