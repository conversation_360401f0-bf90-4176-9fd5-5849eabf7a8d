version: '3.4'

services:

  ## Router (mongos)
  router01:
    image: mongo:6.0
    container_name: router-01
    hostname: router-01
    command: mongos --port 27017 --configdb rs-config-server/configsvr01:27017 --bind_ip_all
    ports:
      - 27117:27017
    volumes:
      - ./initdb.d:/initdb.d
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s

  ## Config Servers
  configsvr01:
    image: mongo:6.0
    container_name: mongo-config-01
    hostname: mongo-config-01
    command: mongod --port 27017 --configsvr --replSet rs-config-server
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27119:27017
    depends_on:
      shard01-a:
        condition: service_started
      shard02-a:
        condition: service_started
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s

  ## Shards
  ## Shards 01
  shard01-a:
    image: mongo:6.0
    container_name: shard-01-node-a
    hostname: shard-01-node-a
    command: mongod --port 27017 --shardsvr --replSet rs-shard-01
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27122:27017
    networks:
      mongoCluster:
    depends_on:
      shard01-b:
        condition: service_started
      shard01-c:
        condition: service_started
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s

  shard01-b:
    image: mongo:6.0
    container_name: shard-01-node-b
    hostname: shard-01-node-b
    command: mongod --port 27017 --shardsvr --replSet rs-shard-01
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27123:27017
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s
  shard01-c:
    image: mongo:6.0
    container_name: shard-01-node-c
    command: mongod --port 27017 --shardsvr --replSet rs-shard-01
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27124:27017
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s

  ## Shards 02
  shard02-a:
    image: mongo:6.0
    container_name: shard-02-node-a
    hostname: shard-02-node-a
    command: mongod --port 27017 --shardsvr --replSet rs-shard-02
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27125:27017
    networks:
      mongoCluster:
    depends_on:
      shard02-b:
        condition: service_started
      shard02-c:
        condition: service_started
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s
  shard02-b:
    image: mongo:6.0
    container_name: shard-02-node-b
    hostname: shard-02-node-b
    command: mongod --port 27017 --shardsvr --replSet rs-shard-02
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27126:27017
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s
  shard02-c:
    image: mongo:6.0
    container_name: shard-02-node-c
    hostname: shard-02-node-c
    command: mongod --port 27017 --shardsvr --replSet rs-shard-02
    volumes:
      - ./initdb.d:/initdb.d
    ports:
      - 27127:27017
    networks:
      mongoCluster:
    healthcheck:
      test: mongosh "mongodb://localhost:27017/admin" --eval "db.stats()"
      interval: 10s
      retries: 10
      timeout: 10s

  # Setup mongodb sharding
  setup:
    image: mongo:6.0
    container_name: setup
    restart: no
    depends_on:
      router01:
        condition: service_started
      configsvr01:
        condition: service_started
    entrypoint:
      - bash
      - /initdb.d/setup.sh
    volumes:
      - ./initdb.d:/initdb.d
    networks:
      mongoCluster:

networks:
  mongoCluster:
    driver: bridge