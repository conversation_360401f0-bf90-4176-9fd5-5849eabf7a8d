sh.addShard("rs-shard-01/shard-01-node-a:27017");
sh.addShard("rs-shard-01/shard-01-node-b:27017");
sh.addShard("rs-shard-01/shard-01-node-c:27017");
sh.addShard("rs-shard-02/shard-02-node-a:27017");
sh.addShard("rs-shard-02/shard-02-node-b:27017");
sh.addShard("rs-shard-02/shard-02-node-c:27017");

sh.enableSharding("MyDatabase");
db.adminCommand( { shardCollection: "MyDatabase.MyCollection", key: { oemNumber: "hashed", zipCode: 1, supplierId: 1 } } );

