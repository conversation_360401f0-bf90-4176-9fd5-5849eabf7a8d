#!/bin/bash

# Init shard01 replicaset
mongosh "mongodb://shard-01-node-a:27017" /initdb.d/init-shard01.js
# Init shard02 replicaset
mongosh "mongodb://shard-02-node-a:27017" /initdb.d/init-shard02.js

# Init config replicaset
mongosh "mongodb://mongo-config-01:27017" /initdb.d/init-config-server.js

# Note: Wait a bit for the config server and shards to elect their primaries before initializing the router
sleep 10s

# Add shard node into router
mongosh "mongodb://router-01:27017" /initdb.d/init-router.js

#mongosh "mongodb://router-01:27017/MyDatabase" --eval "db.stats(); db.MyCollection.getShardDistribution();"