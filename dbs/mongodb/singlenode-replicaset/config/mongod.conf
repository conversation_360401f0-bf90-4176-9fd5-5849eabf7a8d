systemLog:
   path: /var/log/mongo/mongod.log
   verbosity: 0
   quiet: false
   traceAllExceptions: false
   destination: file
   logAppend: true
   logRotate: rename

#processManagement:
#   fork: true

storage:
   dbPath: /data
   journal:
      enabled: true
   syncPeriodSecs: 60
   engine: wiredTiger
   wiredTiger:
      engineConfig:
         cacheSizeGB: 1
         journalCompressor: snappy
         directoryForIndexes: false
      collectionConfig:
         blockCompressor: snappy
      indexConfig:
         prefixCompression: true

net:
  bindIp: 0.0.0.0
  port: 27017
  maxIncomingConnections: 20000

#security:
#  keyFile: /etc/mongo/keys

replication:
   oplogSizeMB: 1024
   replSetName: rs0

