version: "3.3"
services:
    mongo:
        hostname: mdb0
        image: mongo:6.0
        restart: always
        environment:
            #MONGO_INITDB_ROOT_USERNAME: root
            #MONGO_INITDB_ROOT_PASSWORD: example
            #MONGO_INITDB_DATABASE: /initdb.d
            profile: prod
        volumes:
            - "./bin/startup.sh:/startup.sh"
            - "./config:/etc/mongo"
            - "./data:/data"
            - "./logs:/var/log/mongo"
            - "./initdb.d:/docker-entrypoint-initdb.d"
        ports:
            - "27006:27017"
        command:
            - bash
            - /startup.sh

        healthcheck:
            test: mongosh "mongodb://localhost:27017/admin" --eval "db.hello()"
            interval: 10s
            retries: 10
            timeout: 10s

        networks:
            mongoCluster:

networks:
    mongoCluster:
        driver: bridge
        ipam:
            config:
                - subnet: ***********/16
                  ip_range: ***********/24
                  gateway: *************
