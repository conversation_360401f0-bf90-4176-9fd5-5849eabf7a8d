version: "3.3"
services:
    mongo:
        hostname: s-mdb-0
        image: mongo:6.0
        restart: always
        environment:
            MONGO_INITDB_ROOT_USERNAME: root
            MONGO_INITDB_ROOT_PASSWORD: root
            #MONGO_INITDB_DATABASE: /initdb.d
            profile: prod
        volumes:
            - "./config:/etc/mongo"
        ports:
            - "27005:27017"

        networks:
            mongoCluster:

networks:
    mongoCluster:
        driver: bridge
        ipam:
            config:
                - subnet: ***********/16
                  ip_range: ***********/24
                  gateway: *************
