version: "3.3"
services:
  mssql2019:
    image: mcr.microsoft.com/mssql/server:2019-latest
    hostname: mssql2019
    networks:
      mssqlnetwork:
    ports:
      - "1433:1433"
    environment:
      ACCEPT_EULA: Y
      MSSQL_SA_PASSWORD: Df652dd!23
      MSSQL_PID: Standard
    restart: always
    volumes:
      - "./2019/data:/var/opt/mssql"

  mssql2022:
    image: mcr.microsoft.com/mssql/server:2022-latest
    hostname: mssql2022
    networks:
      mssqlnetwork:
    ports:
      - "1433:1433"
    environment:
      ACCEPT_EULA: Y
      MSSQL_SA_PASSWORD: Df652dd!23
      MSSQL_PID: Standard
    restart: always
    volumes:
      - "./2022/data:/var/opt/mssql"

networks:
  mssqlnetwork:
