use msdb;
use tapdata;

select * from sys.tables;

SELECT      t.name AS tableName,      ps.name AS schemaName,      pf.name AS functionName, t.object_id
FROM sys.tables t
    JOIN sys.indexes i ON t.object_id = i.object_id
    JOIN sys.partition_schemes ps ON i.data_space_id = ps.data_space_id
    JOIN sys.partition_functions pf ON ps.function_id = pf.function_id
WHERE       t.name = 't_measurement';

DROP PARTITION FUNCTION my_partition_fun_month;
CREATE PARTITION FUNCTION my_partition_fun_month (datetime)
AS RANGE RIGHT FOR VALUES ('2024-01-01', '2024-02-01', '2024-03-01', '2024-04-01',
               '2024-05-01', '2024-06-01', '2024-07-01', '2024-08-01',
               '2024-09-01', '2024-10-01', '2024-11-01', '2024-12-01');

CREATE PARTITION SCHEME my_partition_scheme_month
    AS PARTITION my_partition_fun_month
    ALL TO ( [PRIMARY] );

CREATE TABLE test_measurement (
    city_id int not null ,
    logdate datetime not null,
    peaktemp int,
    unitsales int
) on my_partition_scheme_month (logdate)
;
CREATE INDEX my_partition_index_month
ON test_measurement (logdate)
ON my_partition_scheme_month(logdate);

drop index my_partition_index_month on test_measurement;

drop partition scheme my_partition_scheme_month;
drop partition function my_partition_fun_month;


insert into test_measurement values (0, ('2024-01-01'), 33, 100);
insert into test_measurement values (1, ('2024-01-01'), 35, 100);
insert into test_measurement values (2, ('2024-01-01'), 30, 100);
insert into test_measurement values (3, ('2024-01-01'), 30, 90);
insert into test_measurement values (0, ('2024-02-02'), 33, 100);
insert into test_measurement values (1, ('2024-02-02'), 35, 100);
insert into test_measurement values (2, ('2024-02-02'), 30, 100);
insert into test_measurement values (3, ('2024-02-02'), 30, 90);
insert into test_measurement values (0, ('2024-03-02'), 33, 100);
insert into test_measurement values (1, ('2024-03-02'), 35, 100);
insert into test_measurement values (2, ('2024-03-02'), 30, 100);
insert into test_measurement values (3, ('2024-03-02'), 30, 90);
insert into test_measurement values (0, ('2024-04-02'), 33, 100);
insert into test_measurement values (1, ('2024-04-02'), 35, 100);
insert into test_measurement values (2, ('2024-04-02'), 30, 100);
insert into test_measurement values (3, ('2024-04-02'), 30, 90);
insert into test_measurement values (0, ('2024-05-02'), 33, 100);
insert into test_measurement values (1, ('2024-05-02'), 35, 100);
insert into test_measurement values (2, ('2024-05-02'), 30, 100);
insert into test_measurement values (3, ('2024-05-02'), 30, 90);
insert into test_measurement values (0, ('2024-05-03'), 33, 100);
insert into test_measurement values (1, ('2024-05-03'), 35, 100);
insert into test_measurement values (2, ('2024-05-03'), 30, 100);
insert into test_measurement values (3, ('2024-05-03'), 30, 90);

insert into test_measurement values (0, ('2024-06-05'), 33, 100);
insert into test_measurement values (1, ('2024-06-05'), 35, 100);
insert into test_measurement values (2, ('2024-06-05'), 30, 100);
insert into test_measurement values (3, ('2024-06-05'), 30, 90);

select t.*, $PARTITION.my_partition_fun_month(logdate) partition_num from test_measurement t;
select count(1) from measurement;
select t.*, $PARTITION.partition_function_measurement(logdate) partition_num from measurement t;
select * from partition2normal_measurement;
select * from sys.partition_functions;


select count(1) from t_measurement;
delete from t_measurement where id > 0;

SELECT
    sch.name AS SchemaName,
    tbl.name AS TableName,
    idx.name AS IndexName,
    ps.name AS PartitionScheme,
    pf.name AS PartitionFunction,
    p.partition_number AS PartitionNumber,
    p.rows AS RowsCount,
    p.data_compression_desc AS CompressionType
FROM
    sys.partitions p
JOIN
    sys.tables tbl ON p.object_id = tbl.object_id
JOIN
    sys.schemas sch ON tbl.schema_id = sch.schema_id
JOIN
    sys.indexes idx ON p.object_id = idx.object_id AND p.index_id = idx.index_id
LEFT JOIN
    sys.partition_schemes ps ON idx.data_space_id = ps.data_space_id
LEFT JOIN
    sys.partition_functions pf ON ps.function_id = pf.function_id
WHERE
    p.index_id IN (0, 1)  -- 0 = Heap Table, 1 = Clustered Index
ORDER BY
    tbl.name, p.partition_number;

select * from sys.partitions;

select name, f.function_id, type, type_desc, fanout, boundary_value_on_right, create_date, modify_date, boundary_id, parameter_id, value
from
    sys.partition_functions f
LEFT JOIN sys.partition_range_values rv on f.function_id = rv.function_id;