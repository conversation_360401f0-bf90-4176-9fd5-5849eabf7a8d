version: "3.3"
services:
  mysql8:
    image: mysql:8.0.30
    hostname: mysql8
    networks:
      dbs:
    ports:
      - "13306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: "root"
    restart: always
    volumes:
      - "./v8/data:/var/lib/mysql"
      - "./v8/conf/mysqld.cnf:/etc/my.cnf"
  mysql5.6:
    image: mysql:5.6
    hostname: mysql5
    networks:
      dbs:
    ports:
      - "13307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: "root"
    restart: always
    volumes:
      - "./v5.6/data:/var/lib/mysql"
      - "./v5.6/conf/mysqld.cnf:/etc/my.cnf"

networks:
  dbs: