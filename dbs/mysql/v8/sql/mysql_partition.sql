
-- 查看是否支持分区: have_partitioning = YES
SHOW VARIABLES LIKE '%partition%';
SELECT
    PLUGIN_NAME as Name,
    PLUGIN_VERSION as Version,
    PLUGIN_STATUS as Status
FROM INFORMATION_SCHEMA.PLUGINS
WHERE PLUGIN_TYPE='STORAGE ENGINE';

CREATE TABLE `TEST_PARTITION` (
     `id` INT primary key,
     `name` VARCHAR(50),
     `email` VARCHAR(50) unique,
     `purchased` DATE
) ENGINE=InnoDB DEFAULT CHARSET=utf8
PARTITION BY RANGE( YEAR(purchased) ) (
     PARTITION p0 VALUES LESS THAN (1990),
     PARTITION p1 VALUES LESS THAN (1995),
     PARTITION p2 VALUES LESS THAN (2000),
     PARTITION p3 VALUES LESS THAN (2005),
     PARTITION p4 VALUES LESS THAN (2010),
     PARTITION p5 VALUES LESS THAN (2015)
);
-- 添加分区表
ALTER TABLE TEST_PARTITION ADD PARTITION(PARTITION p6 VALUES LESS THAN (2020));
-- 删除分区
alter table TEST_PARTITION drop partition p1;
-- 查看分区
SELECT * FROM INFORMATION_SCHEMA.PARTITIONS WHERE TABLE_NAME = 'TEST_PARTITION';
-- 查询分区的数据
SELECT * FROM TEST_PARTITION PARTITION (p2);
-- 清空分区的数据
ALTER TABLE TEST_PARTITION TRUNCATE PARTITION P0;




