show databases ;
show tables;
desc TEST_TABLE_INFO;
alter table t1 add column (
    age int default 25
    );
alter table t1 comment 'test table';
insert into t1 (id, year_col, name) values (
                       1, 2, 'abcdef<PERSON>ijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcedfghijklmnopqrstuv'
                      );
CREATE TABLE TEST_TABLE_INFO (
    ID INT PRIMARY KEY AUTO_INCREMENT,
    NAME VARCHAR(64) NOT NULL ,
    EMAIL VARBINARY(64) UNIQUE ,
    SEX VARCHAR(2) DEFAULT '男'
);
CREATE INDEX NAME_EMAIL_IDX ON TEST_TABLE_INFO ( NAME ASC, EMAIL DESC) COMMENT '索引';


SELECT * FROM `INFORMATION_SCHEMA`.`TABLES` WHERE TABLE_SCHEMA = 'test' AND TABLE_NAME LIKE 'ROUTINES%';
SELECT * FROM information_schema.STATISTICS WHERE TABLE_SCHEMA = 'test' ;
SELECT * FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_TYPE = 'FUNCTION';
SELECT * FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'test' AND ROUTINE_TYPE = 'PROCEDURE';
SELECT * FROM information_schema.TRIGGERS WHERE TRIGGER_SCHEMA = 'test';

SELECT TABLE_NAME AS tableName, TABLE_TYPE AS tableType, TABLE_COMMENT AS tableComment FROM `INFORMATION_SCHEMA`.`TABLES` WHERE TABLE_SCHEMA = 'test';
SELECT DATABASE();
SELECT VERSION();
SELECT @@character_set_database;
SELECT * FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = 'test';
SELECT @@SQL_MODE;

-- 7569338
select count(1) from user_table_10W;

select * from information_schema.columns where table_schema = 'test' and table_name = 'TEST_TABLE_INFO' order by ORDINAL_POSITION;
desc information_schema.columns;

SELECT TABLE_NAME `tableName`,
       COLUMN_NAME `columnName`,
       ORDINAL_POSITION `position`,
       COLUMN_DEFAULT `columnDefault`,
       IS_NULLABLE `nullable`,
       DATA_TYPE `dataType`,
       CHARACTER_MAXIMUM_LENGTH `maxLength`,
       NUMERIC_PRECISION `precision`,
       NUMERIC_SCALE `scale`,
       DATETIME_PRECISION `precision`,
       CHARACTER_SET_NAME `charsetName`,
       COLUMN_TYPE `columnType`,
       COLUMN_KEY `columnKey`,
       COLUMN_COMMENT `columnComment`
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'test' AND TABLE_NAME = 'user_table_0'
ORDER BY ORDINAL_POSITION;

select * from INFORMATION_SCHEMA.STATISTICS where table_schema = 'test' order by index_name, seq_in_index;
SELECT
    TABLE_NAME `tableName`,COLUMN_NAME `columnName`,INDEX_NAME `indexName`,
    INDEX_TYPE `indexType`,
    (CASE WHEN COLLATION = 'A' THEN 'ASC' WHEN COLLATION = 'D' THEN 'DESC' ELSE NULL END) `sort`,
    (CASE WHEN NON_UNIQUE = 0 THEN 1 ELSE 0 END) `isUnique`,
    (CASE WHEN INDEX_NAME = 'PRIMARY' THEN 1 ELSE 0 END) `primaryKey`
FROM
	INFORMATION_SCHEMA.STATISTICS
WHERE
	TABLE_SCHEMA = 'test'
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX;

SHOW PLUGINS;

show tables;

SHOW GRANTS FOR CURRENT_USER;

create database test_hare;
create user hare identified by 'hare';
grant select on test_hare.* to `hare`@`%` with grant option ;

SHOW GLOBAL VARIABLES where Variable_name in ('log_bin', 'binlog_format');
SHOW GLOBAL VARIABLES where variable_name = 'log_bin' OR variable_name = 'binlog_format';
SHOW VARIABLES LIKE 'binlog_row_image%';
show create table user_table_10W;

select * from information_schema.tables where table_schema = 'test';

select count(1) from user_table_1000W;