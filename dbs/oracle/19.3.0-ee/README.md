

# Startup db

```shell

cd databases/oracle
docker-compose up

# 打印下面内容是时，表示已经初始化完成
##########################
# DATABASE IS READY TO USE!
##########################
```




# Connect db

```shell
sqlplus sys/Oracle!23@//localhost:1521/ORCLCDB as sysdba

sqlplus system/Oracle!23@//localhost:1521/ORCLCDB

sqlplus C##USR1/USR1PWD@//localhost:1521/ORCLCDB

```

# Connect db using TCPS

```shell
docker cp <container name>:/opt/oracle/oradata/clientWallet/ORCLCDB <destination wallet directory>

sqlplus sys@tcps://<host>:<port>/<service_name>?wallet_location=<destination wallet directory> as sysdba

```