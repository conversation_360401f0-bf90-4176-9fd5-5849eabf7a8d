version: "3.3"
services:
  oracle:
    image: tapdata-docker.pkg.coding.net/tapdata/databases/oracle:19.3.0-ee
    hostname: oracle
    networks:
      oraclenetwork:
    ports:
      - "1521:1521"
      - "2484:2484"
      - "5500:5500"
    environment:
      ORACLE_PWD: "Oracle!23"
      ENABLE_ARCHIVELOG: "true"
      ORACLE_SID: ORCLCDB
      INIT_CPU_COUNT: 4
      INIT_PROCESSES: 4
      ENABLE_TCPS: "true"
    restart: always
    volumes:
      - "./data/oradata:/opt/oracle/oradata"
      - "./data/fast_recovery_area:/opt/oracle/fast_recovery_area"
      - "./startup:/opt/oracle/scripts/startup"
      - "./setup:/opt/oracle/scripts/setup"

networks:
  oraclenetwork: