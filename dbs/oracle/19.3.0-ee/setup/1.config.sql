alter system set db_create_file_dest='/opt/oracle/oradata' scope = both;
alter system set db_recovery_file_dest_size='50g' scope = both;
alter system set db_recovery_file_dest='/opt/oracle/fast_recovery_area' scope = both;

SHUTDOWN IMMEDIATE;
STARTUP MOUNT;
ALTER DATABASE ARCHIVELOG;
ALTER DATABASE OPEN;
ALTER DATABASE ADD SUPPLEMENTAL LOG DATA;
ALTER SYSTEM SWITCH LOGFILE;

CREATE USER C##USR1 IDENTIFIED BY USR1PWD DEFAULT TABLESPACE "USERS" TEMPORARY TABLESPACE "TEMP";
ALTER USER C##USR1 QUOTA UNLIMITED ON USERS;
GRANT DBA TO C##USR1 WITH ADMIN OPTION;
GRANT SELECT, FLASHBACK ON SYS.CCOL$ TO C##USR1;
GRANT SELECT, FLASHBACK ON SYS.CDEF$ TO C##USR1;
GRANT SELECT, FLASH<PERSON>CK ON SYS.COL$ TO C##USR1;
GRANT SELECT, <PERSON>ASH<PERSON>CK ON SYS.DEFERRED_STG$ TO C##USR1;
GRANT SELECT, FLASHBACK ON SYS.ECOL$ TO C##USR1;
GRANT SELECT, FLASHBACK ON SYS.OBJ$ TO C##USR1;
GRANT SELECT, FLASHBACK ON SYS.SEG$ TO C##USR1;
GRANT SELECT, FLASHBACK ON SYS.TAB$ TO C##USR1;
GRANT SELECT, FLASHBACK ON SYS.TABCOMPART$ TO C##USR1;
GRANT SELECT, FLASHBACK ON SYS.TABPART$ TO C##USR1;
GRANT SELECT, FLASHBACK ON SYS.TABSUBPART$ TO C##USR1;
GRANT SELECT, FLASHBACK ON SYS.USER$ TO C##USR1;
GRANT SELECT ON SYS.V_$ARCHIVED_LOG TO C##USR1;
GRANT SELECT ON SYS.V_$DATABASE TO C##USR1;
GRANT SELECT ON SYS.V_$DATABASE_INCARNATION TO C##USR1;
GRANT SELECT ON SYS.V_$LOG TO C##USR1;
GRANT SELECT ON SYS.V_$LOGFILE TO C##USR1;
GRANT SELECT ON SYS.V_$PARAMETER TO C##USR1;
GRANT SELECT ON SYS.V_$STANDBY_LOG TO C##USR1;
GRANT SELECT ON SYS.V_$TRANSPORTABLE_PLATFORM TO C##USR1;
