version: "3.3"
services:
  postgres:
    # image: postgres:latest
    build:
      dockerfile: Dockerfile
    container_name: postgres
    restart: always
    shm_size: 128mb
    ports:
      - 5432:5432
    environment:
      TZ: "Asia/Shanghai"
      POSTGRES_DB: "postgres"
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "123456"
      PGDATA: /data
    volumes:
      - ./data:/data
      - ./postgresql.conf:/etc/postgresql/postgresql.conf
      - ./pg_hba.conf:/etc/postgresql/pg_hba.conf
    command:
      - postgres
      - -c
      - config_file=/etc/postgresql/postgresql.conf

    networks:
      dbs:


#  adminer:
#    image: adminer
#    restart: always
#    ports:
#      - 18080:8080
#    networks:
#      - postgres-network

networks:
  dbs:
