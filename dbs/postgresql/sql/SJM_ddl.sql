
create database sjm;

-- Table: wdts_reporting.t_alert
-- DROP TABLE IF EXISTs wdts_reporting.t_alert;

create schema wdts_reporting;

GRANT INSERT,UPDATE,DELETE,TRUNCATE
ON ALL TABLES IN SCHEMA wdts_reporting TO postgres;

CREATE TABLE IF NOT EXISTS wdts_reporting.t_alert
(
    alert_id bigint NOT NULL,
    event_id integer,
    event_type_id integer NOT NULL,
    alert_severity character varying(10) COLLATE pg_catalog."default",
    alert_status character varying(15) COLLATE pg_catalog."default",
    created_dtm timestamp with time zone NOT NULL,
    updated_dtm timestamp with time zone ,
    lud_user_id integer,
    topology_id integer,
    topology_name character varying(100) COLLATE pg_catalog."default",
    type character varying(100)COLLATE pg_catalog."default",
    supervisor character varying(100) COLLATE pg_catalog."default",
    dealer character varying(100)COLLATE pg_catalog."default",
    game_id bigint,
    shoe_id bigint,
    category character varying(50) COLLATE pg_catalog."default",
    subcategory character varying(50)COLLATE pg_catalog."default",
    pit_name character varying(100) COLLATE pg_catalog."default",
    gaming_area character varying(100) COLLATE pg_catalog."default",
    event_type character varying(100) COLLATE pg_catalog."default",
    cashier character varying(100) COLLATE pg_catalog."default",
    CONSTRAINT t_alert_pkey PRIMARY KEY (alert_id, event_type_id, created_dtm)
)PARTITION BY RANGE(created_dtm);

-- ALTER TABLE IF EXIsTs wdts_reporting.t_alert OWNER to wdts_admin;
-- REVOKE ALL ON TABLE wdts_reporting.t_alert FROM sim_admin;
-- REVOKE ALL ON TABLE wdts_reporting.t_alert FRoM tapdata;
-- GRANT SELECT ON TABLE wdts_reporting.t_alert To sjm_admin;
--
-- GRANT SELECT ON TABLE wdts_reporting.t_alert To tapdata;
--
-- GRANT ALL ON TABLE wdts_reporting.t_alert To wdts_admin;

-- Index:t alert eid idx
-- DROP INDEX IF EXIsTs wdts_reporting.t_alert_eid_idx;
CREATE INDEX IF NOT EXISTS t_alert_eid_idx
    ON wdts_reporting.t_alert USING btree(event_id ASC NULLS LAST);

-- Index: t_alert_etid idx
-- DROP INDEX IF EXIsTs wdts_reporting.t alert_etid_idx;
CREATE INDEX IF NOT EXISTS t_alert_etid_idx
    ON wdts_reporting.t_alert USING btree(event_type_id ASC NULLS LAST);

-- Index: t_alert_tid_idx
-- DROP INDEX IF EXIsTs wdts_reporting.t alert_tid idx;
CREATE INDEX IF NOT EXISTS t_alert_tid_idx
ON wdts_reporting.t_alert USING btree(topology_id ASC NULLS LAST);

-- Partitions sQL
CREATE TABLE wdts_reporting.t_alert_202405 PARTITION OF wdts_reporting.t_alert
FOR VALUES FROM('2024-05-01 00:00:00+00') TO ('2024-06-01 00:00:00+00')
TABLESPACE pg_default;
-- ALTER TABLE IF EXISTs wdts_reporting.t_alert_202405 OWNER to wdts_admin;

CREATE TABLE wdts_reporting.t_alert_202406 PARTITION OF wdts_reporting.t_alert
    FOR VALUES FROM('2024-06-01 00:00:00+00') TO ('2024-07-01 00:00:00+00')
TABLESPACE pg_default;
-- ALTER TABLE IF EXISTS wdts_reporting.t_alert_202406 OWNER to wdts_admin;

CREATE TABLE wdts_reporting.t_alert_202407 PARTITION OF wdts_reporting.t_alert
    FOR VALUES FROM('2024-07-01 00:00:00+00') TO ('2024-08-01 00:00:00+00')
TABLESPACE pg_default;
-- ALTER TABLE IF EXIsTS wdts_reporting.t_alert_202407 OWNER to wdts_admin;

CREATE TABLE wdts_reporting.t_alert_202408 PARTITION OF wdts_reporting.t_alert
    FOR VALUES FROM('2024-08-01 00:00:00+00') TO ('2024-09-01 00:00:00+00')
TABLESPACE pg_default;
-- ALTER TABLE IF EXISTs wdts_reporting.t_alert_202408 OWNER to wdts_admin;

CREATE TABLE wdts_reporting.t_alert_202409 PARTITION OF wdts_reporting.t_alert
    FOR VALUES FROM('2024-09-01 00:00:00+00') TO ('2024-10-01 00:00:00+00')
TABLESPACE pg_default;
--ALTER TABLE IF EXISTs wdts_reporting.t_alert_202409 OWNER to wdts_admin;

CREATE TABLE wdts_reporting.t_alert_202410 PARTITION OF wdts_reporting.t_alert
    FOR VALUES FROM('2024-10-01 00:00:00+00')TO('2024-11-01 00:00:00+0')
TABLESPACE pg_default;
-- ALTER TABLE IF EXIsTs wdts_reporting.t_alert_202410 OWNER to wdts_admin;

CREATE TABLE wdts_reporting.t_alert_202411 PARTITION OF wdts_reporting.t_alert
    FOR VALUES FROM('2024-11-01 00:00:00+00')TO('2024-12-01 00:00:00+00')
TABLESPACE pg_default;
-- ALTER TABLE IF EXISTS wdts_reporting.t_alert_202411 OWNER to wdts_admin;

CREATE TABLE wdts_reporting.t_alert_202412 PARTITION OF wdts_reporting.t_alert
    FOR VALUES FROM('2024-12-01 00:00:00+00') TO ('2025-01-01 00:00:00+00')
TABLESPACE pg_default;
-- ALTER TABLE IF EXISTs wdts_reporting.t_alert_202412 OWNER to wdts_admin;

CREATE TABLE wdts_reporting.t_alert_default_parition PARTITION OF wdts_reporting.t_alert
    DEFAULT
TABLESPACE pg_default;
-- ALTER TABLE IF EXISTS wdts_reporting.t_alert_deault_partition OWNER to wdts_admin;

insert into wdts_reporting.t_alert values (2, 1, 1, 'info', 'off',
                                           current_timestamp, current_timestamp, 1, 1,
                                           'topology name', 'off', 'test', 'test', 1,
                                           1, 'category', 'sub category', 'pit name',
                                           'gaming area', 'event type', 'cashier');
select * from wdts_reporting.t_alert;
update t_alert set type = 'on' where alert_id = 2;