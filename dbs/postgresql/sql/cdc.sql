
-- test cdc
create table public.test_decode(
  uid    integer not null
         constraint users_pk
         primary key,
  name   varchar(50),
  age    integer,
  score  decimal
);

select * from pg_create_logical_replication_slot('slot_test', 'wal2json');
insert into public.test_decode values (1, '<PERSON>', 25, 100);
insert into public.test_decode values (2, '<PERSON>', 35, 90);
update public.test_decode set name = '<PERSON>' where uid = 1;
delete from public.test_decode where uid = 2;
commit;
select * from pg_logical_slot_peek_changes('tapdata_cdc_03eeb379_068d_447d_831d_69a63dd78bdb', null, null);
select * from pg_stat_replication;
select * from pg_replication_slots;
select * from pg_drop_replication_slot('tapdata_cdc_03eeb379_068d_447d_831d_69a63dd78bdb');
drop table public.test_decode;
