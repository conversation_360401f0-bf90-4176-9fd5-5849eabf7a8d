WITH
all_tables AS (
    SELECT c.oid                        AS table_oid,
           c.relfilenode                as relfilenode,
           c.relname                    AS table_name,
           n.nspname                    AS schema_name,
           i.inhparent                  as inhparent,
           CASE
               WHEN p.partrelid IS NOT NULL THEN 'Partitioned Table'
               WHEN i.inhrelid IS NOT NULL THEN 'Child Table'
               WHEN c.relhassubclass = true THEN 'Parent Table'
               ELSE 'Regular Table' END AS table_type
    FROM pg_class c
             JOIN pg_namespace n ON c.relnamespace = n.oid
             LEFT JOIN pg_partitioned_table p ON c.oid = p.partrelid
             LEFT JOIN pg_inherits i ON c.oid = i.inhrelid
    WHERE n.nspname = 'public'
          and (c.relname in ('measurement_y2024m01', 'measurement_y2024m02', 'measurement')
               OR inhparent in (
                    select oid
                    from pg_class
                    where
                        relname in ('measurement_y2024m01', 'measurement_y2024m02','measurement')
                )
          )
),
inherits AS (
    SELECT parent.relname AS parent_table, child.relname AS child_table, pg_inherits.inhrelid as inhrel_id
    FROM pg_inherits
    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
),
inherits_check AS (
    SELECT parent.relname                           AS parent_table,
           child.relname                            AS partition_table,
           conname                                  AS constraint_name,
           pg_catalog.pg_get_constraintdef(con.oid) AS constraint_definition
    FROM pg_class parent
    JOIN pg_inherits i ON parent.oid = i.inhparent
    JOIN pg_class child ON i.inhrelid = child.oid
    JOIN pg_constraint con ON child.oid = con.conrelid
),
partition_columns AS (
    SELECT a.attrelid AS partitioned_table_oid, a.attname AS partition_column
    FROM pg_attribute a
    JOIN pg_partitioned_table pt ON a.attrelid = pt.partrelid
    WHERE a.attnum = ANY (pt.partattrs)
),
partitions AS (
    SELECT pt.partrelid AS partitioned_table_oid, c.relname    AS partition_table,
           pt.partstrat AS partition_strategy
    FROM pg_partitioned_table pt
    JOIN pg_class c ON pt.partrelid = c.oid
),
partition_bounds AS (
    SELECT pt.partstrat                       AS partition_strategy,
        inhrelid                           AS table_oid,
        c.relname                          AS child_table,
        pg_get_expr(c.relpartbound, c.oid) AS partition_bound
    FROM pg_inherits i
    JOIN pg_class c ON i.inhrelid = c.oid
    LEFT JOIN pg_partitioned_table pt ON i.inhparent = pt.partrelid
)
SELECT a.table_name                                     AS table_name,
       a.table_type                                     AS table_type,
       CASE
           WHEN a.table_type = 'Parent Table' THEN 'Inherit'
           ELSE COALESCE(
                    CASE
                        WHEN p.partition_strategy = 'r' OR pb.partition_strategy = 'r' THEN 'Range'
                        WHEN p.partition_strategy = 'l' OR pb.partition_strategy = 'l' THEN 'List'
                        WHEN p.partition_strategy = 'h' OR pb.partition_strategy = 'h' THEN 'Hash'
                        WHEN a.table_type = 'Child Table' THEN 'Inherit'
                    ELSE 'Unknow' END,
                    ''
                ) END AS partition_type,
       CASE
           WHEN a.table_type = 'Partitioned Table'
               THEN pg_get_partkeydef(concat(a.schema_name, '.', a.table_name)::REGCLASS)
           WHEN i.parent_table IS NOT NULL THEN COALESCE(
                   pg_get_partkeydef(concat(a.schema_name, '.', i.parent_table)::REGCLASS),
                   COALESCE(ic.constraint_definition, ''))
           ELSE '' END                                  AS check_or_partition_rule,
       COALESCE(pb.partition_bound, '')                 AS partition_bound,
       COALESCE(i.parent_table, '')                     AS parent_table,
       COALESCE(p.partition_table, '')                  AS partition_table
FROM all_tables a
         LEFT JOIN inherits i ON a.table_name = i.child_table
         LEFT JOIN partitions p ON a.table_oid = p.partitioned_table_oid
         LEFT JOIN inherits_check ic ON a.table_name = ic.partition_table
         LEFT JOIN partition_bounds pb ON a.table_oid = pb.table_oid
WHERE a.table_type <> 'Regular Table'
ORDER BY a.schema_name, a.table_type DESC;