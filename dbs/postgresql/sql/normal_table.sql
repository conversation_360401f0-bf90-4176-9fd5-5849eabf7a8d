

drop table if exists measurement_normal;
drop table if exists measurement_normal1;
drop table if exists measurement_normal2;
drop table if exists measurement_normal3;
drop table if exists measurement_normal4;
drop table if exists measurement_normal5;
drop table if exists measurement_normal6;
drop sequence if exists measurement_normal_id_seq;
CREATE TABLE IF NOT EXISTS measurement_normal (
    id              int primary key ,
    city_id         int not null,  -- city id
    logdate         date not null, -- date
    peaktemp        int,           -- Maximum temperature of day
    has_sun         boolean,
    unitsales       int            -- sales amount
);
CREATE SEQUENCE IF NOT EXISTS measurement_normal_id_seq increment 1 minvalue 1 start with 1 cache 1;

CREATE TABLE IF NOT EXISTS city (
    id              int primary key ,
    name            varchar(100) not null ,
    location        varchar(500) not null
);
insert into city values (0, 'Beijing', 'China Beijing'),
                        (1, 'Jinan', 'China Jinan'),
                        (2, 'Shenzhen', 'China Shenzhen');
DROP TABLE IF EXISTS city;

DO $$
    declare var_count integer;
    declare var_seq_name varchar;
begin

    var_count := 0;
    var_seq_name := 'measurement_normal_id_seq';

    while var_count < 1000 loop
        insert into measurement_normal values
            (nextval(var_seq_name), 1, ('2024-06-05'), 33, true, var_count),
            (nextval(var_seq_name), 4, ('2024-06-06'), 33, true,var_count),
            (nextval(var_seq_name), 5, ('2024-06-06'), 33, true,var_count),
            (nextval(var_seq_name), 0, ('2024-06-06'), 33, true,var_count),
            (nextval(var_seq_name), 0, ('2024-06-06'), 33, true,var_count),
            (nextval(var_seq_name), 0, ('2024-06-06'), 33, false,var_count),
            (nextval(var_seq_name), 0, ('2024-06-06'), 33, false,var_count),
            (nextval(var_seq_name), 0, ('2024-06-06'), 33, false,var_count),
            (nextval(var_seq_name), 0, ('2024-06-06'), 33, false,var_count),
            (nextval(var_seq_name), 0, ('2024-06-06'), 33, false,var_count);
        commit;
        var_count := var_count+1;
    end loop;
end $$;

select * from measurement_normal order by id desc;
select * from measurement_normal where id = 30000;
select count(1) from measurement_normal;

select * from pg_tables where schemaname = 'public';
