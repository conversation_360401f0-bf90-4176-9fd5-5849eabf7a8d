-- 声明式分区表
CREATE TABLE IF NOT EXISTS measurement (
    id              int not null ,
    city_id         int not null,  -- city id
    logdate         date not null, -- date
    peaktemp        int,           -- Maximum temperature of day
    unitsales       int,            -- sales amount
    primary key (id, logdate)
) PARTITION BY range (logdate);
CREATE SEQUENCE IF NOT EXISTS measurement_id_seq increment 1 minvalue 1 start with 1 cache 1;

CREATE TABLE IF NOT EXISTS measurement_y202503 PARTITION OF measurement
    DEFAULT ;

CREATE TABLE IF NOT EXISTS measurement_y2024m01 PARTITION OF measurement
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE IF NOT EXISTS measurement_y2024m02 PARTITION OF measurement
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

CREATE TABLE IF NOT EXISTS measurement_y2024m03 PARTITION OF measurement
    FOR VALUES FROM ('2024-03-01') TO ('2024-04-01');

CREATE TABLE IF NOT EXISTS measurement_y2024m04 PARTITION OF measurement
    FOR VALUES FROM ('2024-04-01') TO ('2024-05-01');

CREATE TABLE IF NOT EXISTS measurement_y2024m05 PARTITION OF measurement
    FOR VALUES FROM ('2024-05-01') TO ('2024-06-01')
    WITH (parallel_workers = 4);

CREATE TABLE IF NOT EXISTS measurement_y2024m06 PARTITION OF measurement
    FOR VALUES FROM ('2024-06-01') TO ('2024-07-01')
    WITH (parallel_workers = 4);

CREATE TABLE IF NOT EXISTS measurement_y2024m07 PARTITION OF measurement
    FOR VALUES FROM ('2024-07-01') TO ('2024-08-01')
    WITH (parallel_workers = 4);

CREATE TABLE IF NOT EXISTS measurement_y2024m08 PARTITION OF measurement
    FOR VALUES FROM ('2024-08-01') TO ('2024-09-01');

CREATE TABLE IF NOT EXISTS measurement_y2024m09 PARTITION OF measurement
    FOR VALUES FROM ('2024-09-01') TO ('2024-10-01');

CREATE TABLE IF NOT EXISTS measurement_y2024m10 PARTITION OF measurement
    FOR VALUES FROM ('2024-10-01') TO ('2024-11-01');

CREATE TABLE IF NOT EXISTS measurement_y2024m11 PARTITION OF measurement
    FOR VALUES FROM ('2024-11-01') TO ('2024-12-01');

CREATE TABLE IF NOT EXISTS measurement_y2024m12 PARTITION OF measurement
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

CREATE INDEX ON measurement (logdate);

DROP TABLE IF EXISTS measurement_y2024m05;

SELECT
    inhrelid::regclass AS partition_name, inhparent::regclass
FROM
    pg_inherits
WHERE
    inhparent = 'measurement'::regclass;

-- 删除所有分区表
DO $$
DECLARE
    partition RECORD;
BEGIN
    FOR partition IN
        SELECT inhrelid::regclass AS partition_name
        FROM pg_inherits
        WHERE inhparent = 'measurement'::regclass
    LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || partition.partition_name || ' CASCADE';
    END LOOP;

    -- 删除主表
    EXECUTE 'DROP TABLE IF EXISTS measurement';
    EXECUTE 'DROP SEQUENCE IF EXISTS measurement_id_seq';

END $$;

DO $$
    declare var_count integer;
    declare var_month date;
begin

    var_count := 5;
--     var_month := '2024-01-01';
--     var_month := '2024-02-29';
--     var_month := '2024-03-01';
--     var_month := '2024-04-30';
--     var_month := '2024-05-09';
--     var_month := '2024-06-06';
--     var_month := '2024-07-06';
--     var_month := '2024-08-06';
--     var_month := '2024-09-06';
--     var_month := '2024-10-01';
--     var_month := '2024-11-30';
--     var_month := '2024-12-30';
    var_month := '2025-03-02';

    while var_count > 0 loop
        insert into measurement values
            (nextval('measurement_id_seq'), 0, (var_month), 25, var_count),
            (nextval('measurement_id_seq'), 0, (var_month), 26, var_count),
            (nextval('measurement_id_seq'), 0, (var_month), 27, var_count),
            (nextval('measurement_id_seq'), 0, (var_month), 28, var_count),
            (nextval('measurement_id_seq'), 0, (var_month), 29, var_count),
            (nextval('measurement_id_seq'), 0, (var_month), 30, var_count),
            (nextval('measurement_id_seq'), 0, (var_month), 31, var_count),
            (nextval('measurement_id_seq'), 0, (var_month), 32, var_count),
            (nextval('measurement_id_seq'), 0, (var_month), 33, var_count),
            (nextval('measurement_id_seq'), 0, (var_month), 34, var_count);
        commit;
        var_count := var_count-1;
    end loop;
end $$;

SELECT
    t.table_name AS "tableName",
    (SELECT
         COALESCE(
                 CAST(obj_description(c.oid, 'pg_class') AS varchar),
                 ''
             )
     FROM pg_class c
     WHERE c.relname = t.table_name
       AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = t.table_schema)
    ) AS "tableComment",
    CASE
        WHEN EXISTS (SELECT 1 FROM pg_partitioned_table pt WHERE pt.partrelid = c.oid) THEN 'Parent Table'
        WHEN EXISTS (SELECT 1 FROM pg_inherits i WHERE i.inhrelid = c.oid) THEN 'Child Table'
        ELSE 'Regular Table'
        END AS "tableType"
FROM
    information_schema.tables t
        JOIN
    pg_class c ON c.relname = t.table_name AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = t.table_schema)
        LEFT JOIN
    pg_inherits i ON i.inhrelid = c.oid
WHERE
        t.table_type = 'BASE TABLE'
  AND t.table_catalog = 'postgres'
  AND t.table_schema = 'public'
  ORDER BY
    CASE
        WHEN EXISTS (SELECT 1 FROM pg_partitioned_table pt WHERE pt.partrelid = c.oid)
            THEN c.oid
            ELSE i.inhparent
        END,
    t.table_name;

select count(1) from measurement;
update measurement set peaktemp = 35 where id = 314;
delete from measurement where id > 0;

select count(1) from measurement;
update measurement set peaktemp = 36 where id = 129;











-- 继承方式：允许分区子表有不同主表的列；允许多重继承；允许用户自定义的方式分区
CREATE TABLE measurement_inherits (
    id              int not null,
    city_id         int not null,
    logdate         date not null,
    peaktemp        int,
    unitsales       int
);
CREATE TABLE measurement_inherits_y2024m01 (
    CHECK ( logdate >= DATE '2024-01-01' AND logdate < DATE '2024-02-01')
) INHERITS (measurement_inherits);
CREATE TABLE measurement_inherits_y2024m02 (
    CHECK ( logdate >= DATE '2024-02-01' AND logdate < DATE '2024-03-01')
) INHERITS (measurement_inherits);
CREATE TABLE measurement_inherits_y2024m03 (
    CHECK ( logdate >= DATE '2024-03-01' AND logdate < DATE '2024-04-01')
) INHERITS (measurement_inherits);

-- 触发器方式将数据写入到子表
CREATE OR REPLACE FUNCTION measurement_inherits_insert_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF ( NEW.logdate >= DATE '2024-01-01' AND
         NEW.logdate < DATE '2024-02-01' ) THEN
        INSERT INTO measurement_inherits_y2024m01 VALUES (NEW.*);
    ELSIF ( NEW.logdate >= DATE '2024-02-01' AND
            NEW.logdate < DATE '2024-03-01' ) THEN
        INSERT INTO measurement_inherits_y2024m02 VALUES (NEW.*);
    ELSIF ( NEW.logdate >= DATE '2024-03-01' AND
            NEW.logdate < DATE '2024-04-01' ) THEN
        INSERT INTO measurement_inherits_y2024m03 VALUES (NEW.*);
    ELSE
        RAISE EXCEPTION 'Date out of range.  Fix the measurement_insert_trigger() function!';
    END IF;
    RETURN NULL;
END;
$$
LANGUAGE plpgsql;

CREATE TRIGGER insert_measurement_inherits_trigger
    BEFORE INSERT ON measurement_inherits
    FOR EACH ROW EXECUTE FUNCTION measurement_inherits_insert_trigger();

-- 设置规则方式，将数据写入到子表，不支持COPY方式写入数据
CREATE RULE measurement_inherits_insert_y2024m01 AS
ON INSERT TO measurement_inherits WHERE
    ( logdate >= DATE '2024-01-01' AND logdate < DATE '2024-02-01' )
DO INSTEAD
    INSERT INTO measurement_inherits_y2024m01 VALUES (NEW.*);
CREATE RULE measurement_inherits_insert_y2024m02 AS
ON INSERT TO measurement_inherits WHERE
    ( logdate >= DATE '2024-02-01' AND logdate < DATE '2024-03-01' )
DO INSTEAD
    INSERT INTO measurement_inherits_y2024m02 VALUES (NEW.*);
CREATE RULE measurement_inherits_insert_y2024m03 AS
ON INSERT TO measurement_inherits WHERE
    ( logdate >= DATE '2024-03-01' AND logdate < DATE '2024-04-01' )
DO INSTEAD
    INSERT INTO measurement_inherits_y2024m02 VALUES (NEW.*);

INSERT INTO measurement_inherits VALUES
                                     (nextval('measurement_id_seq'), 0, '2024-01-01', 30, 200),
                                     (nextval('measurement_id_seq'), 0, '2024-02-01', 30, 200),
                                     (nextval('measurement_id_seq'), 0, '2024-03-01', 30, 200);

SELECT * from measurement_inherits;
SELECT * from measurement_inherits_y2024m01;
SELECT * from measurement_inherits_y2024m02;
SELECT * from measurement_inherits_y2024m03;
DELETE FROM measurement_inherits WHERE id > 0;
DROP TRIGGER insert_measurement_inherits_trigger ON measurement_inherits;
DROP FUNCTION measurement_inherits_insert_trigger;
DROP TABLE IF EXISTS measurement_inherits_y2024m01;
DROP TABLE IF EXISTS measurement_inherits_y2024m02;
DROP TABLE IF EXISTS measurement_inherits_y2024m03;
DROP TABLE IF EXISTS measurement_inherits;

SELECT     parent.relname AS parent_table,     child.relname AS partition_table FROM     pg_inherits AS i         JOIN pg_class AS child ON i.inhrelid = child.oid         JOIN pg_class AS parent ON i.inhparent = parent.oid         JOIN pg_namespace AS nsp ON child.relnamespace = nsp.oid         JOIN pg_partitioned_table AS pt ON parent.oid = pt.partrelid WHERE    parent.relname IN ('measurement', 'measurement1', 'measurement2')    AND nsp.nspname = 'public' ORDER BY     parent.relname, child.relname;