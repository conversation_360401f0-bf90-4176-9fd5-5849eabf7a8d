version: "3.3"
services:
  redis-master:
    image: redis:6.0.16
    container_name: redis-master
    restart: always
    ports:
      - 6379:6379
    environment:
      TZ: "Asia/Shanghai"
    volumes:
      - ./redis/conf/master:/etc/redis
      - ./redis/data/master/:/data:Z
    command: ["redis-server", "/etc/redis/redis-v6.0.16.conf"]
    networks:
      redis-network:
        ipv4_address: ************

  redis-slave-1:
    image: redis:6.0.16
    container_name: redis-slave-1
    restart: always
    ports:
      - 6380:6379
    depends_on:
      - redis-master
    environment:
      TZ: "Asia/Shanghai"
    volumes:
      - ./redis/conf/slave1:/etc/redis
      - ./redis/data/slave1/:/data:Z
    command: ["redis-server", "/etc/redis/redis-v6.0.16.conf"]
    networks:
      redis-network:
        ipv4_address: ************

  redis-slave-2:
    image: redis:6.0.16
    container_name: redis-slave-2
    restart: always
    ports:
      - 6381:6379
    depends_on:
      - redis-master
    environment:
      TZ: "Asia/Shanghai"
    volumes:
      - ./redis/conf/slave2:/etc/redis
      - ./redis/data/slave2/:/data:Z
    command: ["redis-server", "/etc/redis/redis-v6.0.16.conf"]
    networks:
      redis-network:
        ipv4_address: ************

  redis-sentinel-1:
    image: redis:6.0.16
    container_name: redis-sentinel-1
    restart: always
    environment:
      TZ: "Asia/Shanghai"
    ports:
      - 25000:5000
    depends_on:
      - redis-master
    volumes:
      - ./sentinel/conf/sentinel1:/etc/redis
      - ./sentinel/data/sentinel1:/data:Z
    command: redis-server /etc/redis/redis-v6.0.16.conf --sentinel
    networks:
      redis-network:
        ipv4_address: ************

  redis-sentinel-2:
    image: redis:6.0.16
    container_name: redis-sentinel-2
    restart: always
    environment:
      TZ: "Asia/Shanghai"
    ports:
      - 25001:5000
    depends_on:
      - redis-master
    volumes:
      - ./sentinel/conf/sentinel2:/etc/redis
      - ./sentinel/data/sentinel2:/data:Z
    command: redis-server /etc/redis/redis-v6.0.16.conf --sentinel
    networks:
      redis-network:
        ipv4_address: ************

  redis-sentinel-3:
    image: redis:6.0.16
    container_name: redis-sentinel-3
    restart: always
    environment:
      TZ: "Asia/Shanghai"
    ports:
      - 25002:5000
    depends_on:
      - redis-master
    volumes:
      - ./sentinel/conf/sentinel3:/etc/redis
      - ./sentinel/data/sentinel3:/data:Z
    command: redis-server /etc/redis/redis-v6.0.16.conf --sentinel
    networks:
      redis-network:
        ipv4_address: ************

  dev-node:
    image: node
    container_name: redis-dev-node
    environment:
      DEBUG: ioredis:*
    profiles:
      - dev
    command:
      - sleep
      - 3000s
    volumes:
      - /Users/<USER>/workspace/gateway:/opt/gateway
    networks:
      redis-network:
        ipv4_address: ************

networks:
  redis-network:
    driver: bridge
    ipam:
      config:
        - subnet: ***********/16
          ip_range: ***********/24
          gateway: *************
#          aux_addresses:
#            redis-master: ************
#            redis-slave-1: ************
#            redis-slave-2: ************
#            redis-sentinel-1: ************
#            redis-sentinel-2: ************
#            redis-sentinel-3: ************