# 介绍

  基于 Redis 官方发布的镜像实现，快速启动启动 Redis 主从复制实例，可选启用哨兵模式。

## 各组件配置及端口

| 组件               | 端口                |
|------------------|-------------------|
| redis-master     | 6379 -> 6379/tcp  |
| redis-slave1     | 6380 -> 6379/tcp  |
| redis-slave2     | 6381 -> 6379/tcp  |
| redis-sentinel-1 | 25000 -> 5000/tcp |
| redis-sentinel-2 | 25001 -> 5000/tcp |
| redis-sentinel-3 | 25002 -> 5000/tcp |

## 主从复制密码
 **123456**

# 使用

## 哨兵模式

```shell
cd redis-sentinel

# 启动
docker compose up -d

# 停止
docker compose stop

# 删除实例
docker compose down

```

## 主从复制

```shell
# 启动
docker compose up -d redis-master redis-slave-1 redis-slave-2

# 停止
docker compose stop redis-master redis-slave-1 redis-slave-2

# 删除
docker compose down
```