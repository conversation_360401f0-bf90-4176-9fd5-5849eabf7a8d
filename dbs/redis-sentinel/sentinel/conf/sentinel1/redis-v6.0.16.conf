protected-mode no
daemonize no
port 5000
dir "/data"
sentinel myid 8549df36868205e33bbe0ddd8564fd7464a0e134
sentinel deny-scripts-reconfig yes
sentinel monitor mymaster ************ 6379 2
sentinel down-after-milliseconds mymaster 5000
sentinel failover-timeout mymaster 60000
sentinel auth-pass mymaster 123456
sentinel config-epoch mymaster 0
sentinel leader-epoch mymaster 0
# Generated by CONFIG REWRITE
user default on nopass ~* +@all
sentinel known-replica mymaster ************ 6379
sentinel known-replica mymaster ************ 6379
sentinel known-sentinel mymaster ************ 5000 b943d86c27f5f2bd5f2c9c3f1dd58e205cf65bd0
sentinel known-sentinel mymaster ************ 5000 293dc80ce1399c96dbc9d745faf88aa01bf8a96b
sentinel current-epoch 0
sentinel announce-ip "************"
sentinel announce-port 5000
