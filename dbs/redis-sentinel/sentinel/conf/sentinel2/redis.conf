protected-mode no
daemonize no
port 5000
dir "/data"
sentinel auth-pass mymaster 123456
sentinel announce-ip "************"
sentinel announce-port 5000
sentinel monitor mymaster ************ 6379 2
sentinel down-after-milliseconds mymaster 5000
sentinel failover-timeout mymaster 60000

sentinel deny-scripts-reconfig yes
sentinel resolve-hostnames no
sentinel announce-hostnames no

# Generated by CONFIG REWRITE
latency-tracking-info-percentiles 50 99 99.9
user default on nopass sanitize-payload ~* &* +@all
sentinel myid 8d81ee681d93273b16da42f215f8e9981298c4c4
sentinel config-epoch mymaster 0
sentinel leader-epoch mymaster 0
sentinel current-epoch 0

sentinel known-replica mymaster ************ 6379

sentinel known-replica mymaster ************ 6379

sentinel known-sentinel mymaster ************ 5000 f774ce38210f2b929a756e88b500a6df693dcaf8

sentinel known-sentinel mymaster ************ 5000 293dc80ce1399c96dbc9d745faf88aa01bf8a96b
