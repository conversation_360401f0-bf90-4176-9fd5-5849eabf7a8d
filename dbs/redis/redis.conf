# Redis 配置文件

# 基本配置
bind 0.0.0.0
protected-mode yes
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 通用配置
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# 快照配置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# 内存管理
maxmemory 256mb
maxmemory-policy allkeys-lru

# 延迟监控
latency-monitor-threshold 0

# 键空间通知
notify-keyspace-events Ex

# 安全配置
# requirepass yourpassword  # 取消注释并修改密码以启用密码保护
