
# Sybase 16.2
Based on nguoianphu/docker-sybase Needs about 30 seconds after start for correct initialization

## Environment Variables

### Guest user
Environment variable	Default value
SYBASE_USER	tester
SYBASE_PASSWORD	guest1234
SYBASE_DB	testdb

### Admin user
Environment variable	Default value
SYBASE_USER	sa
SYBASE_PASSWORD	myPassword


# ISQL
```shell
. /opt/sybase/SYBASE.sh
# cat /opt/sybase/interfaces
isql -Usa -PmyPassword -SMYSYBASE -Dtestdb -i/tmp/test.sql -o output.txt

```