package com.qz.hare.tools;

import com.google.api.gax.httpjson.InstantiatingHttpJsonChannelProvider;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.translate.v3.*;
import com.qz.hare.common.util.CommonUtils;
import com.qz.hare.common.util.JsonUtil;
import lombok.Builder;
import lombok.Data;

import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Set proxy for translate api
 * export JAVA_TOOL_OPTIONS="-Dhttps.proxyHost=localhost -Dhttps.proxyPort=1235"
 *
 * <AUTHOR>
 * create at 2024/12/22 09:23
 */
public class GeneratorI18nProperties {

    private static final String TRANSLATE_API_URL = "https://translate.googleapis.com/translate_a/single";

    // HOOK_INTERNAL_ERROR("HookInternalError", "Hook running error ."),
    private static final String ENUM_CONSTANT_PATTERN = "\\s+([A-Za-z0-9_]+)\\s*\\(\\s*\\\"([A-Za-z0-9_]+)\\\"\\s*,\\s*\\\"(.*)\\\"\\)\\s*,?;?"; // Enum constant pattern

    private static final String TARGET_INTERFACE = "(com\\.qz\\.hare\\.common\\.exception\\.)?ErrorCode";
    private static final List<String> excludeDirectory = List.of("hare-web", "target", "dist");
    private static boolean enableBackup = false;

    public static void main(String[] args) throws IOException {
        String basePath = System.getProperty("user.dir", "/Users/<USER>/workspace/hare");

        if (args.length > 0) {
            basePath = args[0];
        }

        File directory = new File(basePath);

        if (!directory.exists()) {
            System.out.println("指定目录不存在");
            return;
        }

        List<FileMetadata> matchedEnums = new ArrayList<>();
        scanDirectory(directory, matchedEnums);

        matchedEnums.forEach(GeneratorI18nProperties::generatorProperties);

    }

    /**
     * 将字符串编码为 ASCII 格式
     *
     * @param input 输入字符串
     * @return 转换为 ASCII 格式的字符串
     */
    public static String encodeToAscii(String input) {
        StringBuilder asciiBuilder = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (c > 127) { // 如果是非 ASCII 字符
                asciiBuilder.append(String.format("\\u%04x", (int) c));
            } else { // 保留 ASCII 字符
                asciiBuilder.append(c);
            }
        }
        return asciiBuilder.toString();
    }

    private static void generatorProperties(FileMetadata fileMetadata) {
        if (fileMetadata.codes == null || fileMetadata.codes.isEmpty())
            return;

        System.out.println("Generator for " + fileMetadata.getFile().getName().replaceAll(".java", ""));

        StringBuilder sb = new StringBuilder();
        fileMetadata.codes.forEach((s, s2) -> {
            sb.append(String.format("%s=%s", s, s2));
            sb.append('\r').append('\n');
        });
        String defaultPropertiesContent = sb.toString();

        writeToFile(new File(fileMetadata.resourceDir, "messages.properties"), defaultPropertiesContent);
        writeToFile(new File(fileMetadata.resourceDir, "messages_en.properties"), defaultPropertiesContent);

        try {
            //String zhPropertiesContent = translate(sb.toString(), "zh-CN");
            String zhPropertiesContent = translateV2(sb.toString(), "zh-CN");
            String[] lines = zhPropertiesContent.split("\n");

            StringBuilder replaceCode = new StringBuilder();
            List<String> keys = new ArrayList<>(fileMetadata.codes.keySet());
            for (int i = 0; i < keys.size(); i++) {
                replaceCode.append(String.format("%s=%s", keys.get(i), lines[i].substring(lines[i].indexOf("=") + 1)));
                replaceCode.append("\r\n");
            }

            String encodeText = encodeToAscii(replaceCode.toString());
            writeToFile(new File(fileMetadata.resourceDir, "messages_zh.properties"), encodeText);
            writeToFile(new File(fileMetadata.resourceDir, "messages_zh_CN.properties"), encodeText);
        } catch (IOException e) {
            System.err.println("  翻译失败: " + fileMetadata .getFile().getName() + ", " + e.getMessage());
        }
    }

    private static void writeToFile(File file, String content) {
        if (content == null) {
            return;
        }
        File backupFile = new File(file.getParentFile(), file.getName() + ".back" + System.currentTimeMillis());
        try {
            if (file.exists() && enableBackup) {
                System.out.println("  backup to " + backupFile.getAbsolutePath());
                Files.copy(file.toPath(), backupFile.toPath(),
                        StandardCopyOption.REPLACE_EXISTING, StandardCopyOption.COPY_ATTRIBUTES);
            }

            System.out.println("  write to " + file.getAbsolutePath());
            Files.writeString(file.toPath(), content, StandardCharsets.UTF_8,
                    StandardOpenOption.CREATE, StandardOpenOption.WRITE);

        } catch (IOException e) {
            System.err.println("  write file error: " + e.getMessage());
            if (backupFile.exists()) {
                CommonUtils.ignoreAnyError(() -> Files.copy(backupFile.toPath(), file.toPath(),
                        StandardCopyOption.REPLACE_EXISTING, StandardCopyOption.COPY_ATTRIBUTES));
            }
        }
    }

    private static String translateV2(String text, String lang) throws IOException {
        /*String projectId = "my-test-project-406012";
        String glossaryId = "hare-cloud-glossary";*/
        String sourceLanguage = "en";
        String targetLanguage = lang;

        GoogleCredentials credentials = GoogleCredentials.fromStream(
                GeneratorI18nProperties.class.getClassLoader()
                        .getResourceAsStream("my-test-project-406012-a8782117809b.json"))
                .createScoped("https://www.googleapis.com/auth/cloud-platform");

        TranslationServiceSettings settings = TranslationServiceSettings.newBuilder()
                .setCredentialsProvider(() -> credentials)
                .setTransportChannelProvider(InstantiatingHttpJsonChannelProvider.newBuilder()
                        /*.setHttpTransport(new NetHttpTransport.Builder()
                                .setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 1235)))
                                .build())*/
                        .build())
                .build();
        try (TranslationServiceClient client = TranslationServiceClient.create(settings)) {
            // 使用术语表
            /*GlossaryName glossaryName = GlossaryName.of(projectId, "us-central1", glossaryId);
            TranslateTextGlossaryConfig glossaryConfig = TranslateTextGlossaryConfig.newBuilder()
                    .setGlossary(glossaryName.toString())
                    .build();*/

            // 构建翻译请求
            TranslateTextRequest request = TranslateTextRequest.newBuilder()
                    .setParent("projects/my-test-project-406012/locations/global")
                    .addContents(text)
                    .setSourceLanguageCode(sourceLanguage)
                    .setTargetLanguageCode(targetLanguage)
                    //.setGlossaryConfig(glossaryConfig)
                    .setMimeType("text/plain")
                    .build();

            // 执行翻译
            TranslateTextResponse response = client.translateText(request);

            // 获取术语翻译结果
            // response.getGlossaryTranslationsList().stream().map(Translation::getTranslatedText).collect(Collectors.joining());
            return response.getTranslationsList().stream().map(Translation::getTranslatedText).collect(Collectors.joining());
        }

    }

    private static String translate(String text, String lang) throws IOException {
        String apiUrl = String.format("%s?client=gtx&sl=en&tl=%s&dt=t&q=%s", TRANSLATE_API_URL, lang, URLEncoder.encode(text, StandardCharsets.UTF_8));
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 1235));
        HttpURLConnection connection = (HttpURLConnection) new URL(apiUrl).openConnection(proxy);
        connection.setRequestMethod("GET");

        BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        StringBuilder response = new StringBuilder();
        String inputLine;
        while ((inputLine = in.readLine()) != null) {
            response.append(inputLine);
        }
        in.close();

        // 提取翻译结果
        String translation = response.toString();
        String translatedText = translation.replaceAll("^\\[\\[\"|\"\\]\\]\\]$", "");

        StringBuilder sb = new StringBuilder();
        List result = JsonUtil.parseJson(translatedText, List.class);

        if (result != null && !result.isEmpty() && result.get(0) instanceof List fl) {
            fl.forEach(l -> {
                if (l instanceof List i) {
                    sb.append(i.get(0));
                }
            });
        }
        sb.append("\r\n");

        return sb.toString();
    }

    /**
     * 递归扫描目录中的 .java 文件
     */
    private static void scanDirectory(File directory, List<FileMetadata> matchedEnums) throws IOException {
        for (File file : Objects.requireNonNull(directory.listFiles())) {
            if (file.isDirectory() && !excludeDirectory.contains(file.getName())) {
                scanDirectory(file, matchedEnums);
            } else if (file.getName().endsWith(".java")) {
                try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                    StringBuilder content = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        content.append(line).append("\n");
                    }

                    // 检查是否是符合条件的枚举类
                    if (isTargetEnum(content.toString())) {
                        String filePath = file.getAbsolutePath();
                        FileMetadata fileMetadata = FileMetadata.builder()
                                .file(file)
                                .resourceDir(new File(filePath.substring(0, filePath.lastIndexOf("/java")), "resources"))
                                .codes(parseEnumConstants(content.toString()))
                                .build();
                        matchedEnums.add(fileMetadata);
                    }
                }
            }
        }
    }

    /**
     * 检查是否是目标枚举类
     */
    private static boolean isTargetEnum(String content) {
        // 正则匹配 `enum` 和实现的接口
        Pattern enumPattern = Pattern.compile("enum\\s+\\w+\\s+.*implements\\s+" + TARGET_INTERFACE);
        Matcher matcher = enumPattern.matcher(content);
        return matcher.find();
    }

    /**
     * 解析枚举常量
     */
    private static LinkedHashMap<String, String> parseEnumConstants(String content) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        // 提取枚举常量
        Matcher constantMatcher = Pattern.compile(ENUM_CONSTANT_PATTERN).matcher(content);

        while (constantMatcher.find()) {
            String code = constantMatcher.group(2); // Enum constant name
            String description = constantMatcher.group(3).replace("\"", ""); // String.format description
            map.put(code, description);
        }
        return map;
    }

    @Builder
    @Data
    public static class FileMetadata {
        File file;
        File resourceDir;
        LinkedHashMap<String, String> codes;
    }

}
