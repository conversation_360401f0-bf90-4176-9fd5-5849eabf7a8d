package com.qz.hare.common.async;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * create at 2024/11/28 14:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsyncTaskHolder<T> {
    private String id;
    private Long startTime;
    private Long expiredTime;

    private T task;
    private BiConsumer<T, Long> expiredHandler;

    public long duration() {
        return System.currentTimeMillis() - this.startTime;
    }

}
