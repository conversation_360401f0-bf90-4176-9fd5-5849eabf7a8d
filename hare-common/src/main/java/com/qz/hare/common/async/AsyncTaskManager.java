package com.qz.hare.common.async;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.qz.hare.common.util.CommonUtils.ignoreAnyError;
import static com.qz.hare.common.util.IDUtils.generatorUUID;

/**
 * <AUTHOR>
 * create at 2024/11/28 14:35
 */
@Slf4j
public class AsyncTaskManager {

    @Getter
    private static AsyncTaskManager instance = new AsyncTaskManager();

    private static Map<String, AsyncTaskHolder> tasks = new ConcurrentHashMap<>();

    private AsyncTaskManager() {
        AsyncUtil.scheduleAtFixedRate(this::checkExpired, 10, 5, TimeUnit.SECONDS);
    }

    private void checkExpired() {
        List<AsyncTaskHolder> expiredTasks = tasks.values().stream().filter(v -> v.getExpiredTime() < System.currentTimeMillis()).toList();
        expiredTasks.forEach(v -> ignoreAnyError(() -> {
            AsyncTaskHolder t = tasks.remove(v.getId());
            if (t != null && t.getExpiredHandler() != null) {
                t.getExpiredHandler().accept(v.getTask(), v.duration());
            }
        }));
    }

    public <T> String registerTask(String taskId, T task, BiConsumer<T, Long> expiredHandler) {
        return registerTask(AsyncTaskHolder.builder().id(taskId).task(task)
                .expiredHandler((BiConsumer) expiredHandler).build());
    }
    public <T> String registerTask(AsyncTaskHolder<T> asyncTaskHolder) {
        if (asyncTaskHolder == null)
            return null;
        if (StringUtils.isBlank(asyncTaskHolder.getId()))
            asyncTaskHolder.setId(generatorUUID());
        asyncTaskHolder.setStartTime(System.currentTimeMillis());
        tasks.putIfAbsent(asyncTaskHolder.getId(), asyncTaskHolder);
        return asyncTaskHolder.getId();
    }

    public <T> T getAndRemoveTask(String taskId) {
        AsyncTaskHolder holder = tasks.remove(taskId);
        if (holder != null) {
            return (T) holder.getTask();
        }
        return null;
    }

    public Map<String, Object> getStatus() {
        return tasks.values().stream().collect(Collectors.toMap(AsyncTaskHolder::getId, e -> {
            Map<String, Object> tmp = new HashMap<>();
            tmp.put("startTime", e.getStartTime());
            tmp.put("expiredTime", e.getExpiredTime());
            tmp.put("id", e.getId());
            return tmp;
        }));
    }

    public void destroy() {
        tasks.values().forEach(t -> t.setExpiredTime(0L));
        checkExpired();
        log.debug("{} shutdown complete", this.getClass().getSimpleName());
    }
}
