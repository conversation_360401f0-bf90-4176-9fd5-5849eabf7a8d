package com.qz.hare.common.async;

import com.qz.hare.common.util.SystemUtils;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * create at 2022/9/16 下午8:09
 */
public class AsyncUtil {

    private static ScheduledExecutorService executorService;

    private static ScheduledExecutorService getExecutorService() {
        if (executorService == null) {
            executorService = Executors.newScheduledThreadPool(Integer.min(SystemUtils.cpus, 16));
        }
        return executorService;
    }

    public static <T> CompletableFuture<T> async(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(supplier);
    }

    public static Future<?> asyncRun(Runnable runnable) {
        return getExecutorService().submit(runnable);
    }

    public static void schedule(Runnable runnable, long delay, TimeUnit timeUnit) {
        getExecutorService().schedule(runnable, delay, timeUnit);
    }

    public static void scheduleAtFixedRate(Runnable runnable, long delay, long peiod, TimeUnit timeUnit) {
        getExecutorService().scheduleAtFixedRate(runnable, delay, peiod, timeUnit);
    }

    public static List<String>  getTaskStatus() {
        List<String> status = new ArrayList<>();
        if ( executorService instanceof ThreadPoolExecutor threadPoolExecutor) {
            BlockingQueue<Runnable> queue = threadPoolExecutor.getQueue();
            queue.forEach(t -> {
                if (t instanceof RunnableScheduledFuture<?> future) {
                    status.add(String.format("%s, periodic %s, delay %ds",
                            future, future.isPeriodic(),
                            future.getDelay(TimeUnit.SECONDS)));
                }
            });
        }
        return status;
    }

    public void destroy() {
        getExecutorService().shutdownNow();
        LoggerFactory.getLogger(AsyncUtil.class).info("Shutdown Async executor service");
    }
}
