package com.qz.hare.common.constants;

/**
 * <AUTHOR>
 * create at 2023/3/6 上午9:39
 */
public class PluginConstants {

    public static final String PLUGIN_FILENAME = "plugin.json";
    public static final String PLUGIN_FILENAME_YAML = "plugin.yaml";
    public static final String PLUGIN_FILENAME_YML = "plugin.yml";

    public static final String PLUGIN_NAME = "name";
    public static final String PLUGIN_VERSION = "version";
    public static final String PLUGIN_LATEST = "latest";
    public static final String PLUGIN_DESCRIPTION = "description";
    public static final String PLUGIN_DATASOURCE_TYPE = "datasourceType";
    public static final String PLUGIN_TYPE = "pluginType";
    public static final String PLUGIN_SCOPE_TYPE = "scopeType";
    public static final String PLUGIN_DEVELOPER = "developer";
    public static final String PLUGIN_EMAIL = "email";
    public static final String PLUGIN_ICON = "icon";
    public static final String PLUGIN_TAGS = "tags";

    public static final String PLUGIN_PATH = "path";
    public static final String PLUGIN_URL = "url";
    public static final String PLUGIN_CLASS = "class";
    public static final String PLUGIN_CLASSPATH = "classpath";
    public static final String PLUGIN_CONNECTOR_FORM_CONFIG = "connectorFormConfig";
    public static final String PLUGIN_CONNECTOR_JSON_SCHEMA = "connectorJsonSchema";
    public static final String PLUGIN_PARAMETER_FORM_CONFIG = "parameterFormConfig";
    public static final String PLUGIN_PARAMETER_JSON_SCHEMA = "parameterJsonSchema";

    public static final String PLUGIN_PARAMETER_INCLUDE_TABLE = "includeTable";

    public static final String CONNECTION_TYPE = "connectionType";

    private PluginConstants() {}
}
