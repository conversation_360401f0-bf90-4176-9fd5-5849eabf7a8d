package com.qz.hare.common.event;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * create at 2023/2/12 下午11:41
 */
@Getter
@Setter
@SuperBuilder
@ToString
public class AddListenerEvent implements Event {

    public static final String EVENT_NAME = "ADD_LISTENER";

    private String eventName;
    private Listener<? extends Event> listener;
    private boolean prepend;

    @Override
    public String getName() {
        return EVENT_NAME;
    }
}
