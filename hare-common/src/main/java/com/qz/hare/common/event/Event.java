package com.qz.hare.common.event;

/**
 * <AUTHOR>
 * create at 2023/2/12 下午10:42
 */
public interface Event {

    /**
     * Get event name
     * @return return the event name
     */
    String getName();

    /**
     * Determine whether this event needs to be processed asynchronously
     * @return return true if asynchronous or else return false
     */
    default boolean isAsync() {
        return false;
    }
}
