package com.qz.hare.common.event;

import com.qz.hare.common.async.AsyncUtil;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * public class A extends EventEmitter{
 *     // extends EventEmitter
 * }
 * A a = new A();
 * a.on("start", new Listener());
 *
 * <AUTHOR>
 * create at 2023/2/12 下午10:15
 */
public class EventEmitter {

    private final Map<String, List<Listener<Event>>> listeners;

    public EventEmitter () {
        listeners = new ConcurrentHashMap<>();
    }

    public void destroy() {
        removeAllListener(null);
    }

    public void addListener(String eventName, Listener<Event> listener, int index) {
        List<Listener<Event>> listeners = this.listeners.computeIfAbsent(eventName, key -> new CopyOnWriteArrayList<>());
        index = Math.max(index, 0);
        index = Math.min(index, listeners.size());
        listeners.add(index, listener);
        this.emit(AddListenerEvent.builder()
                .eventName(eventName).listener(listener).prepend(false).build());
    }

    public void addListener(String eventName, Listener<Event> listener, boolean prepend) {
        addListener(eventName, listener, prepend ? 0 : Integer.MAX_VALUE);
    }

    /**
     * Adds a listener to the event emitter.
     * @param eventName event name
     * @param listener listener for event name
     */
    public EventEmitter addListener(String eventName, Listener<Event> listener) {
        addListener(eventName, listener, false);
        return this;
    }

    /**
     * Removes the specified `listener` from the listeners array.
     * @param eventName event name
     * @param listener listener for event name
     * @return
     */
    public EventEmitter removeListener(String eventName, Listener<Event> listener) {

        List<Listener<Event>> listeners = this.listeners.get(eventName);
        if (listeners == null) {
            return this;
        }
        if (listener != null) {
            listeners.removeIf(l -> l == listener);
            // ignore result for remove listener and emit 'removeListener'
            this.emit(RemoveListenerEvent.builder().eventName(eventName).listener(listener).build());
        } else {
            this.listeners.remove(eventName);
            listeners.forEach(l -> this.emit(RemoveListenerEvent.builder().eventName(eventName).listener(l).build()));
        }

        return this;
    }

    /**
     * Removes all listeners, or those of the specified eventName.
     * It is bad practice to remove listeners added elsewhere in the code,
     * particularly when the EventEmitter instance was created by some other component or module (e.g. sockets or file streams).
     *
     * @param eventName event name
     * @return
     */
    public EventEmitter removeAllListener(String eventName) {
        if ( eventName == null) {
            eventNames().forEach(type -> removeListener(type, null));
        } else {
            removeListener(eventName, null);
        }
        return this;
    }

    /**
     * Alias for addListener(eventName, listener).
     * @param eventName event name
     * @param listener listener for event name
     * @return
     */
    public EventEmitter on(String eventName, Listener<Event> listener) {
        return addListener(eventName, listener);
    }

    /**
     * Adds a one-time listener function for the event named eventName.
     * The next time eventName is triggered, this listener is removed and then invoked.
     * @param eventName event name
     * @param listener listener for event name
     * @return
     */
    public EventEmitter once(String eventName, Listener<Event> listener) {
        return addListener(eventName, new Listener<>() {
            @Override
            public void on(Event event) {
                off(eventName, this);
                listener.on(event);
            }
        });
    }

    /**
     * Alias for removeListener(eventName, listener).
     * @param eventName event name
     * @param listener optional,
     * @return
     */
    public EventEmitter off(String eventName, Listener<Event> listener) {
        return this.removeListener(eventName, listener);
    }

    /**
     * Synchronously calls each of the listeners registered for the event named eventName,
     * in the order they were registered, passing the supplied arguments to each.
     * @param event source event
     */
    public EventEmitter emit(Event event) {
        if (event == null)
            return this;
        return emit(event.getName(), event);
    }
    public EventEmitter emit(String eventName, Event event) {
        eventName = Optional.ofNullable(eventName).orElse(
                Optional.ofNullable(event).map(Event::getName).orElse(null)
        );
        if (eventName == null)
            return this;

        List<Listener<Event>> listeners = this.listeners.get(eventName);
        if (listeners == null) {
            return this;
        } else {
            listeners.forEach(l -> {
                if (event != null && event.isAsync())
                    AsyncUtil.asyncRun(() -> l.on(event));
                else
                    l.on(event);
            });
        }
        return this;
    }

    /**
     * Adds the listener function to the beginning of the listeners array for the event named eventName.
     * No checks are made to see if the listener has already been added.
     * Multiple calls passing the same combination of eventName and listener will result in the listener being added,
     * and called, multiple times.
     * @param eventName event name
     * @param listener listener for event name
     * @return
     */
    public EventEmitter prependListener(String eventName, Listener<Event> listener) {
        addListener(eventName, listener, true);
        return this;
    }

    /**
     * Adds a one-time listener function for the event named eventName to the beginning of the listeners array.
     * The next time eventName is triggered, this listener is removed, and then invoked.
     * @param eventName event name
     * @param listener listener for event name
     * @return
     */
    public EventEmitter prependOnceListener(String eventName, Listener<Event> listener) {
        prependListener(eventName, new Listener<>() {
            @Override
            public void on(Event event) {
                off(eventName, this);
                listener.on(event);
            }
        });
        return this;
    }

    /**
     * Returns an array listing the events for which the emitter has registered listeners.
     * @return String list
     */
    public Collection<String> eventNames() {
        return new ArrayList<>(this.listeners.keySet());
    }

    /**
     * Returns the number of listeners listening to the event named 'eventName'.
     * @param eventName event name.
     * @return
     */
    public int listenerCount(String eventName) {
        if (eventName != null) {
            List<Listener<Event>> listeners = this.listeners.get(eventName);
            return Optional.ofNullable(listeners).map(List::size).orElse(0);
        } else {
            return this.listeners.values().stream().mapToInt(List::size).sum();
        }
    }

    /**
     * Returns a copy of the array of listeners for the event named eventName.
     * @param eventName event name
     * @return
     */
    public Optional<List<Listener<Event>>> listeners(String eventName) {
        return Optional.ofNullable(this.listeners.get(eventName));
    }
}
