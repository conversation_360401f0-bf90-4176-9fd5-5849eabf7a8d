package com.qz.hare.common.exception;

/**
 * <AUTHOR>
 * create at 2022/8/11 上午12:15
 */
public enum CommonErrorCode implements ErrorCode {

    CONFIG_ERROR("ConfigError", "There is an error message in the configuration file you provided, please check your configuration: {0}"),
    CONVERT_NOT_SUPPORT("ConvertNotSupport", "Synchronized data has business dirty data, data type conversion error."),
    CONVERT_OVER_FLOW("ConvertOverFlow", "Synchronized data has business dirty data, and data type conversion overflows."),
    RETRY_FAIL("RetryFail", "Method calls multiple times still fail ."),
    RUNTIME_ERROR("RuntimeError", "Uncaught exception: {0}"),
    HOOK_INTERNAL_ERROR("HookInternalError", "Hook running error ."),
    SHUT_DOWN_TASK("ShutDownTask", "Task received shutdown command to prepare for failover"),
    WAIT_TIME_EXCEED("WaitTimeExceed", "wait time out of bounds"),
    TASK_HUNG_EXPIRED("TaskHungExpired", "Task hung, Expired"),

    EXECUTOR_SERVICE_NOT_INIT("ExecutorServiceNotInit", "Please execute the init() method to init executor service first"),

    PLUGIN_LOAD_ERROR("PluginLoadError", "Load plugin {0}({1}) error: {2}"),
    PLUGIN_NOT_INIT("PluginNotInit", "Please execute the init() method to load the plugin first"),
    PLUGIN_DOWNLOAD_ERROR("PluginDownloadError", "Download plugin error {0} from {1}."),
    PLUGIN_INIT_ERROR("PluginInitError", "There is an error in the initialization of the plugin. This problem is usually caused by an incorrect installation."),
    PLUGIN_NOT_FOUND("PluginNotFound", "Not found plugin {0}. This problem is usually caused by an incorrect installation of . Please contact operation and maintenance for assistance."),
    PLUGIN_MISSING_CONFIG("PluginMissingConfig", "Plugin config error, missing config {0}"),
    PLUGIN_MISSING_ICON("PluginMissingIcon", "Plugin config error, missing icon {0} or icon file not exists."),
    PLUGIN_NOT_FOUND_FILE("PluginExecuteError", "Plugin config error, not found file {0} in plugin"),
    PLUGIN_IMPLEMENT_ERROR("PluginImplementError", "Plugin implementation error, please contact the plugin developer for assistance."),
    PLUGIN_EXECUTE_ERROR("PluginExecuteError", "Execution plug-in method error, please contact the plug-in developer to help solve it."),
    PLUGIN_PARAMS_VALIDATE_ERROR("PluginParamsValidateError", "Plugin params validation error {0}"),

    SECRET_ERROR("SecretError", "Fails to process encryption and decryption."),

    REQUIRED_VALUE("RequiredValue", "Missing required value for {0}"),
    ILLEGAL_ARGUMENT("IllegalArgument", "Illegal argument {0}={1}"),
    NOT_SUPPORT_TYPE("NotSupportType", "Unsupported type"),

    ARGUMENT_ERROR("ArgumentError", "The engine is running incorrectly. This problem is usually caused by an internal programming error. Please contact the development team to solve it."),

    UNSUPPORTED_OPERATION("UnsupportedOperation", "The requested operation is not supported."),

    CONNECTOR_NOT_FOUND_DRIVER_CLASS("NotFoundDriverClass", "Not found driver class"),
    CONNECTOR_CONFIG_NO_PROTOCOL("ConnectorConfigNoProtocol", "Connector not config protocol."),
    CONNECTOR_CONFIG_NO_HOST("ConnectorConfigNoHost", "Connector not config host."),
    CONNECTOR_CONFIG_NO_DATABASE("ConnectorConfigNoDatabase", "Connector not config database."),
    CONNECTOR_CONFIG_ERROR("ConnectorConfigError", "Connector config error: {0}"),

    UNSUPPORTED_TYPE("UnsupportedType", "Unsupported data type"),
    PARSE_VERSION_FAILED("ParseVersionFailed", "Parse version failed: {0}"),

    UNSUPPORTED_COMPRESS_FORMAT("UnsupportedCompressFormat", "Not support compress by {0}, you can use zip."),
    JOB_UNKNOWN_ERROR("JobUnknownError", "An unknown exception occurred while running a task {0} {1}"),
    ;

    private final String code;

    private final String describe;

    CommonErrorCode(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDescription() {
        return this.describe;
    }

}
