package com.qz.hare.common.exception;

/**
 * <AUTHOR>
 * create at 2022/8/11 上午12:15
 */
public interface ErrorCode {

	/**
	 * Error code
	 * @return
	 */
	String getCode();

	/**
	 * Error description
	 * @return
	 */
	String getDescription();

	/**
	 * Must be impl like:
	 * 	return String.format("Code: [%s], Description: [%s]", getCode(), getDescription());
	 *
	 * @return
	 */
	String toString();

	default String toStringFormat() {
		return String.format("Code:[%s], Describe:[%s]", getCode(),
				getDescription());
	}
}
