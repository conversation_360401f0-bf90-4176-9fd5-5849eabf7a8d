package com.qz.hare.common.exception;

import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.TypeFilter;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ErrorCode工具类，用于扫描和缓存所有实现ErrorCode接口的枚举类
 *
 * <AUTHOR>
 * create at 2025/8/31 11:34
 */
public final class ErrorCodeUtils {

    private ErrorCodeUtils() {}

    /**
     * 错误码缓存映射，key为错误码，value为ErrorCode实例
     */
    private static final Map<String, ErrorCode> errorCodeMap = new ConcurrentHashMap<>();

    /**
     * 初始化标志
     */
    private static volatile boolean initialized = false;

    /**
     * 初始化方法，扫描所有实现ErrorCode接口的枚举类
     */
    private static synchronized void init() {
        if (initialized) {
            return;
        }

        try {
            // 使用Spring的ClassPathScanningCandidateComponentProvider扫描classpath
            ClassPathScanningCandidateComponentProvider scanner =
                    new ClassPathScanningCandidateComponentProvider(false);

            // 添加过滤器，只扫描实现ErrorCode接口的类
            scanner.addIncludeFilter((metadataReader, metadataReaderFactory) -> {
                // 跳过接口
                if (metadataReader.getClassMetadata().isInterface()) {
                    return false;
                }

                // 检查是否实现了ErrorCode接口
                for (String interfaceName : metadataReader.getClassMetadata().getInterfaceNames()) {
                    if (interfaceName.equals(ErrorCode.class.getName())) {
                        return true;
                    }
                }
                return false;
            });

            // 扫描com.qz.hare包下的所有类
            Set<BeanDefinition> candidates = scanner.findCandidateComponents("com.qz.hare");

            for (BeanDefinition candidate : candidates) {
                try {
                    Class<?> clazz = Class.forName(candidate.getBeanClassName());
                    // 只处理枚举类
                    if (clazz.isEnum() && ErrorCode.class.isAssignableFrom(clazz)) {
                        processEnumClass(clazz.asSubclass(ErrorCode.class));
                    }
                } catch (ClassNotFoundException e) {
                    System.err.println("Failed to load class: " + candidate.getBeanClassName() + ", error: " + e.getMessage());
                }
            }

            initialized = true;
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize ErrorCodeUtils", e);
        }
    }

    /**
     * 处理枚举类，将所有枚举值添加到缓存中
     *
     * @param enumClass 枚举类
     */
    @SuppressWarnings("unchecked")
    private static void processEnumClass(Class<? extends ErrorCode> enumClass) {
        try {
            // 获取枚举的所有常量
            Object[] enumConstants = enumClass.getEnumConstants();
            if (enumConstants != null) {
                for (Object enumConstant : enumConstants) {
                    ErrorCode errorCode = (ErrorCode) enumConstant;
                    errorCodeMap.put(errorCode.getCode(), errorCode);
                }
            }
        } catch (Exception e) {
            // 记录错误但不中断整个初始化过程
            System.err.println("Failed to process enum class: " + enumClass.getName() + ", error: " + e.getMessage());
        }
    }

    /**
     * 根据错误码获取对应的ErrorCode实例
     *
     * @param errorCode 错误码
     * @return ErrorCode实例，如果未找到则返回null
     */
    public static ErrorCode getErrorCode(String errorCode) {
        if (!initialized) {
            init();
        }
        return errorCodeMap.get(errorCode);
    }

    /**
     * 获取所有已缓存的错误码
     *
     * @return 错误码映射的副本
     */
    public static Map<String, ErrorCode> getAllErrorCodes() {
        if (!initialized) {
            init();
        }
        return new HashMap<>(errorCodeMap);
    }

    /**
     * 清空缓存并重新初始化（主要用于测试）
     */
    static void reset() {
        errorCodeMap.clear();
        initialized = false;
    }
}
