package com.qz.hare.common.exception;

import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * create at 2025/8/31 11:34
 */
public final class ErrorCodeUtils {
    private ErrorCodeUtils() {}
    private static Map<String, ErrorCode> errorCodeMap = new HashMap<>();
    private static void init() {
        // ErrorCodeUtils.class.getClassLoader()
        ClassPathScanningCandidateComponentProvider scanner =
                new ClassPathScanningCandidateComponentProvider(false);
        scanner.addIncludeFilter((metadataReader, metadataReaderFactory) -> {
            if (metadataReader.getClassMetadata().isInterface()) {
                return false;
            }
            for (String interfaceName : metadataReader.getClassMetadata().getInterfaceNames()) {
                if (interfaceName.equals(ErrorCode.class.getName())) {
                    return true;
                }
            }
            return false;
        });
        Set<BeanDefinition> candidate = scanner.findCandidateComponents("com.qz.hare");
        for (BeanDefinition beanDefinition : candidate) {
            System.out.println(beanDefinition);
        }

    }

    public static ErrorCode getErrorCode(String errorCode) {
        if (errorCodeMap.isEmpty()) {
            init();
        }
        return errorCodeMap.get(errorCode);
    }
}
