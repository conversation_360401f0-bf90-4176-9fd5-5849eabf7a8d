package com.qz.hare.common.exception;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * <AUTHOR>
 * create at 2022/8/11 上午12:15
 */
public class ExceptionTracker {
    public static final int STRING_BUFFER = 8192;

    private ExceptionTracker() {
    }

    public static String trace(Throwable ex) {
        StringWriter sw = new StringWriter(STRING_BUFFER);
        PrintWriter pw = new PrintWriter(sw);
        ex.printStackTrace(pw);
        return sw.toString();
    }

    private static String getCauseMessage(Throwable e) {
        if ( e != null) {
            StringBuilder sb = new StringBuilder();
            sb.append(e.getMessage());
            if (e.getCause() != null) {
                String cause = getCauseMessage(e.getCause());
                if ( cause != null) {
                    sb.append('(').append(cause).append(')');
                }
            }
            return sb.toString();
        }
        return null;
    }

    public static String traceWithoutStack(Throwable e) {
        return getCauseMessage(e);
    }
}
