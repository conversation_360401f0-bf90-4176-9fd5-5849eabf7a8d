package com.qz.hare.common.exception;

import com.qz.hare.common.util.LanguageUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;

/**
 * <AUTHOR>
 * create at 2022/8/11 上午12:15
 */
public class HareException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    @Getter
    private final ErrorCode errorCode;
    @Getter
    private final Object[] args;
    @Setter
    @Getter
    private Locale locale;

    public HareException(ErrorCode errorCode, Object ...args) {
        super();
        this.errorCode = errorCode;
        this.args = args;
    }

    public HareException(ErrorCode errorCode, Throwable throwable, Object ...args) {
        super(null, throwable);
        this.errorCode = errorCode;
        this.args = args;
    }

    public HareException lang(String lang) {
        this.locale = LanguageUtil.toLocale(lang);
        return this;
    }

    @Override
    public String getMessage() {
        if (locale == null)
            locale = Locale.getDefault();
        var m = LanguageUtil.getMessage(locale, errorCode, args);
        if (StringUtils.isNotBlank(m))
            return m;
        return String.format(errorCode.getDescription(), args);
    }
}
