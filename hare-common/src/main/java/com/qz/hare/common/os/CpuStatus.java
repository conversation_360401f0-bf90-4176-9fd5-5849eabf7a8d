package com.qz.hare.common.os;


import lombok.Data;

/**
 * <AUTHOR>
 * create at 2022/8/19 下午6:26
 */
@Data
public class CpuStatus {

    Double maxCpuLoad;
    Double minCpuLoad;
    Double curCpuLoad;
    Double averageCpu;

    public void setMaxMinCpuLoad(double curCpu) {
        this.curCpuLoad = curCpu;
        if (maxCpuLoad == null || maxCpuLoad < curCpu) {
            maxCpuLoad = curCpu;
        }

        if (minCpuLoad == null || minCpuLoad > curCpu) {
            minCpuLoad = curCpu;
        }

        averageCpu = (maxCpuLoad + curCpuLoad + minCpuLoad)/3;
    }
}
