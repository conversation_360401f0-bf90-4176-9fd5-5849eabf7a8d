package com.qz.hare.common.os;

import lombok.Data;

/**
 * <AUTHOR>
 * create at 2022/8/19 下午6:23
 */
@Data
public class GCStatus {
    String name;
    Long maxDeltaGCCount;
    Long minDeltaGCCount;
    Long curDeltaGCCount;
    Long totalGCCount;
    Long maxDeltaGCTime;
    Long minDeltaGCTime;
    Long curDeltaGCTime;
    Long totalGCTime;

    public void setCurTotalGcCount(long curTotalGcCount) {
        this.curDeltaGCCount = curTotalGcCount - (totalGCCount == null ? 0 : totalGCCount);
        this.totalGCCount = curTotalGcCount;

        if (maxDeltaGCCount == null || maxDeltaGCCount < curDeltaGCCount) {
            maxDeltaGCCount = curDeltaGCCount;
        }

        if (minDeltaGCCount == null || minDeltaGCCount > curDeltaGCCount) {
            minDeltaGCCount = curDeltaGCCount;
        }
    }

    public void setCurTotalGcTime(long curTotalGcTime) {
        this.curDeltaGCTime = curTotalGcTime - (totalGCTime == null ? 0 : totalGCTime);
        this.totalGCTime = curTotalGcTime;

        if (maxDeltaGCTime == null || maxDeltaGCTime < curDeltaGCTime) {
            maxDeltaGCTime = curDeltaGCTime;
        }

        if (minDeltaGCTime == null || minDeltaGCTime > curDeltaGCTime) {
            minDeltaGCTime = curDeltaGCTime;
        }
    }
}
