package com.qz.hare.common.os;

import lombok.Data;

/**
 * <AUTHOR>
 * create at 2022/8/19 下午6:22
 */
@Data
public class MemoryStatus {
    String name;
    Long initSize;
    Long maxSize;
    Long commitedSize;
    Long usedSize;
    Float percent;
    Long maxUsedSize;
    Float maxPercent;

    public void setMaxMinUsedSize(long curUsedSize) {
        if (maxUsedSize == null || maxUsedSize < curUsedSize) {
            maxUsedSize = curUsedSize;
        }
        this.usedSize = curUsedSize;
    }

    public void setMaxMinPercent(float curPercent) {
        if (maxPercent == null || maxPercent < curPercent) {
            maxPercent = curPercent;
        }
        this.percent = curPercent;
    }
}
