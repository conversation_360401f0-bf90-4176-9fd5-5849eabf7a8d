package com.qz.hare.common.os;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2022/8/19 下午6:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmInfo {

    // static info
    private OSInfo os;
    private String jvm;
    private String fileEncoding; // fileEncoding
    private String defaultCharset; // defaultCharset
    private String workerDir;
    private String tmpDir;
    private String user;
    private String version;
    private String gitBranch;
    private String gitCommitId;
    private String build;
    private Long processId;

    // dynamic info
    private String hostname;
    private Long sysTime;
    private Long uptime;
    private Long cpuTime;
    private Double sysLoadAverage;

    private OSStatus osStatus;
    private Double cpuUsage;
    private Double memUsage;
    private List<DiskStatus> disk;

    private Map<String, GCStatus> gc;
    private Map<String, MemoryStatus> memory;
}
