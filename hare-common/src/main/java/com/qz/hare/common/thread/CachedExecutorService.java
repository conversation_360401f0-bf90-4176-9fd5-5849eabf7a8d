package com.qz.hare.common.thread;

import com.qz.hare.common.base.LifeCycle;
import com.qz.hare.common.exception.CommonErrorCode;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.common.util.SystemUtils;

import java.util.concurrent.*;

import static com.qz.hare.common.thread.ThreadPriority.DEFAULT_THREAD_PRIORITY;

/**
 * <AUTHOR>
 * create at 2024/10/14 20:39
 */
public class CachedExecutorService implements LifeCycle {
    private String threadNamePrefix;
    private String threadGroupName;
    private CustomizableThreadFactory threadFactory;
    private ExecutorService threadPool;

    public CachedExecutorService() {}

    public CachedExecutorService(String threadNamePrefix, String threadGroupName) {
        this.threadNamePrefix = threadNamePrefix;
        this.threadGroupName = threadGroupName;
    }

    @Override
    public void init() {
        if (threadFactory == null) {
            threadFactory = new CustomizableThreadFactory();
            threadFactory.setThreadNamePrefix(getThreadNamePrefix());
            threadFactory.setThreadGroupName(getThreadGroupName());
            threadFactory.setUncaughtExceptionHandler(getUncaughtExceptionHandler());
            threadFactory.setThreadPriority(getThreadPriority());
            threadFactory.setDaemon(isDaemon());
            threadPool = createExecutorService(threadFactory);
        }
    }

    public ExecutorService getExecutorService() {
        if (threadPool == null) {
            throw new HareException(CommonErrorCode.EXECUTOR_SERVICE_NOT_INIT, CommonErrorCode.EXECUTOR_SERVICE_NOT_INIT.getDescription());
        }
        return threadPool;
    }

    protected ExecutorService createExecutorService(ThreadFactory threadFactory) {
        return new ThreadPoolExecutor(Integer.min(4, SystemUtils.cpus), Integer.MAX_VALUE,
                30L, TimeUnit.SECONDS, new SynchronousQueue<>(true), threadFactory);
    }

    protected String getThreadNamePrefix() {
        return threadNamePrefix;
    }
    protected String getThreadGroupName() {
        return threadGroupName;
    }

    protected Thread.UncaughtExceptionHandler getUncaughtExceptionHandler() {
        return null;
    }
    protected int getThreadPriority() {
        return DEFAULT_THREAD_PRIORITY;
    }
    protected boolean isDaemon() {
        return false;
    }

    @Override
    public void destroy() {
        if (threadPool != null) {
            threadPool.shutdown();
        }
        threadPool = null;
        threadFactory = null;
    }
}
