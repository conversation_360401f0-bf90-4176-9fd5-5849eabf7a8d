package com.qz.hare.common.thread;

import com.qz.hare.common.util.ClassUtils;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * create at 2024/12/21 09:25
 */
public class CustomizableThreadCreator implements Serializable {

    @Getter
    private String threadNamePrefix;
    @Getter
    @Setter
    private int threadPriority = 5;
    @Getter
    @Setter
    private boolean daemon = false;
    @Getter
    private ThreadGroup threadGroup;
    @Getter
    @Setter
    private Thread.UncaughtExceptionHandler uncaughtExceptionHandler;

    private final AtomicInteger threadCount = new AtomicInteger();

    public CustomizableThreadCreator() {
        this.threadNamePrefix = this.getDefaultThreadNamePrefix();
    }

    public CustomizableThreadCreator(String threadNamePrefix) {
        this.threadNamePrefix = threadNamePrefix != null ? threadNamePrefix : this.getDefaultThreadNamePrefix();
    }

    public void setThreadNamePrefix(String threadNamePrefix) {
        this.threadNamePrefix = threadNamePrefix != null ? threadNamePrefix : this.getDefaultThreadNamePrefix();
    }

    public void setThreadGroupName(String name) {
        if (name != null)
            this.threadGroup = new ThreadGroup(name);
    }

    public Thread createThread(Runnable runnable) {
        Thread thread = new Thread(this.getThreadGroup(), runnable, this.nextThreadName());
        thread.setPriority(this.getThreadPriority());
        thread.setDaemon(this.isDaemon());
        if (uncaughtExceptionHandler != null)
            thread.setUncaughtExceptionHandler(uncaughtExceptionHandler);
        return thread;
    }

    protected String nextThreadName() {
        String var10000 = this.getThreadNamePrefix();
        return var10000 + this.threadCount.incrementAndGet();
    }

    protected String getDefaultThreadNamePrefix() {
        return ClassUtils.getShortName(this.getClass()) + "-";
    }
}
