package com.qz.hare.common.thread;

/**
 * <AUTHOR>
 * create at 2023/5/28 17:36
 */
public final class ThreadPriority {

    /**
     * 控制任务线程优先级
     */
    public static final int TASK_CONTROLLER_THREAD_PRIORITY = 9;

    public static final int TASK_SCHEDULE_THREAD_PRIORITY = 8;
    /**
     * 节点工作线程优先级
     */
    public static final int NODE_WORKER_THREAD_PRIORITY = 7;

    public static final int DEFAULT_THREAD_PRIORITY = Thread.NORM_PRIORITY;

    private ThreadPriority() {}
}
