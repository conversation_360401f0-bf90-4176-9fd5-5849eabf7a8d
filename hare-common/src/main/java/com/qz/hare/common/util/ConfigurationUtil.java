package com.qz.hare.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

public class ConfigurationUtil {
    private ConfigurationUtil () {}
    private static final List<String> SENSITIVE_KEYS = Arrays.asList(
            "accessKey", "securityKey", "securityToken", "password");

    public static Configuration desensitization(Configuration origin) {
        return desensitization(origin, SENSITIVE_KEYS);
    }

    /**
     * Desensitization config content, match all keys is end with in sensitiveKeys, replace the value to *
     * @param origin config
     * @param sensitiveKeys sensitive keys
     * @return desensitized config
     */
    public static Configuration desensitization(Configuration origin, List<String> sensitiveKeys) {
        if (origin == null) {
            return null;
        }
        Configuration configuration = origin.copy();
        Set<String> keys = configuration.getKeys();
        for (final String key : keys) {
            boolean isSensitive = false;
            for (String sensitiveKey : sensitiveKeys) {
                if (StringUtils.endsWithIgnoreCase(key, sensitiveKey)) {
                    isSensitive = true;
                    break;
                }
            }

            if (isSensitive && configuration.get(key) instanceof String) {
                configuration.set(key, "*****");
            }

        }
        return configuration;
    }
}
