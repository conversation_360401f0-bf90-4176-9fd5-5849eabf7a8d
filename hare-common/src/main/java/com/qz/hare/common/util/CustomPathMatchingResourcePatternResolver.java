package com.qz.hare.common.util;

import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.net.URL;
import java.util.Set;

/**
 * <AUTHOR>
 * create at 2024/12/31 08:13
 */
public class CustomPathMatchingResourcePatternResolver extends PathMatchingResourcePatternResolver {
    private final URL[] urls;

    public CustomPathMatchingResourcePatternResolver(URL[] urls) {
        this.urls = urls;
    }

    @Override
    protected Set<Resource> doFindAllClassPathResources(String path) throws IOException {
        Set<Resource> result = super.doFindAllClassPathResources(path);
        if (!StringUtils.hasLength(path) && this.urls != null) {
            for (URL url : urls) {
                if (ResourceUtils.isJarFileURL(url)) {
                    UrlResource jarResource = (ResourceUtils.URL_PROTOCOL_JAR.equals(url.getProtocol()) ?
                            new UrlResource(url) :
                            new UrlResource(ResourceUtils.JAR_URL_PREFIX + url + ResourceUtils.JAR_URL_SEPARATOR));
                    if (jarResource.exists()) {
                        result.add(jarResource);
                    }
                } else {
                    result.add(convertClassLoaderURL(url));
                }
            }
        }
        return result;
    }
}
