package com.qz.hare.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * create at 2024/12/22 20:45
 */
@Slf4j
public class CustomResourceBundleMessageSource extends ReloadableResourceBundleMessageSource {

    protected List<String> defaultBasename = new ArrayList<>();
    private Map<String, Set<String>> pluginMessageSourceMap = new HashMap<>();
    private ReentrantLock lock = new ReentrantLock();

    private void setBasenames() {
        List<String> merged = Stream.concat(
                defaultBasename.stream(),
                        pluginMessageSourceMap.values().stream().flatMap(Set::stream)).toList();
        this.setBasenames(merged.toArray(new String[0]));
    }

    public void loadMessageDefaultResource() throws IOException {
        if (CollectionUtils.isEmpty(defaultBasename)) {
            defaultBasename.addAll(findMessageSource(null));
        }
        log.info("Load messages from {}", defaultBasename);
        this.setBasenames();
    }

    public void loadPluginMessageSource(String pluginKey, URL[] pluginClasspath) {
        Validate.notNull(pluginKey, "Plugin key cannot be null");
        Validate.notNull(pluginClasspath, "Plugin classpath cannot be null");
        try {
            Set<String> pluginMessageSource = findMessageSource(pluginClasspath);
            pluginMessageSource.removeIf(defaultBasename::contains);

            lock.lock();
            pluginMessageSourceMap.put(pluginKey, pluginMessageSource);
            this.setBasenames();
        } catch (IOException e) {
            log.warn("Load message source from plugin classpath failed {}", e.getMessage());
        } finally {
            if (lock.isLocked())
                lock.unlock();
        }
    }

    public void removePluginMessageSource(String pluginKey) {
        Validate.notNull(pluginKey, "Plugin key cannot be null");
        try {
            lock.lock();
            pluginMessageSourceMap.remove(pluginKey);
            this.setBasenames();
        } finally {
            if (lock.isLocked())
                lock.unlock();
        }
    }

    private Set<String> findMessageSource(URL[] classpath) throws IOException {

        PathMatchingResourcePatternResolver resolver = classpath == null ? new PathMatchingResourcePatternResolver()
                : new CustomPathMatchingResourcePatternResolver(classpath);

        Resource[] resources = resolver.getResources("classpath*:messages*.properties");

        Set<String> basename = new HashSet<>();
        for (Resource resource : resources) {
            String url = resource.getURI().toString();
            basename.add(url.substring(0, url.lastIndexOf("messages") + 8));
        }
        return basename;
    }
}
