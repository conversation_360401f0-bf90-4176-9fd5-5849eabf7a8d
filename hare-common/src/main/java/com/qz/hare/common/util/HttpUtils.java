package com.qz.hare.common.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.ByteBuffer;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.List;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Flow;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.LongConsumer;

/**
 * <AUTHOR>
 * create at 2024/12/18 16:19
 */
public class HttpUtils {
    public static final String CONTENT_LENGTH = "Content-Length";
    public static final String HTTP_PROTOCOL = "http:";
    public static final String HTTPS_PROTOCOL = "https:";

    public static boolean isHttpOrHttpsUrl(String url) {
        return isHttpUrl(url) || isHttpsUrl(url);
    }
    public static boolean isHttpUrl(String url) {
        return url != null && url.startsWith(HTTP_PROTOCOL);
    }
    public static boolean isHttpsUrl(String url) {
        return url != null && url.startsWith(HTTPS_PROTOCOL);
    }

    public static Path downloadFile(URI uri, Path filePath, LongConsumer progressConsumer) throws IOException, InterruptedException {
        HttpClient httpClient = HttpClient.newHttpClient();
        HttpRequest httpRequest = HttpRequest.newBuilder().GET().uri(uri).build();

        AtomicLong totalLength = new AtomicLong(-1);
        AtomicInteger progress = new AtomicInteger(0);
        HttpResponse<InputStream> response = httpClient.send(httpRequest,
            HttpUtils.ofProgressBodyHandler(received -> {
                if (progressConsumer != null && totalLength.get() != -1) {
                    int tmp = BigDecimal.valueOf(received)
                            .divide(BigDecimal.valueOf(totalLength.get()), 2, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100))
                            .intValue();
                    if (tmp != progress.get()){
                        progress.set(tmp);
                        progressConsumer.accept(progress.get());
                    }
                }
            },
                    HttpResponse.BodyHandlers.ofInputStream()
            ));
        totalLength.set(response.headers().firstValueAsLong(CONTENT_LENGTH).orElse(-1));
        try (InputStream inputStream = response.body();
             OutputStream outputStream = Files.newOutputStream(filePath,
                     StandardOpenOption.WRITE, StandardOpenOption.CREATE);) {
            inputStream.transferTo(outputStream);
        }
        return filePath;
    }

    public static <T> HttpResponse.BodyHandler<T> ofProgressBodyHandler(LongConsumer receivedLengthConsumer,
                                                                        HttpResponse.BodyHandler<T> bodyHandler) {
        return responseInfo -> new HttpResponse.BodySubscriber<T>() {
            private HttpResponse.BodySubscriber<T> delegateSubscriber = bodyHandler.apply(responseInfo);
            private long receivedBytes = 0;
            @Override
            public CompletionStage<T> getBody() {
                return delegateSubscriber.getBody();
            }

            @Override
            public void onSubscribe(Flow.Subscription subscription) {
                delegateSubscriber.onSubscribe(subscription);
            }

            @Override
            public void onNext(List<ByteBuffer> item) {
                receivedBytes += item.stream().mapToLong(ByteBuffer::capacity).sum();
                receivedLengthConsumer.accept(receivedBytes);
                delegateSubscriber.onNext(item);
            }

            @Override
            public void onError(Throwable throwable) {
                delegateSubscriber.onError(throwable);
            }

            @Override
            public void onComplete() {
                receivedLengthConsumer.accept(receivedBytes);
                delegateSubscriber.onComplete();
            }
        };
    }

    public static void uploadFile(String url) {

    }
}
