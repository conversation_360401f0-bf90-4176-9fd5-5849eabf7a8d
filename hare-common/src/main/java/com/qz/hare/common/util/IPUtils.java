package com.qz.hare.common.util;

import java.math.BigInteger;
import java.net.*;
import java.util.*;

/**
 * <AUTHOR>
 * create at 2023/12/29 12:14
 */
public class IPUtils {
    private static String hostname;
    private IPUtils() {

    }

    public static String getHostname() {

        if (hostname != null)
            return hostname;

        try {
            hostname = InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            hostname = BigInteger.valueOf(new Date().getTime()).toString(32);
        }

        return hostname;
    }

    /**
     * Check the host:port is available
     * @param host
     * @param port
     * @return
     */
    public static String checkPortAvailable(String host, int port){
        try (Socket socket = new Socket(host, port)) {
            return null;
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    /**
     * 获取本地网卡IP地址列表
     * 只包含物理网卡地址，不包含本地环回地址，不包含虚拟网卡地址
     * 支持IPv4和IPv6地址
     * @return 本地网卡IP地址列表，如果获取失败返回空列表
     */
    public static List<String> getLocalNetworkIPs() {
        List<String> ipList = new ArrayList<>();

        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();

                // 过滤条件：排除环回地址、虚拟网卡、未启用的网卡、点对点连接
                if (networkInterface.isLoopback() ||
                    networkInterface.isVirtual() ||
                    !networkInterface.isUp() ||
                    networkInterface.isPointToPoint()) {
                    continue;
                }

                // 获取该网络接口的所有IP地址
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();

                    // 获取IPv4和IPv6地址，排除环回地址和链路本地地址
                    if (!address.isLoopbackAddress() &&
                        !address.isLinkLocalAddress() &&
                        !address.isMulticastAddress()) { // 排除组播地址

                        String hostAddress = address.getHostAddress();

                        // 对于IPv6地址，去掉可能的区域标识符（如%eth0）
                        if (hostAddress.contains("%")) {
                            hostAddress = hostAddress.substring(0, hostAddress.indexOf("%"));
                        }

                        ipList.add(hostAddress);
                    }
                }
            }
        } catch (SocketException e) {
            // 如果获取网络接口失败，返回空列表
            return Collections.emptyList();
        }

        return ipList;
    }

    /**
     * 简单的IPv4地址格式验证
     */
    public static boolean isValidIPv4(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 简单的IPv6地址格式验证
     */
    public static boolean isValidIPv6(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        if (!ip.contains(":")) {
            return false;
        }

        String[] parts = ip.split(":");
        if (parts.length < 3 || parts.length > 8) {
            return false;
        }

        try {
            for (String part : parts) {
                if (!part.isEmpty()) {
                    Integer.parseInt(part, 16);
                    if (part.length() > 4) {
                        return false;
                    }
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证IP地址格式（支持IPv4和IPv6）
     */
    public static boolean isValidIP(String ip) {
        return isValidIPv4(ip) || isValidIPv6(ip);
    }
}
