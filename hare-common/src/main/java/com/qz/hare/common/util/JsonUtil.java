package com.qz.hare.common.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.core.util.DefaultIndenter;
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 * create at 2022/8/11 下午5:52
 */
@Slf4j
public class JsonUtil {

    private static ObjectMapper objectMapper;

    private static ObjectMapper buildObjectMapper() {

        if (objectMapper == null) {
            objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            DefaultPrettyPrinter prettyPrinter = new DefaultPrettyPrinter();
            prettyPrinter.withArrayIndenter(new DefaultIndenter());
            objectMapper.setDefaultPrettyPrinter(prettyPrinter);
        }
        return objectMapper;
    }

    public static void registerModule(com.fasterxml.jackson.databind.Module module) {
        buildObjectMapper();
        objectMapper.registerModule(module);
    }

    public static <T> T parseJson(String json, TypeReference<T> typeReference) {
        if (json == null) {
            return null;
        } else {
            ObjectMapper objectMapper = buildObjectMapper();

            try {
                return objectMapper.readValue(json, typeReference);
            } catch (JsonProcessingException var4) {
                log.error("Parse json failed", var4) ;
                return null;
            }
        }
    }

    public static <T> T parseJson(String json, Class<T> clazz) {
        if (json == null) {
            return null;
        } else {
            ObjectMapper objectMapper = buildObjectMapper();

            try {
                return objectMapper.readValue(json, clazz);
            } catch (JsonProcessingException var4) {
                log.error("Parse json failed", var4) ;
                return null;
            }
        }
    }

    public static String toJson(Object object) {
        return toJson(object, false);
    }

    public static String toJson(Object object, boolean pretty) {
        if (object == null) {
            return "";
        } else {
            ObjectMapper objectMapper = buildObjectMapper();

            try {
                if (pretty) {
                    return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
                }
                return objectMapper.writeValueAsString(object);
            } catch (JsonProcessingException var3) {
                log.error("To json failed", var3) ;
                return "";
            }
        }
    }

    public static JsonNode convertToJsonNode(Map<String, Object> jsonMap) {
        if (jsonMap == null) {
            return objectMapper.createObjectNode();
        } else {
            return objectMapper.valueToTree(JsonNode.class);
        }
    }
}
