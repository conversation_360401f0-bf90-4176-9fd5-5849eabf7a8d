package com.qz.hare.common.util;

import com.qz.hare.common.exception.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.LocaleUtils;

import java.net.URL;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Locale;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import static com.qz.hare.common.util.CommonUtils.ignoreAnyError;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/9/13 2:42 下午
 * @description
 */
@Slf4j
public class LanguageUtil {

	private static ReentrantLock loadMessageLock = new ReentrantLock();
	private LanguageUtil() {}

	private static CustomResourceBundleMessageSource customResourceBundleMessageSource;

	private static CustomResourceBundleMessageSource getMessageSource() {
		if (customResourceBundleMessageSource == null) {
			loadMessageLock.lock();
			try {
				if (customResourceBundleMessageSource == null) {
					customResourceBundleMessageSource = new CustomResourceBundleMessageSource();
					customResourceBundleMessageSource.loadMessageDefaultResource();
				}
				return customResourceBundleMessageSource;
			} catch (Exception e) {
				log.error("Load message source failed {}", e.getMessage(), e);
			} finally {
				loadMessageLock.unlock();
			}
		}
		return customResourceBundleMessageSource;
	}

	public static void loadPluginMessageSource(String pluginKey, URL[] classpath) {
		Optional.ofNullable(getMessageSource()).ifPresent(m -> m.loadPluginMessageSource(pluginKey, classpath));
    }

	public static void removePluginMessageSource(String pluginKey) {
		Optional.ofNullable(getMessageSource()).ifPresent(m -> m.removePluginMessageSource(pluginKey));
	}

	public static String getMessage(Locale locale, ErrorCode errorCode, Object... params) {
		return getMessage(locale, errorCode.getCode(), errorCode.getClass().getClassLoader(), params);
	}

	/**
	 * 根据指定的 {@code resourceId} 获取消息，并使用指定的参数 {@code params} 对消息信息进行格式化
	 * @param locale 本地环境
	 * @param resourceId 资源ID
	 * @param params 格式化参数
	 * @return 格式化后的消息信息
	 * */
	public static String getMessage(Locale locale, String resourceId, Object... params) {
		return getMessage(locale, resourceId, null, params);
	}
	public static String getMessage(Locale locale, String resourceId, ClassLoader classLoader, Object... params) {
		String msg = getStringOrNull(getResourceBundle(locale, classLoader), resourceId);
		if (msg == null) {
			try {
				msg = Optional.ofNullable(getMessageSource()).map(m -> m.getMessage(resourceId, params, locale)).orElse(null);
			} catch (Exception e) {
				log.debug(e.getMessage());
			}
		}
		if (msg == null) {
			if (params != null && params.length > 0){
				return Arrays.stream(params).map(Object::toString).collect(Collectors.joining(","));
			}
			return resourceId;
		} if(params != null && params.length > 0){
			MessageFormat messageFormat = createMessageFormat(msg, locale);
			return messageFormat.format(params);
		} else
			return msg;

	}

	public static ResourceBundle getResourceBundle(Locale locale, ClassLoader classLoader) {
		if (locale == null)
			locale = Locale.CHINA;
		if (classLoader != null) {
			return ResourceBundle.getBundle("messages", locale, classLoader);
		} else {
			return ResourceBundle.getBundle("messages", locale);
		}
	}

	protected static String getStringOrNull(ResourceBundle bundle, String key){
		try{
			return bundle.getString(key);
		} catch (Exception e){
			log.debug(e.getMessage());
			return null;
		}
	}

	/**
	 * Create a MessageFormat for the given message and Locale.
	 * @param msg the message to insert a MessageFormat for
	 * @param locale the Locale to insert a MessageFormat for
	 * @return the MessageFormat instance
	 */
	public static MessageFormat createMessageFormat(String msg, Locale locale) {
		return new MessageFormat((msg != null ? msg : ""), locale);
	}

	public static Locale toLocale(String lang) {
		AtomicReference<Locale> locale = new AtomicReference<>();
		ignoreAnyError( () -> locale.set(Locale.forLanguageTag(lang)));
		if (locale.get() == null)
			ignoreAnyError( () -> locale.set(LocaleUtils.toLocale(lang)));
		if (locale.get() == null)
			locale.set(Locale.getDefault());
		return locale.get();
	}
}
