package com.qz.hare.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.CharacterIterator;
import java.text.StringCharacterIterator;

/**
 * <AUTHOR>
 * create at 2022/8/15 下午5:58
 */
public class NumberUtil {

    public static String humanReadable(long number, int decimal, String orderUnit) {
        return humanReadable(BigDecimal.valueOf(number), decimal, orderUnit);
    }
    public static String humanReadable(double number, int decimal, String orderUnit) {
        return humanReadable(BigDecimal.valueOf(number), decimal, orderUnit);
    }
    public static String humanReadable(BigDecimal number, int decimal, String orderUnit) {

        CharacterIterator ci = new StringCharacterIterator(orderUnit);
        BigDecimal result = number;
        BigDecimal decimalBig = BigDecimal.valueOf(decimal);
        char unit;
        while (true) {
            unit = ci.next();
            if (unit != StringCharacterIterator.DONE && result.compareTo(decimalBig) >= 0) {
                result = result.divide(decimalBig, RoundingMode.HALF_UP);
            } else {
                break;
            }
        }
        unit = ci.previous();

        result = result.setScale(2, RoundingMode.HALF_UP);
        return String.format("%.1f%c", result.doubleValue(), unit);
    }

}
