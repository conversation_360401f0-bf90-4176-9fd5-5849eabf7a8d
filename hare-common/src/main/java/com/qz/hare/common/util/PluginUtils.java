package com.qz.hare.common.util;

import com.qz.hare.common.constants.PluginConstants;
import com.qz.hare.common.exception.CommonErrorCode;
import com.qz.hare.common.exception.HareException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.LongConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2024/7/2 22:24
 */
@Slf4j
public class PluginUtils {

    private PluginUtils() {}

    public static File downloadPlugin(String downloadUrl, String pluginDirectory, DownloadProgress downloadProgress) {
        try {

            if (log.isDebugEnabled()) {
                log.debug("Downloading plugin {}", downloadUrl);
            }
            File pluginDir = new File(pluginDirectory);
            if (!pluginDir.exists()) {
                boolean result = pluginDir.mkdirs();
                if (!result) {
                    throw new HareException(CommonErrorCode.PLUGIN_DOWNLOAD_ERROR,
                            String.format("Create plugin directory %s failed", pluginDirectory));
                }
            }

            URL url = new URL(downloadUrl);
            String filename = url.getFile();
            if (filename.lastIndexOf("?") > 0) {
                filename = filename.substring(0, filename.lastIndexOf("?"));
            }
            File pluginFile = new File(pluginDirectory, filename);
            if (!pluginFile.exists()) {
                Path downloadFilePath = pluginFile.toPath();
                LongConsumer progress = null;
                if (downloadProgress != null) {
                    var tmp = filename;
                    progress = p -> downloadProgress.accept(tmp, downloadFilePath, p);
                }
                Path path = HttpUtils.downloadFile(url.toURI(), downloadFilePath, progress);
                pluginFile = path.toFile();
            }
            return unpackPlugin(filename, pluginFile);
        } catch (HareException e) {
            throw e;
        } catch (Exception e) {
            log.error("Download plugin failed {}", e.getMessage(), e);
            throw new HareException(CommonErrorCode.PLUGIN_DOWNLOAD_ERROR, e, e.getMessage(), downloadUrl);
        }
    }

    private static File unpackPlugin(String filename, File pluginFile) throws IOException {
        String extensions = FilenameUtils.getExtension(filename);
        if (!FilenameUtils.isExtension(filename.toLowerCase(), "zip")) {
            log.warn("Does not support {} compression packages: {}", FilenameUtils.getExtension(filename), filename);
            throw new HareException(CommonErrorCode.UNSUPPORTED_COMPRESS_FORMAT);
        }
        File unpackPluginDir = new File(pluginFile.getParentFile(),
                filename.substring(0, filename.length() - extensions.length() - 1));

        if (log.isDebugEnabled()) {
            log.debug("Unpacking plugin {} into {}", filename, unpackPluginDir.getAbsolutePath());
        }

        if (!unpackPluginDir.exists()) {
            CompressUtils.unzip(pluginFile);
        }
        return unpackPluginDir;
    }

    /**
     * 查找指定插件的配置
     * @param includePlugins
     * @param pluginDir
     * @return
     */
    public static Configuration loadPluginConfig(List<Configuration> includePlugins, String pluginDir) {
        return loadPluginConfig(includePlugins, pluginDir, null);
    }
    public static Configuration loadPluginConfig(List<Configuration> includePlugins, String pluginDir, DownloadProgress downloadProgress) {

        // download by url
        includePlugins.stream()
                .filter(p -> {
                    String url = p.getString(PluginConstants.PLUGIN_URL);
                    return url != null && HttpUtils.isHttpOrHttpsUrl(url);
                })
                .parallel()
                .forEach(plugin -> downloadPlugin(plugin.getString(PluginConstants.PLUGIN_URL), pluginDir, downloadProgress));

        Map<String, Configuration> needLoadPlugins = includePlugins.stream()
                .collect(Collectors.toMap(PluginUtils::generatePluginKey, plugin -> plugin, (p1, p2) -> p1));

        Configuration pluginConfigs = Configuration.newDefault();
        List<String> loadedPlugin = new ArrayList<>();

        Consumer<File> foundPlugin = (File pluginConfigFile) -> {
            Configuration pluginConfig = Configuration.from(pluginConfigFile);
            String pluginKey = generatePluginKey(pluginConfig);

            if (loadedPlugin.contains(pluginKey)) {
                return;
            }

            // ignore non-job dependency plugin
            // include all plugin when needLoadPlugins is empty.
            if (needLoadPlugins.isEmpty() || needLoadPlugins.containsKey(pluginKey)) {
                pluginConfig.set(PluginConstants.PLUGIN_PATH, pluginConfigFile.getParentFile().getAbsolutePath());
                loadReferenceConfig(pluginConfig, pluginConfig.getString(PluginConstants.PLUGIN_PATH));

                pluginConfigs.set(pluginKey, pluginConfig);
                if (!loadedPlugin.contains(pluginKey)) {
                    loadedPlugin.add(pluginKey);
                }
            }
        };

        includePlugins.stream().filter(p -> {
            String path = p.getString(PluginConstants.PLUGIN_PATH);
            if (StringUtils.isNotBlank(path)) {
                log.debug("Loading plugin from path: {}", path);
                boolean result = new File(path).exists();
                if (!result) {
                    log.warn("Plugin path does not exist: {}", path);
                }
                return result;
            }
            return false;
        }).forEach(p -> scanPlugin(new File(p.getString(PluginConstants.PLUGIN_PATH)), foundPlugin));

        scanPlugin(new File(pluginDir), foundPlugin);

        if (!includePlugins.isEmpty() && includePlugins.size() != loadedPlugin.size()) {

            String notFoundPlugins = needLoadPlugins.keySet().stream().filter(k -> !loadedPlugin.contains(k))
                    .map(needLoadPlugins::get).map(p ->
                            String.format("%s %s", getPluginName(p),
                                    getPluginVersion(p))).collect(Collectors.joining(","));
            throw new HareException(CommonErrorCode.PLUGIN_NOT_FOUND, notFoundPlugins);
        }

        return pluginConfigs;
    }

    /**
     * 遍历插件目录，查找所有的插件
     * @param pluginHome
     * @param foundPlugin
     */
    public static void scanPlugin(File pluginHome, Consumer<File> foundPlugin) {
        List<File> directors = new ArrayList<>();
        boolean isPlugin = false;
        File[] pluginFiles = pluginHome.listFiles();
        if (pluginFiles != null) {
            for (File f : pluginFiles) {
                String fileName = f.getName();
                if (f.isFile() && (fileName.equalsIgnoreCase(PluginConstants.PLUGIN_FILENAME)
                        || fileName.equalsIgnoreCase(PluginConstants.PLUGIN_FILENAME_YML)
                        || fileName.equalsIgnoreCase(PluginConstants.PLUGIN_FILENAME_YAML))) {
                    foundPlugin.accept(f);
                    isPlugin = true;
                } else if (f.isDirectory()) {
                    directors.add(f);
                }
            }
            if (!isPlugin) {
                directors.forEach(f -> scanPlugin(f, foundPlugin));
            }
        }
    }

    public static Configuration loadReferenceConfig(Configuration pluginConfig, String pluginHome) {
        pluginConfig.find((k, v) -> v instanceof String str
                        && str.startsWith("@")
                        && (str.endsWith(".json")
                        || str.endsWith(".yml")
                        || str.endsWith(".yaml")))
                .forEach(entry -> {
                    String fileName = entry.getValue().toString().substring(1);
                    File file = new File(pluginHome, fileName);
                    if (file.exists()) {
                        Configuration referenceConfig = Configuration.from(file);
                        pluginConfig.set(entry.getKey(), loadReferenceConfig(referenceConfig, pluginHome));
                    }
                });
        return pluginConfig;
    }

    public static final String PLUGIN_KEY_FORMAT = "plugin.%s.%s";
    public static String generatePluginKey(Configuration pluginConfig) {
        return generatePluginKey(getPluginName(pluginConfig), getPluginVersion(pluginConfig));
    }
    public static String generatePluginKey(String pluginName, String pluginVersion) {
        return String.format(PLUGIN_KEY_FORMAT, encodeStr(pluginName), encodeStr(pluginVersion));
    }

    private static String encodeStr(String str) {
        if (str == null) {
            return null;
        }
        return str.replaceAll(" ", "").replaceAll("\\.", "_");
    }

    private static final String configRequiredMessage = "Plugin config cannot be empty.";
    public static String getPluginName(Configuration pluginConfig) {
        Validate.notNull(pluginConfig, configRequiredMessage);
        return pluginConfig.getString(PluginConstants.PLUGIN_NAME);
    }
    public static String getPluginVersion(Configuration pluginConfig) {
        Validate.notNull(pluginConfig, configRequiredMessage);
        return pluginConfig.getString(PluginConstants.PLUGIN_VERSION, "latest");
    }
    public static String getPluginClasspath(Configuration pluginConfig) {
        Validate.notNull(pluginConfig, configRequiredMessage);
        return pluginConfig.getString(PluginConstants.PLUGIN_CLASSPATH);
    }

    @FunctionalInterface
    public static interface DownloadProgress {
        void accept(String filename, Path downloadFile, long progress);
    }
}
