package com.qz.hare.common.util;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * <AUTHOR> @ gmail.com>
 *  create at 2020/9/27 3:00 下午
 */
@Slf4j
public class ReflectionUtils {

	public static <T>  List<Field> getAllDeclaredFields(Class<T> clazz) {

		List<Field[]> fieldArrayList = new ArrayList<>();

		while (clazz != null) {
			fieldArrayList.add(clazz.getDeclaredFields());
			//noinspection unchecked
			clazz = (Class<T>) clazz.getSuperclass();
		}

		return fieldArrayList.stream().flatMap(Arrays::stream)
				.filter(f -> !Modifier.isStatic(f.getModifiers()))
				.toList();
	}

	public static <T>  Field[] getAllDeclaredFieldsIgnoreSuper(Class<T> clazz) {

		List<Field[]> fieldArrayList = new ArrayList<>();

		fieldArrayList.add(clazz.getDeclaredFields());

		int fieldCount = 0;
		int fieldIndex = 0;
		for (Field[] fieldArray : fieldArrayList) {
			fieldCount = fieldCount + fieldArray.length;
		}

		Field[] allFields = new Field[fieldCount];
		for (Field[] fieldArray : fieldArrayList) {
			for (Field field : fieldArray) {
				allFields[fieldIndex++] = field;
			}
		}

		return allFields;
	}

	public static Map<String, Class<?>> getAllFieldType(Class<?> clazz) {
		List<Field> fields = getAllDeclaredFields(clazz);

		Map<String, Class<?>> types = new HashMap<>();
		for (Field field : fields) {
			types.put(field.getName(), field.getType());
		}
		return types;
	}

	public static Object getFieldValue(Object target, String fieldName) {
		return getFieldValue(getField(target.getClass(), fieldName), target);
	}
	public static Object getFieldValue(Field field, Object target) {
		try {
			field.setAccessible(true);
			return field.get(target);
		} catch (IllegalAccessException e) {
			log.error(e.getMessage(), e);
		}
		return null;
	}

	public static Field getField(Class<?> c, String fieldName) {
		Optional<Field> fieldOptional = Arrays.stream(c.getDeclaredFields()).filter(f -> f.getName().equals(fieldName)).findFirst();
        return fieldOptional.orElseGet(() -> getField(c.getSuperclass(), fieldName));
    }
}
