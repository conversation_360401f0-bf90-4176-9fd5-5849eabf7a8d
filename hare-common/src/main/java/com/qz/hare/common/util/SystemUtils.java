package com.qz.hare.common.util;

import com.qz.hare.common.os.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import oshi.SystemInfo;

import java.io.File;
import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.MemoryPoolMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Stream;

import static com.qz.hare.common.util.CommonUtils.ignoreAnyError;
import static java.lang.Thread.sleep;

/**
 * <AUTHOR>
 * create at 2022/8/19 下午6:32
 */
public class SystemUtils {

    private static final Logger log = LoggerFactory.getLogger(SystemUtils.class);

    private static final OperatingSystemMXBean osMXBean;
    private static final RuntimeMXBean runtimeMXBean;
    private static final List<GarbageCollectorMXBean> garbageCollectorMXBeanList;
    private static final List<MemoryPoolMXBean> memoryPoolMXBeanList;
    private static final SystemInfo si;

    public static int cpus;

    static {
        osMXBean = java.lang.management.ManagementFactory.getOperatingSystemMXBean();
        runtimeMXBean = java.lang.management.ManagementFactory.getRuntimeMXBean();
        garbageCollectorMXBeanList = java.lang.management.ManagementFactory.getGarbageCollectorMXBeans();
        memoryPoolMXBeanList = java.lang.management.ManagementFactory.getMemoryPoolMXBeans();

        cpus = osMXBean.getAvailableProcessors();

        si = new SystemInfo();
    }

    /**
     * 收集静态信息
     * @param vmInfo
     */
    public static void fillingStaticVmInfo(VmInfo vmInfo) {
        //osInfo = osMXBean.getName() + " " + osMXBean.getArch() + " " + osMXBean.getVersion();
        //cpus = osMXBean.getAvailableProcessors();

        OSInfo osInfo = new OSInfo();
        osInfo.setName(osMXBean.getName());
        osInfo.setArch(osMXBean.getArch());
        osInfo.setCpus(cpus);
        osInfo.setVersion(osMXBean.getVersion());
        osInfo.setPhysicalMemory(getLongFromOperatingSystem(osMXBean, "getTotalPhysicalMemorySize"));
        osInfo.setMaxFileDescriptorCount(si.getOperatingSystem().getFileSystem().getMaxFileDescriptors());
        osInfo.setNetworkInterfaces(getNetworkInterfaces());

        vmInfo.setOs(osInfo);
        vmInfo.setJvm(String.format("%s %s %s",
                runtimeMXBean.getVmVendor(), runtimeMXBean.getSpecVersion(), runtimeMXBean.getVmVersion()));

        vmInfo.setFileEncoding(System.getProperty("file.encoding"));
        vmInfo.setDefaultCharset(Charset.defaultCharset().displayName());

        vmInfo.setUser(System.getenv("USER"));
        vmInfo.setWorkerDir(System.getenv("PWD"));
        vmInfo.setTmpDir(System.getenv("TMPDIR"));
        vmInfo.setProcessId(getProcessId());
    }

    public static List<NetworkInterface> getNetworkInterfaces() {

        try {
            Enumeration<java.net.NetworkInterface> interfaces = java.net.NetworkInterface.getNetworkInterfaces();
            List<NetworkInterface> networkInterfaces = new ArrayList<>();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = new NetworkInterface();
                java.net.NetworkInterface nf = interfaces.nextElement();
                if (nf.isLoopback() || nf.isVirtual() ||
                        nf.isPointToPoint() || !nf.isUp()) {
                    continue;
                }
                networkInterface.setName(nf.getName());
                networkInterface.setHardwareAddress(convertAddress(nf.getHardwareAddress(), "-"));
                networkInterface.setMTU(nf.getMTU());
                networkInterface.setDisplayName(nf.getDisplayName());
                networkInterface.setAddresses(nf.getInterfaceAddresses().stream().map(i -> {
                    Address address = new Address();
                    try {
                        InetAddress ip = InetAddress.getByAddress(i.getAddress().getAddress());
                        address.setIp(ip.getHostAddress());
                    } catch (UnknownHostException e) {
                        String ip = i.getAddress().getHostAddress();
                        address.setIp(ip);
                    }
                    address.setPrefixLength(i.getNetworkPrefixLength());
                    return address;
                }).toList());
                networkInterfaces.add(networkInterface);
            }
            return networkInterfaces;
        } catch (SocketException e) {
            return Collections.emptyList();
        }
    }

    private static String convertAddress(byte[] address, String spacing) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < address.length; i++) {
            sb.append(String.format("%02X%s", address[i], (i < address.length - 1) ? spacing : ""));
        }
        return sb.toString();
    }

    /**
     * 收集动态指标信息
     * @param vmInfo
     */
    public static void fillingDynamicInfo(VmInfo vmInfo) {

        try {
            vmInfo.setHostname(getHostname());
            vmInfo.setSysTime(System.currentTimeMillis());
            vmInfo.setUptime(runtimeMXBean.getUptime());
            vmInfo.setSysLoadAverage(osMXBean.getSystemLoadAverage());
            vmInfo.setCpuUsage(getCpuUsage());

            // os status
            if (vmInfo.getOsStatus() == null) {
                vmInfo.setOsStatus(new OSStatus());
            }
            vmInfo.getOsStatus().setFreeMemory(si.getHardware().getMemory().getAvailable());
            vmInfo.getOsStatus().setCurrentOpenFileDescriptorCount(
                    si.getOperatingSystem().getProcess(si.getOperatingSystem().getProcessId()).getOpenFiles());

            vmInfo.setMemUsage(getMemUsage(vmInfo.getOsStatus()));

            // gc status
            if (vmInfo.getGc() == null) {
                vmInfo.setGc(new HashMap<>());
            }
            if (garbageCollectorMXBeanList != null) {
                garbageCollectorMXBeanList.forEach(garbage -> {
                    GCStatus gcStatus = vmInfo.getGc().get(garbage.getName());
                    if (gcStatus == null) {
                        gcStatus = new GCStatus();
                        gcStatus.setName(garbage.getName());
                        vmInfo.getGc().put(garbage.getName(), gcStatus);
                    }

                    long curTotalGcCount = garbage.getCollectionCount();
                    gcStatus.setCurTotalGcCount(curTotalGcCount);

                    long curTotalGcTime = garbage.getCollectionTime();
                    gcStatus.setCurTotalGcTime(curTotalGcTime);
                });
            }

            // memory status
            if (memoryPoolMXBeanList != null && !memoryPoolMXBeanList.isEmpty()) {
                if (vmInfo.getMemory() == null) {
                    vmInfo.setMemory(new HashMap<>());
                }
                memoryPoolMXBeanList.forEach(pool -> {
                    MemoryStatus memoryStatus = vmInfo.getMemory().get(pool.getName());
                    if (memoryStatus == null) {
                        memoryStatus = new MemoryStatus();
                        memoryStatus.setName(pool.getName());
                        vmInfo.getMemory().put(pool.getName(), memoryStatus);
                    }
                    memoryStatus.setCommitedSize(pool.getUsage().getCommitted());
                    memoryStatus.setMaxMinUsedSize(pool.getUsage().getUsed());
                    long maxMemory = memoryStatus.getCommitedSize() > 0 ? memoryStatus.getCommitedSize() :
                            memoryStatus.getMaxSize();
                    memoryStatus.setMaxMinPercent(maxMemory > 0 ? (float) 100 * memoryStatus.getUsedSize() / maxMemory : -1);
                });
            }

            // disk status
            if (vmInfo.getDisk() == null) {
                vmInfo.setDisk(new ArrayList<>());
            }
            Stream.of(File.listRoots()).forEach(file -> {
                DiskStatus diskStatus = new DiskStatus();
                diskStatus.setPath(file.getPath());
                diskStatus.setTotal(file.getTotalSpace());
                diskStatus.setFree(file.getFreeSpace());
                vmInfo.getDisk().add(diskStatus);
            });
        } catch (Exception e) {
            log.error("Get system status fail", e);
        }
    }

    public static double getMemUsage(OSStatus osStatus) {
        long pageSize = si.getHardware().getMemory().getPageSize();
        long totalMemory = si.getHardware().getMemory().getTotal() * pageSize;
        long availableMemory = si.getHardware().getMemory().getAvailable() * pageSize;
        if (osStatus != null) {
            osStatus.setFreeMemory(availableMemory);
        }
        return ((double)(totalMemory - availableMemory)) / ((double) totalMemory);
    }
    public static double getCpuUsage() {
        long[] preTicks = si.getHardware().getProcessor().getSystemCpuLoadTicks();
        ignoreAnyError(() -> sleep(1000));
        return si.getHardware().getProcessor().getSystemCpuLoadBetweenTicks(preTicks);
    }

    public static long getLongFromOperatingSystem(OperatingSystemMXBean operatingSystem, String methodName) {
        var tmp =  getFromOperatingSystem(operatingSystem, methodName);
        if (tmp == null)
            return -1;
        if (tmp instanceof Long l)
            return l;
        return -1;
    }

    public static Object getFromOperatingSystem(OperatingSystemMXBean operatingSystem, String methodName) {
        try {
            final Method method = operatingSystem.getClass().getMethod(methodName, (Class<?>[]) null);
            method.setAccessible(true);
            return method.invoke(operatingSystem, (Object[]) null);
        } catch (Exception e) {
            log.error(String.format("OperatingSystemMXBean %s failed, Exception = %s ", methodName, e.getMessage()));
        }

        return null;
    }

    public static long getProcessId() {
        return ProcessHandle.current().pid();
    }

    public static String getHostname() {
        return IPUtils.getHostname();
    }

}
