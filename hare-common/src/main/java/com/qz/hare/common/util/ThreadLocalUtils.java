package com.qz.hare.common.util;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> @ gmail.com>
 *  create at 2020/10/10 4:58 下午
 */
public class ThreadLocalUtils {

	public final static String USER_LOCALE = "USER_LOCAL";
	public final static String REQUEST_ID = "REQUEST_ID";

	private final static ThreadLocal<Map<String, Object>> threadLocal = new ThreadLocal<>();

	public static void set(String key, Object value) {
		if (threadLocal.get() == null) {
			threadLocal.set(new HashMap<>());
		}
		threadLocal.get().put(key, value);
	}

	public static Object get(String key) {
		Map<String, Object> map = threadLocal.get();
		if (map != null && map.containsKey(key)) {
			return map.get(key);
		}
		return null;
	}

}
