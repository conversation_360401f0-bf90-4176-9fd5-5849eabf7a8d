package com.qz.hare.common.util;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * create at 2023/6/6 09:14
 */
@Slf4j
public class ThreadUtils {

    private ThreadUtils () {}

    public static boolean isAlive() {
        if (log.isTraceEnabled()) {
            log.trace("Thread {} state is {}, alive is {}, interrupted is {}",
                    Thread.currentThread().getName(), Thread.currentThread().getState(),
                    Thread.currentThread().isAlive(), Thread.currentThread().isInterrupted());
        }
        return Thread.currentThread().isAlive() && !Thread.currentThread().isInterrupted();
    }

    public static boolean wait(long millis, String msg) {
        try {
            Thread.sleep(millis);
            return false;
        } catch (InterruptedException e) {
            log.warn(msg);
            Thread.currentThread().interrupt();
        }
        return true;
    }
}
