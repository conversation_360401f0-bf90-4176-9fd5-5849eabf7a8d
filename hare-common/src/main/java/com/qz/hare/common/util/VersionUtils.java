package com.qz.hare.common.util;

import com.qz.hare.common.exception.CommonErrorCode;
import com.qz.hare.common.exception.HareException;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * create at 2024/12/29 13:08
 */
public class VersionUtils {
    private static final Pattern VERSION_PATTERN = Pattern.compile(
            "^(?<prefix>[a-zA-Z-_]*)?(?<major>\\d+)(?:\\.(?<minor>\\d+))?(?:\\.(?<revision>\\d+))?(?<suffix>[a-zA-Z-_]+)?$");

    private VersionUtils() {}

    /**
     * parse string as normalized version, e.g :
     *  - "8.0.3" => VersionUtils.NormalizedVersion(majorVersion=8, minorVersion=0, revisionVersion=3)
     *  - "v8.0.3" => VersionUtils.NormalizedVersion(majorVersion=8, minorVersion=0, revisionVersion=3)
     *  - "v8.0" => VersionUtils.NormalizedVersion(majorVersion=8, minorVersion=0, revisionVersion=0)
     *  - "v8" => VersionUtils.NormalizedVersion(majorVersion=8, minorVersion=0, revisionVersion=0)
     *  - "v8.1" => VersionUtils.NormalizedVersion(majorVersion=8, minorVersion=1, revisionVersion=0)
     *  - "v8.1.0a" => VersionUtils.NormalizedVersion(majorVersion=8, minorVersion=1, revisionVersion=0)
     * @param version version string
     * @return
     */
    public static VersionUtils.NormalizedVersion parse(String version) {
        if (StringUtils.isBlank(version)) {
            throw new HareException(CommonErrorCode.PARSE_VERSION_FAILED, "version string cannot be null or empty");
        }

        Matcher matcher = VERSION_PATTERN.matcher(version.trim());
        if (!matcher.matches()) {
            throw new HareException(CommonErrorCode.PARSE_VERSION_FAILED, "Invalid version format: " + version);
        }

        String prefix = matcher.group("prefix");
        int majorVersion = Integer.parseInt(matcher.group("major"));
        int minorVersion = matcher.group("minor") != null ? Integer.parseInt(matcher.group("minor")) : 0;
        int revisionVersion = matcher.group("revision") != null ? Integer.parseInt(matcher.group("revision")) : 0;
        String suffix = matcher.group("suffix");


        return NormalizedVersion.builder()
                .prefix(StringUtils.defaultString(prefix))
                .majorVersion(majorVersion)
                .minorVersion(minorVersion)
                .revisionVersion(revisionVersion)
                .suffix(StringUtils.defaultString(suffix))
                .build();
    }

    @Data
    @Builder
    public static class NormalizedVersion implements Comparable<NormalizedVersion> {
        private String prefix;
        private int majorVersion; // 主要版本号
        private int minorVersion; // 次要版本号
        private int revisionVersion; // 修订版本号
        private String suffix;

        @Override
        public int compareTo(NormalizedVersion o) {
            if (this.majorVersion != o.majorVersion) {
                return Integer.compare(this.majorVersion, o.majorVersion);
            }
            if (this.minorVersion != o.minorVersion) {
                return Integer.compare(this.minorVersion, o.minorVersion);
            }
            if (this.revisionVersion != o.revisionVersion) {
                return Integer.compare(this.revisionVersion, o.revisionVersion);
            }
            // 如果主要、次要和修订版本号相等，比较后缀（按字典顺序）
            return StringUtils.defaultString(this.suffix).compareTo(StringUtils.defaultString(o.suffix));
        }

        @Override
        public String toString() {
            return String.format("%s%s.%s.%s%s",
                    StringUtils.isNotBlank(prefix) ? prefix : "",
                    majorVersion, minorVersion, revisionVersion,
                    StringUtils.isNotBlank(suffix) ? suffix : "");
        }
    }
}
