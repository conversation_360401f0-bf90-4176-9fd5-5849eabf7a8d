package com.qz.hare.common.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * create at 2024/11/9 16:05
 */
@Slf4j
public class YamlUtils {

    private YamlUtils(){}

    public static String toYaml(Object object) {
        ObjectMapper mapper = new ObjectMapper(YAMLFactory.builder().build());
        mapper.findAndRegisterModules();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Serialization to yaml error: {}", e.getMessage(), e);
            return null;
        }
    }
}
