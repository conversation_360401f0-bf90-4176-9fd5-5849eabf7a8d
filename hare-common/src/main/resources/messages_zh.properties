ConfigError=\u60a8\u63d0\u4f9b\u7684\u914d\u7f6e\u6587\u4ef6\u4e2d\u5b58\u5728\u9519\u8bef\u4fe1\u606f\uff0c\u8bf7\u68c0\u67e5\u60a8\u7684\u914d\u7f6e\uff1a{0}
ConvertNotSupport=\u540c\u6b65\u6570\u636e\u5b58\u5728\u4e1a\u52a1\u810f\u6570\u636e\uff0c\u6570\u636e\u7c7b\u578b\u8f6c\u6362\u9519\u8bef\u3002
ConvertOverFlow=\u540c\u6b65\u6570\u636e\u5b58\u5728\u4e1a\u52a1\u810f\u6570\u636e\uff0c\u6570\u636e\u7c7b\u578b\u8f6c\u6362\u6ea2\u51fa\u3002
RetryFail=\u65b9\u6cd5\u8c03\u7528\u591a\u6b21\u4ecd\u7136\u5931\u8d25\u3002
RuntimeError=\u672a\u6355\u83b7\u5f02\u5e38\uff1a{0}
HookInternalError=Hook\u8fd0\u884c\u9519\u8bef\u3002
ShutDownTask=\u4efb\u52a1\u5df2\u6536\u5230\u5173\u95ed\u547d\u4ee4\uff0c\u51c6\u5907\u8fdb\u884c\u6545\u969c\u8f6c\u79fb
WaitTimeExceed=\u7b49\u5f85\u65f6\u95f4\u8d85\u51fa\u8303\u56f4
TaskHungExpired=\u4efb\u52a1\u6302\u8d77\uff0c\u5df2\u8fc7\u671f
ExecutorServiceNotInit=\u8bf7\u5148\u6267\u884c init() \u65b9\u6cd5\u521d\u59cb\u5316\u6267\u884c\u5668\u670d\u52a1
PluginLoadError=\u52a0\u8f7d\u63d2\u4ef6 {0}({1}) \u9519\u8bef\uff1a{2}
PluginNotInit=\u8bf7\u5148\u6267\u884c init() \u65b9\u6cd5\u52a0\u8f7d\u63d2\u4ef6
PluginDownloadError=\u4ece {1} \u4e0b\u8f7d\u63d2\u4ef6\u9519\u8bef {0}\u3002
PluginInitError=\u63d2\u4ef6\u521d\u59cb\u5316\u65f6\u51fa\u9519\u3002\u6b64\u95ee\u9898\u901a\u5e38\u662f\u7531\u4e8e\u5b89\u88c5\u4e0d\u6b63\u786e\u9020\u6210\u7684\u3002
PluginNotFound=\u672a\u627e\u5230\u63d2\u4ef6 {0}\u3002\u6b64\u95ee\u9898\u901a\u5e38\u662f\u7531\u4e8e \u7684\u5b89\u88c5\u4e0d\u6b63\u786e\u9020\u6210\u7684\u3002\u8bf7\u8054\u7cfb\u8fd0\u7ef4\u5bfb\u6c42\u5e2e\u52a9\u3002
PluginMissingConfig=\u63d2\u4ef6\u914d\u7f6e\u9519\u8bef\uff0c\u7f3a\u5c11\u914d\u7f6e {0}
PluginMissingIcon=\u63d2\u4ef6\u914d\u7f6e\u9519\u8bef\uff0c\u7f3a\u5c11\u56fe\u6807 {0} \u6216\u56fe\u6807\u6587\u4ef6\u4e0d\u5b58\u5728\u3002
PluginExecuteError=\u6267\u884c\u63d2\u4ef6\u65b9\u6cd5\u9519\u8bef\uff0c\u8bf7\u8054\u7cfb\u63d2\u4ef6\u5f00\u53d1\u8005\u534f\u52a9\u89e3\u51b3\u3002
PluginImplementError=\u63d2\u4ef6\u5b9e\u73b0\u9519\u8bef\uff0c\u8bf7\u8054\u7cfb\u63d2\u4ef6\u5f00\u53d1\u8005\u5bfb\u6c42\u5e2e\u52a9\u3002
PluginParamsValidateError=\u63d2\u4ef6\u53c2\u6570\u9a8c\u8bc1\u9519\u8bef {0}
SecretError=\u52a0\u5bc6\u89e3\u5bc6\u5931\u8d25\u3002
RequiredValue=\u7f3a\u5c11 {0} \u7684\u5fc5\u9700\u503c
IllegalArgument=\u975e\u6cd5\u53c2\u6570 {0}={1}
NotSupportType=\u4e0d\u652f\u6301\u7684\u7c7b\u578b
ArgumentError=\u5f15\u64ce\u8fd0\u884c\u4e0d\u6b63\u786e\u3002\u6b64\u95ee\u9898\u901a\u5e38\u662f\u7531\u5185\u90e8\u7f16\u7a0b\u9519\u8bef\u5f15\u8d77\u7684\u3002\u8bf7\u8054\u7cfb\u5f00\u53d1\u56e2\u961f\u89e3\u51b3\u3002
UnsupportedOperation=\u4e0d\u652f\u6301\u8bf7\u6c42\u7684\u64cd\u4f5c\u3002
NotFoundDriverClass=\u672a\u627e\u5230\u9a71\u52a8\u7a0b\u5e8f\u7c7b
ConnectorConfigNoProtocol=\u8fde\u63a5\u5668\u672a\u914d\u7f6e\u534f\u8bae\u3002
ConnectorConfigNoHost=\u8fde\u63a5\u5668\u672a\u914d\u7f6e\u4e3b\u673a\u3002
ConnectorConfigNoDatabase=\u8fde\u63a5\u5668\u672a\u914d\u7f6e\u6570\u636e\u5e93\u3002
ConnectorConfigError=\u8fde\u63a5\u5668\u914d\u7f6e\u9519\u8bef\uff1a{0}
UnsupportedType=\u4e0d\u652f\u6301\u7684\u6570\u636e\u7c7b\u578b
ParseVersionFailed=\u89e3\u6790\u7248\u672c\u5931\u8d25\uff1a{0}
UnsupportedCompressFormat=\u4e0d\u652f\u6301 {0} \u538b\u7f29\uff0c\u60a8\u53ef\u4ee5\u4f7f\u7528 zip \u538b\u7f29\u683c\u5f0f\u3002
