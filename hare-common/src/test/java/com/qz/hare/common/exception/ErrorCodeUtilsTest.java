package com.qz.hare.common.exception;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ErrorCodeUtils 单元测试
 * 
 * <AUTHOR>
 * create at 2025/8/31 11:34
 */
public class ErrorCodeUtilsTest {

    @BeforeEach
    void setUp() {
        // 每次测试前重置ErrorCodeUtils状态
        ErrorCodeUtils.reset();
    }

    @Test
    void testGetErrorCodeWithCommonErrorCode() {
        // 测试获取CommonErrorCode中的错误码
        ErrorCode configError = ErrorCodeUtils.getErrorCode("ConfigError");
        assertNotNull(configError, "应该能够找到ConfigError错误码");
        assertEquals("ConfigError", configError.getCode());
        assertEquals("There is an error message in the configuration file you provided, please check your configuration: {0}", 
                configError.getDescription());
        assertTrue(configError instanceof CommonErrorCode);
        assertEquals(CommonErrorCode.CONFIG_ERROR, configError);
    }

    @Test
    void testGetErrorCodeWithRuntimeError() {
        // 测试获取RuntimeError错误码
        ErrorCode runtimeError = ErrorCodeUtils.getErrorCode("RuntimeError");
        assertNotNull(runtimeError, "应该能够找到RuntimeError错误码");
        assertEquals("RuntimeError", runtimeError.getCode());
        assertEquals("Uncaught exception: {0}", runtimeError.getDescription());
        assertTrue(runtimeError instanceof CommonErrorCode);
        assertEquals(CommonErrorCode.RUNTIME_ERROR, runtimeError);
    }

    @Test
    void testGetErrorCodeWithConvertNotSupport() {
        // 测试获取ConvertNotSupport错误码
        ErrorCode convertNotSupport = ErrorCodeUtils.getErrorCode("ConvertNotSupport");
        assertNotNull(convertNotSupport, "应该能够找到ConvertNotSupport错误码");
        assertEquals("ConvertNotSupport", convertNotSupport.getCode());
        assertEquals("Synchronized data has business dirty data, data type conversion error.", 
                convertNotSupport.getDescription());
        assertTrue(convertNotSupport instanceof CommonErrorCode);
        assertEquals(CommonErrorCode.CONVERT_NOT_SUPPORT, convertNotSupport);
    }

    @Test
    void testGetErrorCodeWithNonExistentCode() {
        // 测试获取不存在的错误码
        ErrorCode nonExistent = ErrorCodeUtils.getErrorCode("NonExistentErrorCode");
        assertNull(nonExistent, "不存在的错误码应该返回null");
    }

    @Test
    void testGetErrorCodeWithNullInput() {
        // 测试传入null参数
        ErrorCode nullResult = ErrorCodeUtils.getErrorCode(null);
        assertNull(nullResult, "传入null应该返回null");
    }

    @Test
    void testGetErrorCodeWithEmptyString() {
        // 测试传入空字符串
        ErrorCode emptyResult = ErrorCodeUtils.getErrorCode("");
        assertNull(emptyResult, "传入空字符串应该返回null");
    }

    @Test
    void testGetAllErrorCodes() {
        // 测试获取所有错误码
        Map<String, ErrorCode> allErrorCodes = ErrorCodeUtils.getAllErrorCodes();
        assertNotNull(allErrorCodes, "getAllErrorCodes应该返回非null的Map");
        assertFalse(allErrorCodes.isEmpty(), "错误码Map不应该为空");
        
        // 验证CommonErrorCode中的一些错误码是否存在
        assertTrue(allErrorCodes.containsKey("ConfigError"), "应该包含ConfigError");
        assertTrue(allErrorCodes.containsKey("RuntimeError"), "应该包含RuntimeError");
        assertTrue(allErrorCodes.containsKey("ConvertNotSupport"), "应该包含ConvertNotSupport");
        
        // 验证返回的是副本，修改不会影响原始数据
        int originalSize = allErrorCodes.size();
        allErrorCodes.clear();
        Map<String, ErrorCode> allErrorCodesAgain = ErrorCodeUtils.getAllErrorCodes();
        assertEquals(originalSize, allErrorCodesAgain.size(), "返回的应该是副本，清空不应该影响原始数据");
    }

    @Test
    void testInitializationIsThreadSafe() throws InterruptedException {
        // 测试多线程初始化的线程安全性
        final int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        final ErrorCode[] results = new ErrorCode[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                results[index] = ErrorCodeUtils.getErrorCode("ConfigError");
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 验证所有线程都获得了相同的结果
        for (int i = 0; i < threadCount; i++) {
            assertNotNull(results[i], "线程" + i + "应该获得非null结果");
            assertEquals(CommonErrorCode.CONFIG_ERROR, results[i], "所有线程应该获得相同的结果");
        }
    }

    @Test
    void testToStringFormat() {
        // 测试ErrorCode的toString格式化方法
        ErrorCode configError = ErrorCodeUtils.getErrorCode("ConfigError");
        assertNotNull(configError);
        
        String formatted = configError.toStringFormat();
        assertNotNull(formatted);
        assertTrue(formatted.contains("ConfigError"), "格式化字符串应该包含错误码");
        assertTrue(formatted.contains("Code:"), "格式化字符串应该包含'Code:'");
        assertTrue(formatted.contains("Describe:"), "格式化字符串应该包含'Describe:'");
    }

    @Test
    void testMultipleCallsReturnSameInstance() {
        // 测试多次调用返回相同实例
        ErrorCode first = ErrorCodeUtils.getErrorCode("ConfigError");
        ErrorCode second = ErrorCodeUtils.getErrorCode("ConfigError");
        
        assertNotNull(first);
        assertNotNull(second);
        assertSame(first, second, "多次调用应该返回相同的实例");
    }
}
