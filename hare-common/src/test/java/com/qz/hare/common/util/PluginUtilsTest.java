package com.qz.hare.common.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * create at 2025/7/4 08:43
 */
class PluginUtilsTest {

    @Test
    void testLoadReferenceConfig() {

        Configuration config = Configuration.fromJson(PluginUtilsTest.class.getResourceAsStream("/test_reference_file.json"));

        Assertions.assertNotNull(config);
        Assertions.assertEquals("@json_schema.json", config.get("jsonSchema"));

        String path = System.getProperty("user.dir") + "/src/test/resources";

        config = PluginUtils.loadReferenceConfig(config, path);
        Assertions.assertNotNull(config);
        Assertions.assertNotEquals("@json_schema.json", config.get("jsonSchema"));
        Assertions.assertEquals("object", config.get("jsonSchema.type"));
        Assertions.assertEquals("Deployment", config.get("dependencies.formConfig.kind"));
    }

}
