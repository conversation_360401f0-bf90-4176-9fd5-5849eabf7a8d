package com.qz.hare.core.job;

import com.qz.hare.common.constants.JobConstants;
import com.qz.hare.common.exception.CommonErrorCode;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.common.util.Configuration;
import com.qz.hare.core.AbstractContainer;
import com.qz.hare.core.dag.JobGraph;
import com.qz.hare.core.log.LoggerFactory;
import com.qz.hare.core.plugin.PluginRegistry;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;

/**
 * Job 实例运行在 Job 中，只负责任务的初始化、拆分、调度、运行、回收、监控和汇报，但它不做实际的数据处理操作
 * - 执行数据同步生命周期 init()->start()->stop()->destroy()
 *
 * <AUTHOR>
 * create at 2022/9/18 下午1:39
 */
public class JobRunner extends AbstractContainer {

    private long startTimestamp;
    private long endTimestamp;
    private PluginRegistry pluginRegistry;
    private JobGraph jobGraph;

    protected Logger log;

    public JobRunner(Configuration config) {
        super(config);
        log = LoggerFactory.getLogger(getId(), JobRunner.class);
    }

    @Override
    protected String getThreadNamePrefix() {
        return "job-" + getId();
    }

    /**
     * JobContainer 创建任务后立即执行初始化方法
     */
    @Override
    public void init() {
        super.init();
        initContainer();
    }

    private void initContainer() {
        log.info("init.");
        pluginRegistry = new PluginRegistry();
        pluginRegistry.init();
    }

    /**
     * 启动执行的完整过程
     */
    @Override
    public void start() {
        Validate.notNull(pluginRegistry, "Please execute init() method before init job.");
        log.info("starts job.");
        try {
            this.startTimestamp = System.currentTimeMillis();

            log.info("starts to do init job ...");
            this.jobGraph = new JobGraph(getJobConfiguration(), pluginRegistry, getExecutorService());
            this.jobGraph.init();
            log.info("starts to do check job ...");
            this.jobGraph.preCheck();
            log.info("starts to do prepare job ...");
            this.jobGraph.prepare();
            log.info("starts to do start ...");
            this.jobGraph.start();
            log.info("Job run completed");
        } catch (HareException e) {
            throw e;
        } catch (Exception e) {
            log.error("Exception with job run", e);
            //throw new HareException(CommonErrorCode.)
        } finally {
            log.info("Job {} to stop, total cost {}s", getId(), (this.endTimestamp - this.startTimestamp) / 1000);
            this.endTimestamp = System.currentTimeMillis();
        }
    }

    private int adjustTaskNumber() {
        int needTaskNumberByRecord = Integer.MAX_VALUE;

        boolean isRecordLimit = getJobConfiguration().getInt(JobConstants.JOB_SETTING_SPEED_RECORD, 0) > 0;
        if (isRecordLimit) {
            Long globalLimitedRecordSpeed = getJobConfiguration()
                    .getLong(JobConstants.JOB_SETTING_SPEED_RECORD, 100000);
            Long taskLimitedRecordSpeed = getJobConfiguration()
                    .getLong(JobConstants.JOB_SETTING_SPEED_RECORD_PRE_TASK);
            if (taskLimitedRecordSpeed == null || taskLimitedRecordSpeed <= 0) {
                throw new HareException(CommonErrorCode.CONFIG_ERROR,
                        "Under the condition of setting the global tps speed limit, " +
                                "the tps value of a single task must be set and greater than 0");
            }
            needTaskNumberByRecord = (int) (globalLimitedRecordSpeed / taskLimitedRecordSpeed);
            needTaskNumberByRecord = needTaskNumberByRecord > 0 ? needTaskNumberByRecord : 1;
            log.info("Job set Max-Record-Speed to {} records.", globalLimitedRecordSpeed);
        }
        int taskNumber = needTaskNumberByRecord;

        if (taskNumber < Integer.MAX_VALUE) {
            log.info("Job set Task-Number to {} tasks.", taskNumber);
            return taskNumber;
        }

        boolean isTaskLimit = getJobConfiguration()
                .getInt(JobConstants.JOB_SETTING_SPEED_TASK, 0) > 0;

        if (isTaskLimit) {
            taskNumber = getJobConfiguration().getInt(JobConstants.JOB_SETTING_SPEED_TASK);

            log.info("Job set Task-Number to {} tasks.", taskNumber);
            return taskNumber;
        }

        throw new HareException(CommonErrorCode.CONFIG_ERROR,
                String.format("Job running speed(%s) must be set.", JobConstants.JOB_SETTING_SPEED));

    }

    /**
     * 停止任务
     *
     * @param force 如果 force 为 true 则强制停止任务，否则正常退出任务
     */
    @Override
    public void stop(boolean force) {
        if (jobGraph != null) {
            jobGraph.stop(force);
        }
        log.info("{} job.", force ? "Force stop" : "Stop");
    }

    /**
     * 销毁 Job
     */
    @Override
    public void destroy() {
        if (jobGraph != null) {
            jobGraph.destroy();
        }
        if (pluginRegistry != null) {
            pluginRegistry.destroy();
        }

        super.destroy();
        LoggerFactory.stopLoggerFactory(getId());
        log.info("Destroy job {}.", getId());
    }
}
