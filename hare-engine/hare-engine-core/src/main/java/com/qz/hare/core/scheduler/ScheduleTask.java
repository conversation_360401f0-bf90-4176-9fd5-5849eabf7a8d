package com.qz.hare.core.scheduler;

import com.qz.hare.common.os.VmInfo;
import com.qz.hare.common.util.SystemUtils;
import com.qz.hare.core.websocket.ServerApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * create at 2022/8/14 下午1:01
 */
@Slf4j
@Component
public class ScheduleTask {

    @Value("${application.version:}")
    private String version;
    @Value("${application.build:}")
    private String build;
    @Value("${git.branch:}")
    private String gitBranch;
    @Value("${git.commit.id.describe:}")
    private String gitCommitId;
    private final ServerApi serverApi;
    private AtomicBoolean first = new AtomicBoolean(true);

    public ScheduleTask(ServerApi serverApi) {
        this.serverApi = serverApi;
    }

    @Scheduled(fixedDelay = 20, timeUnit = TimeUnit.SECONDS, initialDelay = 10)
    public void vmStatistics() {

        VmInfo vmInfo = new VmInfo();
        if (first.get()) {
            SystemUtils.fillingStaticVmInfo(vmInfo);
            vmInfo.setVersion(version);
            vmInfo.setBuild(build);
            vmInfo.setGitBranch(gitBranch);
            vmInfo.setGitCommitId(gitCommitId);
            first.set(false);
        }
        SystemUtils.fillingDynamicInfo(vmInfo);

        boolean result = serverApi.collectorEngineStatus(vmInfo);
        if (!result) {
            first.set(true);
        }
    }
}
