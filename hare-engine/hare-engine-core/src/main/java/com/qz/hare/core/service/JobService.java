package com.qz.hare.core.service;

import com.qz.hare.common.base.LifeCycle;
import com.qz.hare.common.util.Configuration;
import com.qz.hare.core.AbstractContainer;
import com.qz.hare.core.config.EngineConfig;
import com.qz.hare.core.job.JobRunner;
import com.qz.hare.core.websocket.ServerApi;
import com.qz.hare.server.common.model.job.Job;
import com.qz.hare.ws.rpc.AllowRemoteCall;
import com.qz.hare.ws.rpc.RemoteService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 用户控制任务的接口
 *
 * <AUTHOR>
 * create at 2022/9/15 下午2:27
 */
@Slf4j
@Component
@RemoteService
@RequiredArgsConstructor
public class JobService implements LifeCycle {

    private final ServerApi serverApi;
    private Map<String, AbstractContainer> jobs;
    private ThreadPoolExecutor cachedThreadPool;
    private final EngineConfig engineConfig;

    @PostConstruct
    @Override
    public void init() {
        jobs = new ConcurrentHashMap<>();
        CustomizableThreadFactory threadFactory = new CustomizableThreadFactory();
        threadFactory.setThreadNamePrefix("job-");
        threadFactory.setThreadGroupName("jobs");
        cachedThreadPool = new ThreadPoolExecutor(0, engineConfig.getMaxJobThreads(),
                10L, TimeUnit.SECONDS, new SynchronousQueue<>(), threadFactory);
    }

    /**
     * 启动任务
     */
    @AllowRemoteCall
    public void startJob(Job jobConfig) {
        if (jobConfig == null) {
            log.warn("Job configuration can not be null");
            return ;
        }
        log.info("start job: {}", jobConfig.getId().toHexString());

        if (cachedThreadPool.getActiveCount() >= engineConfig.getMaxJobThreads()) {
            log.warn("Engine current active job count ({}) is greater than max active job threads ({})",
                    cachedThreadPool.getActiveCount(), engineConfig.getMaxJobThreads());
        }

        cachedThreadPool.submit(() -> {
            try {
                Configuration jobConfiguration = Configuration.fromJson(jobConfig.getId().toHexString());
                JobRunner jobRunner = new JobRunner(jobConfiguration);
                jobRunner.init();
                jobRunner.start();
                jobs.put(jobRunner.getId(), jobRunner);
            } catch (Exception e) {
                log.error("Running job has error {}", jobConfig, e);
                serverApi.onJobError(jobConfig.getId(), e);
            }
        });
    }

    /**
     * 停止任务
     */
    public void stopJob(String jobId, boolean force) {
        Validate.notBlank(jobId, "Job id can not be blank.");

        log.info("jobService stop job with id {}", jobId);
        if (!jobs.containsKey(jobId)) {
            log.warn("Stop job failed, not found job with id {}", jobId);
            return;
        }

        AbstractContainer jobContainer = jobs.get(jobId);
        jobContainer.stop(force);
        jobContainer.destroy();
        // remove job container on job stopped.
    }

    @AllowRemoteCall
    public Collection<String> getJobIds() {
        return jobs.keySet();
    }

    @PreDestroy
    @Override
    public void destroy() {

    }
}
