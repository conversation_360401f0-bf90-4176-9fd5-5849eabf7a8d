package com.qz.hare.core.websocket;

import com.qz.hare.common.exception.CommonErrorCode;
import com.qz.hare.common.exception.ErrorCode;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.common.os.VmInfo;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/8/22 14:27
 */
@Component
@RequiredArgsConstructor
public class ServerApi {

    private static final String engineServiceName = "engineService";
    private static final String collectorEngineStatusMethodName = "collectorEngineStatus";

    private static final String jobServiceName = "jobService";
    private static final String onJobErrorMethodName = "onJobError";

    private final EngineClient engineClient;

    public boolean collectorEngineStatus(VmInfo vmInfo) {
        return engineClient.call(engineServiceName, collectorEngineStatusMethodName, List.of(vmInfo), true);
    }

    public boolean onJobError(ObjectId jobId, Throwable e) {
        ErrorCode code;
        Object[] args;
        if (e instanceof HareException hareException) {
            code = hareException.getErrorCode();
            args = hareException.getArgs();
        } else {
            code = CommonErrorCode.JOB_UNKNOWN_ERROR;
            args = new Object[]{jobId.toHexString(), e.getMessage()};
        }

        // TODO: 这里需要添加重试
        return engineClient.call(jobServiceName, onJobErrorMethodName, List.of(jobId.toHexString(), code, args), true);
    }
}
