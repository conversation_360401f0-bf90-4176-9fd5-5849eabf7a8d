<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true" scanPeriod="3 seconds">
	<property name="logName" value="hare-engine" />
	<property name="logPath" value="logs"/>
	<contextName>${logName}</contextName>

	<turboFilter class="com.qz.hare.core.log.filter.DuplicateMessageFilter">
		<allowedRepetitions>5</allowedRepetitions>
		<cacheSize>1000</cacheSize>
		<timeWindow>10000</timeWindow>
	</turboFilter>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>
				%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %highlight(%level) %logger{16}#%M - %msg%n
			</pattern>
		</encoder>
	</appender>

	<!--<appender name="APP_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${logPath}/${logName}-${HOSTNAME}.log</file>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${logPath}/${logName}-${HOSTNAME}-%d{yyyy-MM-dd}.%i.zip</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>1024MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>7</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
		</encoder>
	</appender>-->

	<jmxConfigurator />
	<root level="WARN">
		<appender-ref ref="STDOUT" />
		<!--<appender-ref ref="APP_LOG_FILE" />-->
	</root>

	<logger name="com.qz.hare" level="DEBUG"/>

</configuration>
