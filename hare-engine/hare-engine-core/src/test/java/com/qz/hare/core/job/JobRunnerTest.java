package com.qz.hare.core.job;

import com.qz.hare.common.constants.JobConstants;
import com.qz.hare.common.util.Configuration;
import com.qz.hare.engine.metrics.MetricsCenter;
import com.qz.hare.engine.metrics.registry.PrintMeterRegistry;
import io.micrometer.core.instrument.Measurement;
import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.util.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * create at 2022/9/18 下午4:17
 */
@Slf4j
@Disabled
class JobRunnerTest {

    public JobRunnerTest() {
        PrintMeterRegistry printMeterRegistry = new PrintMeterRegistry(Duration.ofSeconds(2));
        MetricsCenter.globalRegistry.add(printMeterRegistry);
        printMeterRegistry.start(new NamedThreadFactory("print"));
    }

    @BeforeEach
    void clearMeters() {
        MetricsCenter.globalRegistry.clear();
    }

    @Test
    void testDryRun() throws IOException {

        String jobConfig = IOUtils.toString(
                Objects.requireNonNull(getClass().getResourceAsStream("/jobs/job-dry-run.json")));
        Configuration jobConfiguration = Configuration.fromJson(jobConfig);

        JobRunner jobRunner = startJob(jobConfiguration);

        Assertions.assertEquals("642b6fc6df285d27b9bd9c5b", jobRunner.getId());

        stopJob(jobRunner);

    }

    @Test
    void testFullMigrationPerformance() throws IOException, InterruptedException {

        String filePath = "/jobs/job-full-migration_performance.yaml";
        String jobConfig = IOUtils.toString(
                Objects.requireNonNull(getClass().getResourceAsStream(filePath)), StandardCharsets.UTF_8);
        Configuration jobConfiguration = Configuration.fromYaml(jobConfig);

        long start = System.currentTimeMillis();
        JobRunner jobRunner = startJob(jobConfiguration);

        stopJob(jobRunner);
        long count = jobConfiguration.getLong("job.dag.nodes.[0].plugin.params.tables.[0].sliceRecordCount");
        long time = System.currentTimeMillis() - start;
        long avg = count / (time / 1000);
        log.info("counter:{}, time: {}ms, avg: {}行/s", count, time, avg);

        Assertions.assertTrue(avg >= 1000000, "性能指标测试未通过，每秒处理数据需要大于 1,000,000");
        Thread.sleep(3000);
    }

    private double getMeterValue(String meterName, String tagKey, String tagValue) {
        List<Meter> meters = MetricsCenter.globalRegistry.getMeters();
        Optional<Meter> meterOptional = meters.stream().filter(meter -> {

            if (meter.getId().getName().equals(meterName)) {
                return meter.getId().getTags().stream().anyMatch(tag -> tag.getKey().equals(tagKey) && tag.getValue().equals(tagValue));
            }
            return false;

        }).findFirst();
        if (meterOptional.isPresent()) {
            Iterator<Measurement> iterator = meterOptional.get().measure().iterator();
            if (iterator.hasNext()) {
                return iterator.next().getValue();
            }
        }
        return -1D;
    }

    @Test
    void testFullMigrationPerformance_1() throws IOException, InterruptedException {

        String filePath = "/jobs/job-full-migration_performance_1.yaml";
        String jobConfig = IOUtils.toString(
                Objects.requireNonNull(getClass().getResourceAsStream(filePath)), StandardCharsets.UTF_8);
        Configuration jobConfiguration = Configuration.fromYaml(jobConfig);

        long start = System.currentTimeMillis();
        JobRunner jobRunner = startJob(jobConfiguration);

        stopJob(jobRunner);

        double node1Total = getMeterValue("dag.node.output.total", "nodeId", "1"),
                node2Total = getMeterValue("dag.node.input.total", "nodeId", "2");
        log.info("source node total {}, target node total {}", BigDecimal.valueOf(node1Total), BigDecimal.valueOf(node2Total));
        Assertions.assertEquals(node1Total, node2Total);

        Integer total = jobConfiguration.getInt("job.dag.nodes.[0].plugin.params.tables.[0].sliceRecordCount", -1);
        Assertions.assertEquals(BigDecimal.valueOf(total + 1), BigDecimal.valueOf(node2Total));

        long count = jobConfiguration.getLong("job.dag.nodes.[0].plugin.params.tables.[0].sliceRecordCount");
        long time = System.currentTimeMillis() - start;
        long avg = count / (time / 1000);
        log.info("counter:{}, time: {}ms, avg: {}行/s", count, time, avg);

        Assertions.assertTrue(avg >= 500000, "性能指标测试未通过，每秒处理数据需要大于 500,000");
    }

    @Test
    void testFullMigration() throws IOException, InterruptedException {

        String filePath = "/jobs/job-full-migration.yaml";
        String jobConfig = IOUtils.toString(
                Objects.requireNonNull(getClass().getResourceAsStream(filePath)), StandardCharsets.UTF_8);
        Configuration jobConfiguration = Configuration.fromYaml(jobConfig);

        JobRunner jobRunner = startJob(jobConfiguration);

        Assertions.assertEquals("642b6fc6df285d27b9bd9c5d", jobRunner.getId());

        stopJob(jobRunner);

        Integer total = jobConfiguration.getInt("job.dag.nodes.[0].plugin.params.tables.[0].sliceRecordCount", -1);

        double node1Total = getMeterValue("dag.node.output.total", "nodeId", "1"),
                node2Total = getMeterValue("dag.node.input.total", "nodeId", "2");
        log.info("source node total {}, target node total {}", BigDecimal.valueOf(node1Total), BigDecimal.valueOf(node2Total));
        Assertions.assertEquals(node1Total, node2Total);
        Assertions.assertEquals(total +1, node2Total);
    }

    @Test
    void testMySQLStream() throws IOException {

        String filePath = "/jobs/job-mysql-stream.yaml";
        String jobConfig = IOUtils.toString(
                Objects.requireNonNull(getClass().getResourceAsStream(filePath)), StandardCharsets.UTF_8);

        Assertions.assertNotNull(jobConfig);

        Configuration jobConfiguration = Configuration.fromYaml(jobConfig);
        Assertions.assertEquals("642b6fc6df285d27b9bd9c5d", jobConfiguration.getString(JobConstants.JOB_ID));

        JobRunner jobRunner = startJob(jobConfiguration);
        Assertions.assertEquals("642b6fc6df285d27b9bd9c5d", jobRunner.getId());
        stopJob(jobRunner);
    }

    @Test
    void testCDCMigration() throws IOException {
        String jobConfig = IOUtils.toString(
                Objects.requireNonNull(getClass().getResourceAsStream("/jobs/job-cdc-migration.yaml")));
        Configuration jobConfiguration = Configuration.fromYaml(jobConfig);

        JobRunner jobRunner = startJob(jobConfiguration);

        Assertions.assertEquals("642b6fc6df285d27b9bd9c5d", jobRunner.getId());

        stopJob(jobRunner);
    }

    @Test
    void testFullAndCdcMigration() throws IOException {
        String jobConfig = IOUtils.toString(
                Objects.requireNonNull(getClass().getResourceAsStream("/jobs/job-full-cdc-migration.yaml")));
        Configuration jobConfiguration = Configuration.fromYaml(jobConfig);

        JobRunner jobRunner = startJob(jobConfiguration);

        Assertions.assertEquals("642b6fc6df285d27b9bd9c5d", jobRunner.getId());

        stopJob(jobRunner);
    }

    private JobRunner startJob(Configuration jobConfiguration) {
        JobRunner jobRunner = new JobRunner(jobConfiguration);
        jobRunner.init();
        jobRunner.start();
        return jobRunner;
    }

    private void stopJob(JobRunner jobRunner) {
        jobRunner.stop(false);
        jobRunner.destroy();
    }

}
