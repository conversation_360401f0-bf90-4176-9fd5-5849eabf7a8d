package com.qz.hare.core.test;

import com.qz.hare.common.util.Configuration;
import com.qz.hare.core.dag.JobGraph;
import com.qz.hare.core.dag.NodeType;
import com.qz.hare.core.plugin.PluginRegistry;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * create at 2024/7/2 21:09
 */
public class TestJobRunnerGraph {

    @Test
    public void testDeduceNodeType () {
        JobGraph jobGraph = new JobGraph(Configuration.newDefault(),
                new PluginRegistry(), Executors.newSingleThreadExecutor());

        Configuration config = Configuration.fromYaml("{\"edges\":[{\"src\":\"1\",\"dst\":\"2\"}]}");
        List<Configuration> edges = config.getListConfiguration("edges");
        Map<String, NodeType> result = jobGraph.deduceNodeType(edges);
        Assertions.assertNotNull(result);
        Assertions.assertNotNull(result.get("1"));
        Assertions.assertNotNull(result.get("2"));
        Assertions.assertEquals(2, result.size());
        Assertions.assertEquals(NodeType.SOURCE, result.get("1"));
        Assertions.assertEquals(NodeType.SINK, result.get("2"));

        config = Configuration.fromYaml("{\"edges\":[{\"src\":\"1\",\"dst\":\"2\"}, {\"src\":\"2\",\"dst\":\"3\"}]}");
        edges = config.getListConfiguration("edges");
        result = jobGraph.deduceNodeType(edges);
        Assertions.assertNotNull(result);
        Assertions.assertNotNull(result.get("1"));
        Assertions.assertNotNull(result.get("2"));
        Assertions.assertNotNull(result.get("3"));
        Assertions.assertEquals(3, result.size());
        Assertions.assertEquals(NodeType.SOURCE, result.get("1"));
        Assertions.assertEquals(NodeType.TRANSFORM, result.get("2"));
        Assertions.assertEquals(NodeType.SINK, result.get("3"));

        config = Configuration.fromYaml("{\"edges\":[{\"src\":\"1\",\"dst\":\"2\"}, {\"src\":\"2\",\"dst\":\"3\"}, {\"src\":\"2\",\"dst\":\"4\"}]}");
        edges = config.getListConfiguration("edges");
        result = jobGraph.deduceNodeType(edges);
        Assertions.assertNotNull(result);
        Assertions.assertNotNull(result.get("1"));
        Assertions.assertNotNull(result.get("2"));
        Assertions.assertNotNull(result.get("3"));
        Assertions.assertNotNull(result.get("4"));
        Assertions.assertEquals(4, result.size());
        Assertions.assertEquals(NodeType.SOURCE, result.get("1"));
        Assertions.assertEquals(NodeType.TRANSFORM, result.get("2"));
        Assertions.assertEquals(NodeType.SINK, result.get("3"));
        Assertions.assertEquals(NodeType.SINK, result.get("4"));

        config = Configuration.fromYaml("{\"edges\":[{\"src\":\"1\",\"dst\":\"2\"}, {\"src\":\"2\",\"dst\":\"3\"}, {\"src\":\"3\",\"dst\":\"4\"}]}");
        edges = config.getListConfiguration("edges");
        result = jobGraph.deduceNodeType(edges);
        Assertions.assertNotNull(result);
        Assertions.assertNotNull(result.get("1"));
        Assertions.assertNotNull(result.get("2"));
        Assertions.assertNotNull(result.get("3"));
        Assertions.assertNotNull(result.get("4"));
        Assertions.assertEquals(4, result.size());
        Assertions.assertEquals(NodeType.SOURCE, result.get("1"));
        Assertions.assertEquals(NodeType.TRANSFORM, result.get("2"));
        Assertions.assertEquals(NodeType.TRANSFORM, result.get("3"));
        Assertions.assertEquals(NodeType.SINK, result.get("4"));
    }
}
