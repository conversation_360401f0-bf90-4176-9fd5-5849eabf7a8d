package com.qz.hare.plugin.api.config;

import com.qz.hare.common.constants.PluginConstants;
import com.qz.hare.common.exception.CommonErrorCode;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.common.util.Configuration;
import com.qz.hare.model.plugin.PluginType;
import com.qz.hare.model.plugin.ScopeType;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;

import java.io.File;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

import static com.qz.hare.common.util.PluginUtils.generatePluginKey;

/**
 * <AUTHOR>
 * create at 2025/1/1 16:57
 */
public class PluginConfig {
    private final Configuration configuration;
    private Map<String, Object> cachedValues = new HashMap<>();

    public PluginConfig(Configuration configuration) {
        Validate.notNull(configuration, "Plugin config cannot be null");
        this.configuration = configuration;
    }

    public void validate() {
        if (StringUtils.isBlank(getPluginName()))
            throw new HareException(CommonErrorCode.PLUGIN_MISSING_CONFIG, PluginConstants.PLUGIN_NAME);
        if (StringUtils.isBlank(getPluginVersion()))
            throw new HareException(CommonErrorCode.PLUGIN_MISSING_CONFIG, PluginConstants.PLUGIN_VERSION);
        if (getPluginType() == null)
            throw new HareException(CommonErrorCode.PLUGIN_MISSING_CONFIG, PluginConstants.PLUGIN_TYPE);
        if (PluginType.connector == getPluginType() && StringUtils.isBlank(getPluginDatasourceType())) {
            throw new HareException(CommonErrorCode.PLUGIN_MISSING_CONFIG, PluginConstants.PLUGIN_DATASOURCE_TYPE);
        }
        if (StringUtils.isBlank(getPluginPath()))
            throw new HareException(CommonErrorCode.PLUGIN_MISSING_CONFIG, PluginConstants.PLUGIN_PATH);
        if (PluginType.connector == getPluginType() && getPluginConnectorFormConfig() == null)
            throw new HareException(CommonErrorCode.PLUGIN_MISSING_CONFIG, PluginConstants.PLUGIN_CONNECTOR_FORM_CONFIG);
        if (PluginType.connector == getPluginType() && getPluginConnectorJsonSchema() == null)
            throw new HareException(CommonErrorCode.PLUGIN_MISSING_CONFIG, PluginConstants.PLUGIN_CONNECTOR_JSON_SCHEMA);

        if (getPluginParameterFormConfig() == null)
            throw new HareException(CommonErrorCode.PLUGIN_MISSING_CONFIG, PluginConstants.PLUGIN_PARAMETER_FORM_CONFIG);
        if (getPluginParameterJsonSchema() == null)
            throw new HareException(CommonErrorCode.PLUGIN_MISSING_CONFIG, PluginConstants.PLUGIN_PARAMETER_JSON_SCHEMA);


        Path iconPath = getPluginIconPath();
        if (!iconPath.toFile().exists())
            throw new HareException(CommonErrorCode.PLUGIN_MISSING_ICON, iconPath.getFileName());
    }

    public String getPluginKey() {
        return generatePluginKey(getPluginName(), getPluginVersion());
    }

    public String getPluginName() {
        return configuration.getString(PluginConstants.PLUGIN_NAME);
    }
    public String getPluginVersion() {
        return configuration.getString(PluginConstants.PLUGIN_VERSION, "0.0.1");
    }
    public Boolean getPluginLatest() {
        return configuration.getBool(PluginConstants.PLUGIN_LATEST, true);
    }
    public String getPluginDescription() {
        return configuration.getString(PluginConstants.PLUGIN_DESCRIPTION);
    }

    public String getPluginDatasourceType() {
        return configuration.getString(PluginConstants.PLUGIN_DATASOURCE_TYPE);
    }

    public PluginType getPluginType() {
        String pluginType = configuration.getString(PluginConstants.PLUGIN_TYPE);
        if ( StringUtils.isNotEmpty(pluginType))
            return PluginType.valueOf(pluginType);
        return null;
    }

    public ScopeType getPluginScopeType() {
        String scopeType = configuration.getString(PluginConstants.PLUGIN_SCOPE_TYPE);
        if ( StringUtils.isBlank(scopeType))
            return ScopeType.PRIVATE;
        else
            return ScopeType.valueOf(scopeType);
    }

    public Map<String, String> getPluginTags() {
        return configuration.getMap(PluginConstants.PLUGIN_TAGS, String.class);
    }

    public String getPluginDeveloper() {
        return configuration.getString(PluginConstants.PLUGIN_DEVELOPER);
    }
    public String getPluginEmail() {
        return configuration.getString(PluginConstants.PLUGIN_EMAIL);
    }
    public String getPluginClasspath() {
        return configuration.getString(PluginConstants.PLUGIN_CLASSPATH);
    }

    public Path getPluginIconPath() {
        return Path.of(getPluginPath(), configuration.getString(PluginConstants.PLUGIN_ICON, "icon.svg"));
    }

    public String getPluginPath() {
        return configuration.getString(PluginConstants.PLUGIN_PATH);
    }

    public Map<String, Object> getPluginConnectorFormConfig() {
        return configuration.getMap(PluginConstants.PLUGIN_CONNECTOR_FORM_CONFIG);
    }

    public Map<String, Object> getPluginConnectorJsonSchema() {
        return configuration.getMap(PluginConstants.PLUGIN_CONNECTOR_JSON_SCHEMA);
    }

    public Map<String, Object> getPluginParameterFormConfig() {
        return configuration.getMap(PluginConstants.PLUGIN_PARAMETER_FORM_CONFIG);
    }

    public Map<String, Object> getPluginParameterJsonSchema() {
        return configuration.getMap(PluginConstants.PLUGIN_PARAMETER_JSON_SCHEMA);
    }

    /**
     * get ref file content
     * Deprecated
     * @see com.qz.hare.common.util.PluginUtils#loadReferenceConfig
     * @param configName config name
     * @return ref file content
     */
    @Deprecated
    private String getStringOrFileContent(String configName) {
        if(cachedValues.containsKey(configName))
            return (String) cachedValues.get(configName);

        String formConfig = configuration.getString(configName);
        if (formConfig != null && formConfig.startsWith("@")) {
            File file = Path.of(getPluginPath(), formConfig.substring(1)).toFile();
            if (!file.exists())
                throw new HareException(CommonErrorCode.PLUGIN_NOT_FOUND_FILE, formConfig);
            String content = Configuration.from(file).toJSON();
            cachedValues.put(configName, content);
            return content;
        }
        return formConfig;
    }
}
