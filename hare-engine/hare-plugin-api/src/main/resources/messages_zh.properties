NotSupportDiscoverDatabase=\u8fde\u63a5\u5668\u4e0d\u652f\u6301\u53d1\u73b0\u6570\u636e\u5e93
NotSupportListTable=\u8fde\u63a5\u5668\u4e0d\u652f\u6301\u5217\u8868\u8868
NotSupportDiscoverTable=\u8fde\u63a5\u5668\u4e0d\u652f\u6301\u53d1\u73b0\u8868
NotSupportDiscoverIndex=\u8fde\u63a5\u5668\u4e0d\u652f\u6301\u53d1\u73b0\u7d22\u5f15
RequiredHost=\u8fde\u63a5\u4e3b\u673a\u5730\u5740\u4e0d\u80fd\u4e3a\u7a7a\u3002
PortOutOfRange=\u8fde\u63a5\u7aef\u53e3\u8d85\u51fa\u8303\u56f4 1-65535\u3002
HostPortValid=\u4e3b\u673a\u5730\u5740\u548c\u7aef\u53e3\u6709\u6548({0}:{1})\u3002
HostPortInvalid=\u4e3b\u673a\u5730\u5740\u6216\u7aef\u53e3\u65e0\u6548({0}:{1})\uff0c\u8bf7\u68c0\u67e5\uff1a{2}
ConnectValid=\u8fde\u63a5\u6570\u636e\u6e90\u6210\u529f\u3002
ConnectInvalid=\u65e0\u6cd5\u4e0e\u6570\u636e\u6e90\u521b\u5efa\u8fde\u63a5\uff1a{0}
GetVersionFailed=\u65e0\u6cd5\u4ece\u6570\u636e\u6e90\u83b7\u53d6\u7248\u672c\u3002
VersionValid={0} {1}
VersionInvalidBetween=\u4ec5\u652f\u6301 {0} \u5230 {1} \u4e4b\u95f4\u7684\u7248\u672c\uff0c\u5f53\u524d\u7248\u672c\u4e3a {2}
VersionInvalidLow=\u4ec5\u652f\u6301\u5927\u4e8e\u6216\u7b49\u4e8e {0} \u7684\u7248\u672c\uff0c\u5f53\u524d\u7248\u672c\u4e3a {1}
VersionInvalidHigh=\u4ec5\u652f\u6301\u5c0f\u4e8e\u6216\u7b49\u4e8e {0} \u7684\u7248\u672c\uff0c\u5f53\u524d\u7248\u672c\u4e3a {1}
WritePrivilegeValid=\u5f53\u524d\u6743\u9650\uff1a{0}
WritePrivilegeNotEnough=\u7528\u6237\u6ca1\u6709\u8db3\u591f\u7684\u5199\u5165\u6743\u9650\uff0c\u6211\u4eec\u9700\u8981 {0} \u6743\u9650
ReadPrivilegeNotEnough=\u7528\u6237\u6ca1\u6709\u8db3\u591f\u7684\u8bfb\u53d6\u6743\u9650\uff0c\u6211\u4eec\u9700\u8981 {0} \u6743\u9650
CdcCanBeUsed=\u5df2\u542f\u7528\u3002
CdcPrivilegeValid=\u5f53\u524d\u7528\u6237\u6709\u6743\u8bfb\u53d6\u589e\u91cf\u6570\u636e\u3002
CdcPrivilegeInvalid=\u5f53\u524d\u7528\u6237\u65e0\u6743\u8bfb\u53d6\u589e\u91cf\u6570\u636e\u3002
