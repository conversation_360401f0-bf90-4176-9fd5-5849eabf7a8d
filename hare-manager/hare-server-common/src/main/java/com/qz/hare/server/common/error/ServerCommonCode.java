package com.qz.hare.server.common.error;

import com.qz.hare.common.exception.ErrorCode;

/**
 * <AUTHOR>
 * create at 2024/10/17 21:06
 */
public enum ServerCommonCode implements ErrorCode {

    EMPTY_PIPELINE("EmptyPipeline", "Job data pipeline cannot be empty"),
    INVALID_PIPELINE("InvalidPipeline", "Job data pipeline configuration is invalid {0}"),
    PIPELINE_CYCLE("PipelineCycle", "Job data pipeline cannot have cycle {0}"),
    MULTIPLE_PIPELINE("MultiplePipeline", "Job data pipeline cannot have multiple pipeline instances");

    private final String code;

    private final String describe;

    ServerCommonCode(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDescription() {
        return this.describe;
    }

    @Override
    public String toString() {
        return toStringFormat();
    }
}
