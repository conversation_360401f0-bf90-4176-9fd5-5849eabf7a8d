package com.qz.hare.server.common.model;

import com.qz.hare.common.util.ThreadLocalUtils;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

import static com.qz.hare.common.util.IDUtils.generatorUUID;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/9/11 9:24 上午
 * @description
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class ResponseMessage<T> {

	public static final String OK = "ok";

	private String reqId;

	private long ts = new Date().getTime();

	/**
	 * 请求处理的代码
	 */
	protected String code = OK;

	/**
	 * 请求处理失败时的错误消息
	 */
	protected String message;

	/**
	 * 请求处理成功的数据
	 */
	protected T data;

	protected String errorStack;

	public ResponseMessage() {
		Object obj = ThreadLocalUtils.get(ThreadLocalUtils.REQUEST_ID);
		if (obj == null) {
			this.reqId = generatorUUID();
		} else {
			this.reqId = obj.toString();
		}
	}

}
