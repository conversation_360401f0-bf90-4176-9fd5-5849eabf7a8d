package com.qz.hare.server.common.model.engine;

import com.qz.hare.server.common.model.BaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.bson.types.ObjectId;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2022/8/12 下午3:24
 */
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class Engine extends BaseDto<ObjectId> {
    private String engineId;

    private Map<String, String> labels;

    public List<String> getStringLabels() {
        if (labels == null || labels.isEmpty())
            return Collections.emptyList();
        return labels.entrySet().stream()
                .map(e -> String.format("%s=%s", e.getKey(), e.getValue()))
                .toList();
    }
}
