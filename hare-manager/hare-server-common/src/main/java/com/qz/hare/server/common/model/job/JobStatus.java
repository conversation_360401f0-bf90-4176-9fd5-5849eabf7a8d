package com.qz.hare.server.common.model.job;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/1/12 16:17
 */
public enum JobStatus {
    EDITING,            // 编辑中
    SCHEDULE_FAILED,     // 调度失败
    SCHEDULE_SUCCESS,    // 调度成功
    WAITING_START,      // 启动中，将任务分配给engine，并且engine还未运行起来任务时的状态
    WAITING_NEXT_RUN,   // 等待下次运行，定时执行任务用户启动后后，等待运行时的状态
    RUNNING,            // 运行中，正在运行的任务
    STOPPING,           // 停止中，用户点击停止任务后，引擎还没有停下任务之前
    COMPLETE,           // 运行完成，全量任务运行完成后的状态
    STOPPED,            // 已停止，用户点击停止任务，引擎已经将任务停下来
    ERROR,              // 错误
    RESETTING,          // 重置中
    RESET_FAILED,       // 重置失败
    DELETED;            // 已删除

    /**
     * Unable to delete status
     * @return list of status
     */
    public static List<JobStatus> unableToDeleteStatus() {
        return List.of(JobStatus.RESETTING, JobStatus.RUNNING, JobStatus.STOPPING);
    }

    public static List<JobStatus> canEditingStatus() {
        return List.of(EDITING, SCHEDULE_FAILED, COMPLETE, STOPPED, ERROR, RESET_FAILED, DELETED);
    }
}
