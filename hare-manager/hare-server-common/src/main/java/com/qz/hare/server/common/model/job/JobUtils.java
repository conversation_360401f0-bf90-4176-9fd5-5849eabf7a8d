package com.qz.hare.server.common.model.job;

import com.qz.hare.common.exception.CommonErrorCode;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.server.common.error.ServerCommonCode;
import com.qz.hare.server.common.model.job.dag.Dag;
import com.qz.hare.server.common.model.job.dag.Edge;
import com.qz.hare.server.common.model.job.dag.Node;
import com.qz.hare.server.common.model.plugin.PluginStore;
import io.github.openlg.graphlib.Graph;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2025/6/24 07:45
 */
public class JobUtils {

    private JobUtils() {}

    public static Graph<Node, Edge> buildGraph(Dag dag) {
        Graph<Node, Edge> graph = new Graph<>();
        for (Node node : dag.getNodes()) {
            graph.setNode(node.getId(), node);
        }
        for (Edge edge : dag.getEdges()) {
            graph.setEdge(edge.getSrc(), edge.getDst(), edge);
        }
        return graph;
    }

    /**
     * check dag rules
     * @param graph graph
     */
    public static void validateDag(Graph<Node, Edge> graph) {
        if (graph == null)
            throw new HareException(CommonErrorCode.REQUIRED_VALUE, "graph");

        if (graph.components().size() > 1) {
            throw new HareException(ServerCommonCode.MULTIPLE_PIPELINE);
        }

        List<List<String>> cycles = graph.findCycles();
        if (cycles != null && !cycles.isEmpty()) {
            String cycleIds = cycles.stream().map(r -> String.join(", ", r)).collect(Collectors.joining(";"));
            throw new HareException(ServerCommonCode.PIPELINE_CYCLE, cycleIds);
        }
    }

    /**
     * check node configs
     * @param graph graph
     * @param pluginStore plugin store
     */
    public static void validateNodeConfig(Graph<Node, Edge> graph, PluginStore pluginStore) {
        if (graph == null)
            throw new HareException(CommonErrorCode.REQUIRED_VALUE, "graph");

        graph.getNodes().stream().map(graph::getNode).forEach(node -> node.validate(pluginStore));
    }

    public static void validateJobConfiguration(Job job, PluginStore pluginStore) {
        if (pluginStore == null)
            throw new HareException(CommonErrorCode.REQUIRED_VALUE, "pluginRegistry");
        if (job == null)
            throw new HareException(CommonErrorCode.REQUIRED_VALUE, "job");

        Dag dag = job.getDag();
        if (dag == null || dag.getNodes() == null || dag.getNodes().isEmpty() || dag.getEdges() == null || dag.getEdges().isEmpty()) {
            throw new HareException(ServerCommonCode.EMPTY_PIPELINE);
        }

        Graph<Node, Edge> graph = buildGraph(dag);
        // 1. Check dag settings
        validateDag(graph);

        // 2. Check node settings
        validateNodeConfig(graph, pluginStore);
    }

}
