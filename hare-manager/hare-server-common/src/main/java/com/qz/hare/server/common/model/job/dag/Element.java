package com.qz.hare.server.common.model.job.dag;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qz.hare.server.common.model.plugin.PluginKey;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2025/1/13 19:01
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class Element implements Serializable {
    private String id;
    private String name;

    private PluginKey pluginKey;

    private Map<String, Object> params;

    @JsonIgnore
    @Schema(hidden = true)
    public abstract ElementType getElementType();
}
