package com.qz.hare.server.common.model.job.dag;

import com.networknt.schema.ValidationMessage;
import com.qz.hare.common.exception.CommonErrorCode;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.common.util.JsonUtil;
import com.qz.hare.server.common.model.plugin.PluginStore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2025/1/13 19:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Slf4j
public class Node extends Element {

    private String type;

    public boolean validate(PluginStore pluginStore) throws HareException {

        if (StringUtils.isBlank(getId()))
            throw new HareException(CommonErrorCode.REQUIRED_VALUE, "node id");
        if (StringUtils.isBlank(getName()))
            throw new HareException(CommonErrorCode.REQUIRED_VALUE, "node name");
        if (StringUtils.isBlank(type))
            throw new HareException(CommonErrorCode.REQUIRED_VALUE, String.format("node %s type", getName()));

        var params = getParams();
        if (params == null || params.isEmpty())
            throw new HareException(CommonErrorCode.REQUIRED_VALUE, String.format("node %s params", getName()));

        if (getPluginKey() == null)
            throw new HareException(CommonErrorCode.REQUIRED_VALUE, String.format("node %s plugin", getName()));

        var jsonSchema = pluginStore.getPluginParameterFormJsonSchema(getPluginKey());
        if (jsonSchema != null) {
            Set<ValidationMessage> validateMessages = jsonSchema.validate(JsonUtil.convertToJsonNode(params));

            if (validateMessages != null && !validateMessages.isEmpty()) {
                var errorMessage = validateMessages.stream()
                        .map(m -> String.format("%s: (%s)%s", m.getProperty(), m.getCode(), m.getMessage()))
                        .collect(Collectors.joining(","));
                throw new HareException(CommonErrorCode.PLUGIN_PARAMS_VALIDATE_ERROR, errorMessage);
            }
        } else {
            log.warn("Not found plugin json schema for {}, skip validate form config", getPluginKey());
        }

        return true;
    }

    @Override
    public ElementType getElementType() {
        return ElementType.NODE;
    }
}
