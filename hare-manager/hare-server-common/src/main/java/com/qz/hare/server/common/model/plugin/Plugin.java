package com.qz.hare.server.common.model.plugin;

import com.qz.hare.model.plugin.PluginType;
import com.qz.hare.model.plugin.ScopeType;
import com.qz.hare.server.common.model.BaseDto;
import com.qz.hare.server.common.model.file.FileItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.bson.types.ObjectId;

import java.util.Map;

/**
 * <AUTHOR>
 * create at 2024/10/17 09:18
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@SuperBuilder
@Schema
@AllArgsConstructor
@NoArgsConstructor
public class Plugin extends BaseDto<ObjectId> {

    private String name;
    private String version;
    private Boolean latest;
    private String developer;
    private String email;
    private String description;

    /**
     * 相比name，更标准简化，用于区分不同种类的数据源，例如 mysql,mongodb 等;
     * 同一个 connector 在不同的版本中可以更改name，但不能修改 type，否则识别为新的数据源种类；
     * datasourceType + version 唯一
     */
    private String datasourceType;
    /**
     * 插件类型，connector 或者 transformer
     */
    private PluginType pluginType;

    private FileItem iconFile;
    private FileItem pluginFile;

    private Map<String, Object> connectorFormConfig;    // 创建数据源连接的配置，pluginType == connector 时有效，transformer 直接初始化使用即可，无需预先创建，因此不需要createFormConfig
    private Map<String, Object> connectorFormJsonSchema;// 数据源连接的参数描述

    private Map<String, Object> parameterFormConfig;    // 构建Pipeline时的表单参数配置
    private Map<String, Object> parameterFormJsonSchema;// 构建Pipeline的表单参数描述

    private DependVersion depend;       // 依赖引擎版本

    private Map<String, Object> metadata;
    private Map<String, String> tags;

    private ScopeType scopeType;

    public PluginKey getPluginKey() {
        return PluginKey.builder()
                .name(this.getName())
                .version(this.getVersion())
                .pluginType(this.getPluginType())
                .scopeType(this.getScopeType())
                .build();
    }

}
