package com.qz.hare.server.common.model.plugin;

import com.qz.hare.model.plugin.PluginType;
import com.qz.hare.model.plugin.ScopeType;
import lombok.*;

/**
 * <AUTHOR>
 * create at 2025/6/30 16:55
 */
@Data
@Builder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PluginKey {
    private String name;
    private String version;
    private ScopeType scopeType;
    private PluginType pluginType;
    private String projectId;
    private Long checksum;
}
