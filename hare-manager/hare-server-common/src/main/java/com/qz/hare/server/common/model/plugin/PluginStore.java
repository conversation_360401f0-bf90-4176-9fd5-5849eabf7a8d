package com.qz.hare.server.common.model.plugin;

import com.networknt.schema.JsonSchema;

/**
 * <AUTHOR>
 * create at 2025/6/27 08:31
 */
public interface PluginStore {

    /**
     * Get plugin configuration json schema
     * @param pluginKey key for plugin
     * @return json schema
     */
    JsonSchema getPluginConnectorFormJsonSchema(PluginKey pluginKey);

    /**
     * Get plugin configuration json schema
     * @param pluginKey key for plugin
     * @return json schema
     */
    JsonSchema getPluginParameterFormJsonSchema(PluginKey pluginKey);

}
