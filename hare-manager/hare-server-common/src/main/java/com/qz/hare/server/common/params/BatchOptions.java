package com.qz.hare.server.common.params;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.bson.types.ObjectId;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * create at 2024/12/2 08:49
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BatchOptions {
    private List<String> ids;

    public static List<ObjectId> getObjectIds(BatchOptions batchOptions) {
        return Optional.ofNullable(batchOptions)
                .map(BatchOptions::getIds)
                .orElse(Collections.emptyList())
                .stream().map(ObjectId::new).toList();
    }
}
