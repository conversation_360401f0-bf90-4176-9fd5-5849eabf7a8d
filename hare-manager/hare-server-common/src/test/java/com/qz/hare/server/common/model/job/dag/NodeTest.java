package com.qz.hare.server.common.model.job.dag;

import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SpecVersion;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.common.util.ClassUtils;
import com.qz.hare.server.common.model.plugin.PluginKey;
import com.qz.hare.server.common.model.plugin.PluginStore;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * create at 2025/6/27 14:15
 */
public class NodeTest {

    @Test
    public void testValidate() {
        Node node = new Node();
        node.setId("id");
        node.setName("name");
        node.setType("type");
        node.setParams(Map.of(
                "test", "test",
                "id", "68620308e95bab5d4823e891"
        ));

        JsonSchema jsonSchema = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V7)
                .getSchema(ClassUtils.getResourceAsStream("json-schema.json"));

        PluginStore pluginStore = mock(PluginStore.class);
        when(pluginStore.getPluginParameterFormJsonSchema(any(PluginKey.class))).thenReturn(jsonSchema);

        Assertions.assertThrows(HareException.class, () -> {
            node.validate(pluginStore);
        });
    }

}
