<assembly
        xmlns="http://maven.apache.org/ASSEMBLY/2.1.1"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema"
        xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/2.1.1 https://maven.apache.org/xsd/assembly-2.1.1.xsd">
    <id>hare-server</id>
    <formats>
        <format>dir</format>
        <!--<format>tar.gz</format>-->
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <!-- for bin -->
        <fileSet>
            <directory>src/main/bin</directory>
            <includes>
                <include>*.*</include>
            </includes>
            <!--<excludes>
                <exclude>*.pyc</exclude>
            </excludes>-->
            <directoryMode>775</directoryMode>
            <outputDirectory>/bin</outputDirectory>
        </fileSet>
        <!-- for scripts -->
        <!--<fileSet>
            <directory>src/main/script</directory>
            <includes>
                <include>*.*</include>
            </includes>
            <directoryMode>775</directoryMode>
            <outputDirectory>/script</outputDirectory>
        </fileSet>-->
        <!-- for configs -->
        <fileSet>
            <directory>src/main/conf</directory>
            <includes>
                <include>*.*</include>
            </includes>
            <outputDirectory>/conf</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>target/classes</directory>
            <includes>
                <include>application.yml</include>
                <include>logback.xml</include>
            </includes>
            <outputDirectory>/conf</outputDirectory>
        </fileSet>
        <!-- for server -->
        <fileSet>
            <directory>target/</directory>
            <includes>
                <include>hare-server-*.jar</include>
            </includes>
            <outputDirectory>/lib</outputDirectory>
        </fileSet>

        <!--<fileSet>-->
            <!--<directory>src/main/log/</directory>-->
            <!--<includes>-->
                <!--<include>*.log</include>-->
            <!--</includes>-->
            <!--<outputDirectory>/log</outputDirectory>-->
        <!--</fileSet>-->

        <!--<fileSet>-->
            <!--<directory>src/main/log/</directory>-->
            <!--<includes>-->
                <!--<include>*.log</include>-->
            <!--</includes>-->
            <!--<outputDirectory>/log_perf</outputDirectory>-->
        <!--</fileSet>-->

        <!--<fileSet>
            <directory>src/main/jobRunner/</directory>
            <includes>
                <include>*.json</include>
            </includes>
            <outputDirectory>/jobRunner</outputDirectory>
        </fileSet>-->

        <!--<fileSet>
            <directory>src/main/tools/</directory>
            <includes>
                <include>*.*</include>
            </includes>
            <outputDirectory>/tools</outputDirectory>
        </fileSet>-->

        <fileSet>
            <fileMode>777</fileMode>
            <directory>src/main/tmp</directory>
            <includes>
                <include>*.*</include>
            </includes>
            <outputDirectory>/tmp</outputDirectory>
        </fileSet>
    </fileSets>

    <!--<dependencySets>
        <dependencySet>
            <useProjectArtifact>false</useProjectArtifact>
            <outputDirectory>/lib</outputDirectory>
            <scope>runtime</scope>
        </dependencySet>
    </dependencySets>-->
</assembly>
