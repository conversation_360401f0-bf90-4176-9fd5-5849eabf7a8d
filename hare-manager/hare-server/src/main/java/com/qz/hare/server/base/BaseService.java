package com.qz.hare.server.base;

import com.mongodb.client.result.UpdateResult;
import com.qz.hare.server.base.entity.BaseEntity;
import com.qz.hare.server.common.model.BaseDto;
import com.qz.hare.server.common.model.Update;
import com.qz.hare.server.common.params.Field;
import com.qz.hare.server.common.params.Filter;
import com.qz.hare.server.common.params.Page;
import com.qz.hare.server.common.params.Where;
import com.qz.hare.server.config.security.UserDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

import static com.qz.hare.server.base.entity.BaseEntity.FIELD_NAME_DELETED;

/**
 * <AUTHOR> @ gmail.com>
 *  create at 2020/9/11 4:29 下午
 */
@Slf4j
public abstract class BaseService<D extends BaseDto<ID>,
        E extends BaseEntity<?>, ID extends Serializable, R extends BaseRepository<E, ID>> {

    protected R repository;
    protected Class<D> dtoClass;
    protected Class<E> entityClass;

    public BaseService(R repository) {
        this.repository = repository;

        Type[] actualTypes = ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments();
        if (actualTypes == null || actualTypes.length < 4) {
            throw new IllegalArgumentException("Generics parameter must be specified when extending BaseService<Dto, Entity, ID, Repository>");
        }
        //noinspection unchecked
        this.dtoClass = (Class<D>) actualTypes[0];
        this.entityClass = repository.getEntityClass();
    }

    protected void filterDeletedInQuery(Criteria criteria) {
        if (criteria == null)
            return;
        criteria.and(FIELD_NAME_DELETED).ne(true);
    }
    protected void filterDeletedInWhere(Where where) {
        if (where == null)
            return;
        where.and(FIELD_NAME_DELETED, Where.where("$ne", true));
    }

    /**
     * Paging query
     *
     * @param filter optional, page query parameters
     * @return the Page of current page, include page data and total size.
     */
    public Page<D> find(Filter filter, UserDetail userDetail) {

        List<E> entityList = repository.findAll(filter, userDetail);

        long total = repository.count(filter != null ? filter.getWhere() : null, userDetail);

        List<D> items = convertToDto(entityList, dtoClass, "password");

        return new Page<>(total, items);
    }


    /**
     * Paging query
     *
     * @param filter optional, page query parameters
     * @return the Page of current page, include page data and total size.
     */
    public Page<D> find(Filter filter, String excludeField, UserDetail userDetail) {

        List<E> entityList = repository.findAll(filter,excludeField, userDetail);

        long total = repository.count(filter != null ? filter.getWhere() : null, userDetail);

        List<D> items = convertToDto(entityList, dtoClass, "password");

        return new Page<>(total, items);
    }

    /**
     * Paging query
     *
     * @param filter optional, page query parameters
     * @return the Page of current page, include page data and total size.
     */
    public Page<D> find(Filter filter) {

        List<E> entityList = repository.findAll(filter);

        long total = repository.count(filter != null ? filter.getWhere() : null);

        List<D> items = convertToDto(entityList, dtoClass, "password");

        return new Page<>(total, items);
    }

    public List<E> findAll(Query query, UserDetail userDetail) {
        return repository.findAll(query, userDetail);
    }

    public List<E> findAll(Query query, Field fields, UserDetail userDetail) {
        return repository.findAll(query, fields, userDetail);
    }

    public List<D> findAllDto(Query query, UserDetail userDetail) {
        return repository.findAll(query, userDetail).stream().map( entity -> convertToDto(entity, dtoClass)).toList();
    }


    public List<D> findAll(Query query) {
        return repository.findAll(query).stream().map(entity -> convertToDto(entity, dtoClass)).toList();
    }

    public List<E> findAll(UserDetail userDetail) {

        return repository.findAll(userDetail);
    }

    /**
     * Save the object to the collection for the entity type of the object to save. This will perform an insert if the
     * object is not already present, that is an 'upsert'.
     *
     * @param dto required
     * @return Data after persistence
     */
    public <T extends BaseDto> D save(D dto, UserDetail userDetail) {

        Assert.notNull(dto, "Dto must not be null!");

        beforeSave(dto, userDetail);

        E entity = convertToEntity(entityClass, dto);

        entity = repository.save(entity, userDetail);

        BeanUtils.copyProperties(entity, dto);

        return dto;
    }

    public <T extends BaseDto> List<D> save(List<D> dtoList, UserDetail userDetail) {
        Assert.notNull(dtoList, "Dto must not be null!");

        List<E> entityList = new ArrayList<>();
        for (D dto : dtoList) {
            beforeSave(dto, userDetail);

            E entity = convertToEntity(entityClass, dto);
            entityList.add(entity);
        }

        entityList = repository.saveAll(entityList, userDetail);

        dtoList = convertToDto(entityList, dtoClass);

        return dtoList;
    }

    protected void beforeSave(D dto, UserDetail userDetail){

    }

    public boolean deleteById(ID id, UserDetail userDetail) {

        Assert.notNull(id, "Id must not be null!");
        return repository.deleteById(id, userDetail);
    }

    public boolean deleteById(ID id) {

        Assert.notNull(id, "Id must not be null!");
        return repository.deleteById(id);
    }

    public UpdateResult deleteLogicsById(String id) {
        Assert.notNull(id, "Id must not be null!");
        org.springframework.data.mongodb.core.query.Update update = org.springframework.data.mongodb.core.query.Update.update("is_deleted", true);
        Query query = new Query();
        query.addCriteria(Criteria.where("id").is(id));
        UpdateResult updateResult = repository.getMongoOperations().updateMulti(query, update, entityClass);
        return updateResult;
    }


    /**
     * find model by id
     *
     * @param id
     * @param userDetail
     * @return
     */
    public D findById(ID id, UserDetail userDetail) {
        Assert.notNull(id, "Id must not be null!");
        Optional<E> entity = repository.findById(id, userDetail);
        return entity.map(value -> convertToDto(value, dtoClass)).orElse(null);
    }

//	public Dto findById(String id) {
//		Assert.notNull(id, "Id must not be null!");
//		Query query=new Query().addCriteria(Criteria.where("_id").is(id));
//		Optional<Entity> entity = repository.findOne(query);
//		return entity.map(value -> convertToDto(value, dtoClass)).orElse(null);
//	}


    /**
     * find model by id
     *
     * @param id
     * @param userDetail
     * @return
     */
    public D findById(ID id, Field field, UserDetail userDetail) {
        Assert.notNull(id, "Id must not be null!");
        Optional<E> entity = repository.findById(id, field, userDetail);
        return entity.map(value -> convertToDto(value, dtoClass)).orElse(null);
    }

    /**
     * find model by id
     *
     * @param id id
     * @return
     */
    public D findById(ID id) {
        return findById(id, new Field());
    }


    public D findById(ID id, Field field) {
        Assert.notNull(id, "Id must not be null!");
        Optional<E> entity = repository.findById(id, field);
        return entity.map(value -> convertToDto(value, dtoClass)).orElse(null);
    }

    /**
     * find one model
     *
     * @param query query
     * @param userDetail user detail
     * @return
     */
    public D findOne(Query query, UserDetail userDetail) {
        return repository.findOne(query, userDetail).map(entity -> convertToDto(entity, dtoClass)).orElse(null);
    }

    /**
     * find one model
     *
     * @param query query
     * @return
     */
    public D findOne(Query query) {
        return repository.findOne(query).map(entity -> convertToDto(entity, dtoClass)).orElse(null);
    }
    /**
     * find one model
     *
     * @param query query
     * @return
     */
    public D findOne(Query query, String excludeField) {
        query.fields().exclude(excludeField);
        return repository.findOne(query).map(entity -> convertToDto(entity, dtoClass)).orElse(null);
    }

    /**
     * find one model
     *
     * @param filter filter
     * @param userDetail user detail
     * @return dto
     */
    public D findOne(Filter filter, UserDetail userDetail) {
        Query query = repository.filterToQuery(filter);
        return findOne(query, userDetail);
    }

    /**
     * Convert DB Entity to Dto
     *
     * @param entityList       required, the record List of entity.
     * @param dtoClass         required, the Class of Dto.
     * @param ignoreProperties optional, fields that do not need to be processed during conversion.
     * @return the List of converted.
     */
    public List<D> convertToDto(List<E> entityList, Class<D> dtoClass, String... ignoreProperties) {
        if (entityList == null)
            return null;

        return entityList.stream().map(entity -> convertToDto(entity, dtoClass, ignoreProperties)).toList();
    }

    /**
     * Convert DB Entity to Dto
     *
     * @param entityList       required, the record List of entity.
     * @param ignoreProperties optional, fields that do not need to be processed during conversion.
     * @return the List of converted.
     */
    public List<D> convertToDto(List<E> entityList, String... ignoreProperties) {
        if (entityList == null)
            return null;
        return entityList.stream().map(entity -> convertToDto(entity, dtoClass, ignoreProperties)).toList();
    }

    /**
     * Convert DB Entity to Dto
     *
     * @param entity           required, the record of Entity.
     * @param ignoreProperties optional, fields that do not need to be processed during conversion.
     * @return the Dto of converted.
     */
    public D convertToDto(E entity, String... ignoreProperties) {
        return convertToDto(entity, dtoClass, ignoreProperties);
    }

    /**
     * Convert DB Entity to Dto
     *
     * @param entity           required, the record of Entity.
     * @param dtoClass         required, the Class of Dto.
     * @param ignoreProperties optional, fields that do not need to be processed during conversion.
     * @return the Dto of converted.
     */
    public <T extends BaseDto> T convertToDto(E entity, Class<T> dtoClass, String... ignoreProperties) {
        if (dtoClass == null || entity == null)
            return null;

        try {
            T target = dtoClass.getDeclaredConstructor().newInstance();

            BeanUtils.copyProperties(entity, target, ignoreProperties);

            return target;
        } catch (Exception e) {
            log.error("Convert dto " + dtoClass + " failed.", e);
        }
        return null;
    }

    /**
     * Convert Dto to DB Entity.
     *
     * @param entityClass      required, the Class of entity.
     * @param dtoList          required, the record list of dto.
     * @param ignoreProperties optional, fields that do not need to be processed during conversion.
     * @return the List of converted.
     */
    public <T extends BaseDto> List<E> convertToEntity(Class<E> entityClass, List<T> dtoList, String... ignoreProperties) {
        if (dtoList == null)
            return null;

        return dtoList.stream().map(dto -> convertToEntity(entityClass, dto, ignoreProperties)).toList();
    }

    /**
     * Convert Dto to DB Entity
     *
     * @param entityClass      required, the Class of entity
     * @param dto              required, the record of dto.
     * @param ignoreProperties optional, fields that do not need to be processed during conversion.
     * @return the List of converted.
     */
    public <T extends BaseDto> E convertToEntity(Class<E> entityClass, T dto, String... ignoreProperties) {

        if (entityClass == null || dto == null)
            return null;

        try {
            E entity = entityClass.getDeclaredConstructor().newInstance();

            BeanUtils.copyProperties(dto, entity, ignoreProperties);

            return entity;
        } catch (Exception e) {
            log.error("Convert entity " + entityClass + " failed.", e);
        }
        return null;
    }

    public UpdateResult updateById(ID id, org.springframework.data.mongodb.core.query.Update update, UserDetail userDetail) {
        Assert.notNull(id, "Id must not be null!");

        return repository.updateFirst(new Query(Criteria.where("_id").is(id)), update, userDetail);
    }

    public UpdateResult updateById(String id, org.springframework.data.mongodb.core.query.Update update, UserDetail userDetail) {
        Assert.notNull(id, "Id must not be null!");

        return repository.updateFirst(new Query(Criteria.where("_id").is(id)), update, userDetail);
    }


    public long updateByWhere(Where where, Update<D> dto, UserDetail userDetail) {
        Filter filter = new Filter(where);
        Query query = repository.filterToQuery(filter);
        E set = convertToEntity(entityClass, dto.getSet());
        E setOnInsert = convertToEntity(entityClass, dto.getSetOnInsert());
        return repository.updateByWhere(query, set, setOnInsert, dto.getUnset(), userDetail).getModifiedCount();
    }

    public long updateByWhere(Where where, Document doc, UserDetail userDetail) {
        Filter filter = new Filter(where);
        Query query = repository.filterToQuery(filter);
        //Entity set = convertToEntity(entityClass, dto.getSet());
        //Entity setOnInsert = convertToEntity(entityClass, dto.getSetOnInsert());

        return repository.update(query, org.springframework.data.mongodb.core.query.Update.fromDocument(doc), userDetail).getModifiedCount();
        // return repository.updateByWhere(query, set, setOnInsert, dto.getUnset(), userDetail).getModifiedCount();
    }

    public long updateByWhere(Where where, D dto, UserDetail userDetail) {

        beforeSave(dto, userDetail);
        Filter filter = new Filter(where);
        Query query = repository.filterToQuery(filter);
        E entity = convertToEntity(entityClass, dto);
        UpdateResult updateResult = repository.updateByWhere(query, entity, userDetail);
        return updateResult.getModifiedCount();
    }

    public long updateByWhere(Query query, D dto, UserDetail userDetail) {
        beforeSave(dto, userDetail);
        E entity = convertToEntity(entityClass, dto);
        UpdateResult updateResult = repository.updateByWhere(query, entity, userDetail);
        return updateResult.getModifiedCount();
    }

    public <T extends BaseDto> D upsert(Query query, T dto, UserDetail userDetail) {

        E result = repository.upsert(query, convertToEntity(entityClass, dto), userDetail);

        return convertToDto(result);
    }

    public <T extends BaseDto> D upsert(Query query, T dto) {

        E result = repository.upsert(query, convertToEntity(entityClass, dto));
        return convertToDto(result);
    }

    public D upsertByWhere(Where where, D dto, UserDetail userDetail) {

        beforeSave(dto, userDetail);
        Filter filter = new Filter(where);
        Query query = repository.filterToQuery(filter);
        E result = repository.upsert(query, convertToEntity(entityClass, dto), userDetail);

        return convertToDto(result);
    }

    public List<D> findAll(Where where, UserDetail userDetail) {
        List<E> entities = repository.findAll(where, userDetail);
        return convertToDto(entities, dtoClass, "password");
    }

    public List<D> findAll(Where where) {
        List<E> entities = repository.findAll(where);
        return convertToDto(entities, dtoClass, "password");
    }


    public UpdateResult update(Query query, org.springframework.data.mongodb.core.query.Update update, UserDetail userDetail) {
        return repository.update(query, update, userDetail);
    }

    public UpdateResult update(Query query, D dto) {
        return repository.update(query, convertToEntity(entityClass, dto));
    }

    public UpdateResult update(Query query, org.springframework.data.mongodb.core.query.Update update) {
        return repository.update(query, update);
    }

    public E findAndModify(Query query, org.springframework.data.mongodb.core.query.Update update, UserDetail userDetail) {

        return repository.findAndModify(query, update, userDetail);
    }

    public long deleteAll(Query query, UserDetail userDetail) {
        return repository.deleteAll(query, userDetail);
    }

    public long deleteById(List<ID> ids, UserDetail userDetail) {
        if (CollectionUtils.isNotEmpty(ids)) {
            Query query = new Query(Criteria.where("id").in(ids));
            return repository.deleteAll(query, userDetail);
        }
        return 0;
    }

    public long deleteAll(Query query) {
        return repository.deleteAll(query);
    }

    public long count(Where where, UserDetail userDetail) {

        return repository.count(where, userDetail);
    }

    public long count(Query query) {
        return repository.count(query);
    }

    public long count(Query query, UserDetail userDetail) {
        return repository.count(query, userDetail);
    }


    public D replaceById(ID id, D dto, UserDetail userDetail) {
        E entity = repository.replaceById(new Query(Criteria.where("_id").is(id)), convertToEntity(entityClass, dto), userDetail);
        return convertToDto(entity, dtoClass);
    }

    public D replaceOrInsert(D dto, UserDetail userDetail) {
        E entity = repository.replaceOrInsert(new Query(Criteria.where("_id").is(dto.getId())), convertToEntity(entityClass, dto), userDetail);
        return convertToDto(entity, dtoClass);
    }



    protected List<String> lists(String... statues) {
        ArrayList<String> lists = new ArrayList<>();
        if (statues != null && statues.length > 0) {
            lists.addAll(Arrays.asList(statues));
        }
        return lists;
    }

    protected String renameOnExists(String fieldName, String fieldValue, ID excludeId, UserDetail userDetail) {
        Criteria criteria = Criteria.where(fieldName).regex("^" + Pattern.quote(fieldValue));
        if (excludeId != null) {
            criteria.and("_id").ne(excludeId);
        }
        Query query = Query.query(criteria);
        query.fields().include(fieldName);
        List<String> existsNames = new ArrayList<>();
        repository.findAll(query, userDetail, doc -> existsNames.add(doc.getString(fieldName)));
        String name = fieldValue;
        if (existsNames.isEmpty()) return name;
        int counter = 1;
        while (true) {
            if (existsNames.contains(name)) {
                name = String.format("%s (%d)", fieldValue, counter++);
            } else {
                break;
            }
        }
        return name;
    }


}
