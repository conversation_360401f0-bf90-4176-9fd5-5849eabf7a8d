package com.qz.hare.server.config;

import com.qz.hare.server.module.session.DelayedUpdatesSessionRepository;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.serializer.support.DeserializingConverter;
import org.springframework.core.serializer.support.SerializingConverter;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.session.*;
import org.springframework.session.config.SessionRepositoryCustomizer;
import org.springframework.session.data.mongo.AbstractMongoSessionConverter;
import org.springframework.session.data.mongo.JacksonMongoSessionConverter;
import org.springframework.session.data.mongo.JdkMongoSessionConverter;
import org.springframework.session.data.mongo.MongoIndexedSessionRepository;
import org.springframework.session.data.mongo.config.annotation.web.http.EnableMongoHttpSession;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2024/8/23 15:08
 */
@Configuration
@EnableMongoHttpSession
public class SessionConfig {
    private IndexResolver<Session> indexResolver;
    private String collectionName = "sessions";
    private SessionIdGenerator sessionIdGenerator = UuidSessionIdGenerator.getInstance();
    private List<SessionRepositoryCustomizer<MongoIndexedSessionRepository>> sessionRepositoryCustomizers;

    //@Bean
    public AbstractMongoSessionConverter jdkMongoSessionConverter() {
        return new JacksonMongoSessionConverter();
    }

    @Bean
    public MongoIndexedSessionRepository mongoSessionRepository(MongoOperations mongoOperations,
                                                                CacheManager cacheManager) {

        Cache sessionCache = cacheManager.getCache("session");

        DelayedUpdatesSessionRepository repository = new DelayedUpdatesSessionRepository(mongoOperations, sessionCache);
        repository.setDefaultMaxInactiveInterval(MapSession.DEFAULT_MAX_INACTIVE_INTERVAL);
        JdkMongoSessionConverter mongoSessionConverter = new JdkMongoSessionConverter(new SerializingConverter(),
                new DeserializingConverter(this.getClass().getClassLoader()),
                Duration.ofSeconds(MapSession.DEFAULT_MAX_INACTIVE_INTERVAL_SECONDS));

        if (this.indexResolver != null) {
            mongoSessionConverter.setIndexResolver(this.indexResolver);
        }

        repository.setMongoSessionConverter(mongoSessionConverter);

        if (StringUtils.hasText(this.collectionName)) {
            repository.setCollectionName(this.collectionName);
        }
        repository.setSessionIdGenerator(this.sessionIdGenerator);

        this.sessionRepositoryCustomizers
                .forEach((sessionRepositoryCustomizer) -> sessionRepositoryCustomizer.customize(repository));

        return repository;
    }

    @Autowired(required = false)
    public void setIndexResolver(IndexResolver<Session> indexResolver) {
        this.indexResolver = indexResolver;
    }

    @Autowired(required = false)
    public void setSessionIdGenerator(SessionIdGenerator sessionIdGenerator) {
        this.sessionIdGenerator = sessionIdGenerator;
    }

    @Autowired(required = false)
    public void setSessionRepositoryCustomizers(
            ObjectProvider<SessionRepositoryCustomizer<MongoIndexedSessionRepository>> sessionRepositoryCustomizers) {
        this.sessionRepositoryCustomizers = sessionRepositoryCustomizers.orderedStream().collect(Collectors.toList());
    }
}
