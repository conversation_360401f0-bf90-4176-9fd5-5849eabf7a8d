package com.qz.hare.server.error;

import com.qz.hare.common.exception.ErrorCode;

/**
 * <AUTHOR>
 * create at 2024/10/17 21:06
 */
public enum ServerErrorCode implements ErrorCode {
    SYSTEM_ERROR("SystemError", "Catching unknown exceptions {0}."),
    NOT_LOGIN("NotLogin", "User not login"),
    ILLEGAL_STATE("IllegalState", "Illegal State: {0}"),
    ILLEGAL_ARGUMENT("IllegalArgument", "Illegal Argument: {0}"),
    WRONG_PASSWORD("WrongPassword", "Password is wrong."),
    STORE_FILE_FAILED("StoreFileFailed", "Store file failed."),
    NOT_FOUND_ONLINE_CLIENT("NotFoundOnlineClient", "Not found online client."),
    SEND_MESSAGE_FAILED("SendMessageFailed", "Send message failed."),
    NOT_FOUND_RESOURCE_SERVICE("NotFoundResourceService", "Not found resource service by name {0}"),
    NOT_FOUND_RESOURCE_MODEL("NotFoundResourceModel", "Not found resource model by name {0}"),

    NOT_FOUND_FILENAME("NotFoundFilename", "Not found filename in request body."),
    READ_FILE_DATA_ERROR("ReadFileDataError", "Read file data error {0}"),
    READ_NEXT_SEQUENCE_FAILED("ReadNextSequenceFailed", "Read next sequence for '{0}' failed."),
    NOT_FOUND_DATABASE_BY_ID("NotFoundDatabaseById", "Not found database metadata by id {0}"),

    NOT_FOUND_RESOURCE("NotFoundResource", "Not found resource"),

    CONNECTION_IN_USE("ConnectionInUse", "Connection in use {0}, cannot be deleted."),
    JOB_IN_USE("JobInUse", "Job in use {0}, cannot be deleted."),

    EMPTY_PIPELINE("EmptyPipeline", "Job data pipeline cannot be empty"),
    INVALID_PIPELINE("InvalidPipeline", "Job data pipeline configuration is invalid {0}"),
    PIPELINE_CYCLE("PipelineCycle", "Job data pipeline cannot have cycle {0}"),
    MULTIPLE_PIPELINE("MultiplePipeline", "Job data pipeline cannot have multiple pipeline instances"),

    NO_ENGINE_AVAILABLE("NoEngineAvailable", "No engine available."),

    NOT_ALLOWED_EDITING("NotAllowedEditing", "Not allowed editing {0}"),

    NOTIFY_ENGINE_ERROR("NotifyEngineError", "Notify engine error {0}"),
    HANDLER_JOB_ERROR("HandlerJobError", "Handler job error {0}"),
    ;

    private final String code;

    private final String describe;

    ServerErrorCode(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDescription() {
        return this.describe;
    }

    @Override
    public String toString() {
        return toStringFormat();
    }
}
