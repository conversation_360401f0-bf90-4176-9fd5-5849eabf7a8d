package com.qz.hare.server.module.connection;

import com.qz.hare.server.base.BaseController;
import com.qz.hare.server.common.model.ResponseMessage;
import com.qz.hare.server.common.model.connection.Connection;
import com.qz.hare.server.common.model.schema.Schema;
import com.qz.hare.server.common.params.BatchOptions;
import com.qz.hare.server.common.params.Page;
import com.qz.hare.server.common.validation.groups.Groups;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

import static com.qz.hare.server.utils.MongoUtils.toObjectId;

/**
 * <AUTHOR>
 * create at 2022/9/17 上午12:52
 */
@Lazy
@RestController
@RequestMapping("/api/v1/connection")
@AllArgsConstructor
@Tag(name = "connection", description = "Data source connection API")
public class ConnectionController extends BaseController {

    private ConnectionService connectionService;

    @Operation(summary = "Find datasource of the model matched by filter from the data source.")
    @GetMapping
    public ResponseMessage<Page<Connection>> findConnection(
            @Parameter(in = ParameterIn.QUERY, description = "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"field\":{\"something\":true|false},\"sort\": [\"name desc\"],\"skip\":1,\"limit\":20}`).")
            @RequestParam(value = "filter", required = false) String filterJson) {
        Page<Connection> result = connectionService.find(parseFilter(filterJson), getUserDetail());
        return success(result);
    }

    @Operation(summary = "Create datasource connection.")
    @PostMapping
    public ResponseMessage<Connection> createConnection(
            @RequestBody @Validated(Groups.CreateGroup.class) Connection connectionDto) {
        return success(connectionService.save(connectionDto, getUserDetail()));
    }

    @Operation(summary = "Delete datasource connection.")
    @DeleteMapping
    public ResponseMessage<Long> deleteConnection(@RequestBody BatchOptions batchOptions) {
        return success(connectionService.deleteById(BatchOptions.getObjectIds(batchOptions), getUserDetail()));
    }

    @Operation(summary = "Delete datasource connection.")
    @DeleteMapping("/{id}")
    public ResponseMessage<Long> deleteConnection(@PathVariable String id) {
        return deleteConnection(BatchOptions.builder().ids(Collections.singletonList(id)).build());
    }

    @Operation(summary = "Find datasource connection by id")
    @GetMapping("/{id}")
    public ResponseMessage<Connection> findConnectionById(@PathVariable String id) {
        return success(connectionService.findById(toObjectId(id), getUserDetail()));
    }

    @Operation(summary = "Copy datasource connection by id")
    @GetMapping("/copy/{id}")
    public ResponseMessage<Connection> copyConnectionById(@PathVariable String id) {
        return success(connectionService.copyConnection(toObjectId(id), getUserDetail()));
    }

    @Operation(summary = "Find table by connection id")
    @GetMapping("/{id}/tables")
    public ResponseMessage<Page<Schema>> findTableByConnectionId(
            @PathVariable String id,
            @Parameter(in = ParameterIn.QUERY, description = "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"field\":{\"something\":true|false},\"sort\": [\"name desc\"],\"skip\":1,\"limit\":20}`).")
            @RequestParam(value = "filter", required = false) String filterJson) {
        return success(connectionService.findTablesByConnectionId(toObjectId(id), parseFilter(filterJson), getUserDetail()));
    }

}
