package com.qz.hare.server.module.connection;

import com.mongodb.client.result.UpdateResult;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.common.util.Configuration;
import com.qz.hare.model.connection.TestConnectionResult;
import com.qz.hare.security.SecurityUtil;
import com.qz.hare.server.base.BaseService;
import com.qz.hare.server.base.entity.BaseEntity;
import com.qz.hare.server.common.model.connection.Connection;
import com.qz.hare.server.common.model.connection.ConnectionStatus;
import com.qz.hare.server.common.model.job.Job;
import com.qz.hare.server.common.model.schema.Schema;
import com.qz.hare.server.common.model.schema.SchemaType;
import com.qz.hare.server.common.model.tree.TreeNodeData;
import com.qz.hare.server.common.params.Field;
import com.qz.hare.server.common.params.Filter;
import com.qz.hare.server.common.params.Page;
import com.qz.hare.server.config.security.UserDetail;
import com.qz.hare.server.error.ServerErrorCode;
import com.qz.hare.server.module.connection.entity.ConnectionEntity;
import com.qz.hare.server.module.engine.EngineApi;
import com.qz.hare.server.module.engine.EngineService;
import com.qz.hare.server.module.job.JobService;
import com.qz.hare.server.module.job.entity.JobEntity;
import com.qz.hare.server.module.schema.SchemaService;
import com.qz.hare.ws.rpc.AllowRemoteCall;
import com.qz.hare.ws.rpc.RemoteService;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.qz.hare.server.module.connection.entity.ConnectionEntity.*;
import static com.qz.hare.server.module.schema.entity.SchemaEntity.FIELD_NAME_CONNECTION_ID;
import static com.qz.hare.server.module.schema.entity.SchemaEntity.FIELD_NAME_SCHEMA_TYPE;

/**
 * <AUTHOR>
 * create at 2024/10/19 11:46
 */
@Lazy
@Slf4j
@Service
@RemoteService
public class ConnectionService extends BaseService<Connection, ConnectionEntity, ObjectId, ConnectionRepository> {

    private final EngineApi engineApi;
    private final EngineService engineService;
    private SchemaService schemaService;
    private JobService jobService;

    public ConnectionService(ConnectionRepository repository, EngineApi engineApi, EngineService engineService) {
        super(repository);
        this.engineApi = engineApi;
        this.engineService = engineService;
    }

    @Override
    protected void beforeSave(Connection connection, UserDetail userDetail) {
        connection.setStatus(computedConnectionStatus(connection.getTestConnectionResult()));
        describeDatasource(connection);
        connection.setName(
                renameOnExists(FIELD_NAME_CONNECTION_NAME, connection.getName(), connection.getId(), userDetail));
    }

    private ConnectionStatus computedConnectionStatus(TestConnectionResult testConnectionResult) {
        if (testConnectionResult != null && testConnectionResult.getSuccess() != null) {
            return testConnectionResult.getSuccess() ? ConnectionStatus.READY : ConnectionStatus.INVALID;
        } else {
            return ConnectionStatus.NOT_DETECTED;
        }
    }

    public void describeDatasource(Connection connection) {
        if (connection == null) return;

        String json = StringUtils.isNotBlank(connection.getConfig()) ?
                SecurityUtil.decryptAES(connection.getConfig(), SecurityUtil.DEFAULT_AES_SECRET_KEY) : null;

        if (StringUtils.isBlank(json))
            return;

        Configuration config = Configuration.fromJson(json);

        String host = config.getString("host", config.getString("hostname", ""));
        String port = config.getString("port", "");
        String user = config.getString("username", "");
        String database = config.getString("database", "");
        String schema = config.getString("schema", "");
        String protocol = config.getString("protocol", "");

        String configPlain = String.format("%s%s:******@%s:%s/%s/%s",
                StringUtils.isBlank(protocol) ? "" : protocol + "://", user, host, port, database, schema);
        if (StringUtils.isBlank(host) && StringUtils.isBlank(port) && StringUtils.isBlank(protocol))
            connection.setConfigPlain(null);
        else
            connection.setConfigPlain(configPlain);
    }

    @Override
    public Page<Connection> find(Filter filter, UserDetail userDetail) {

        if (filter == null)
            filter = new Filter();
        filter.getWhere().and(FIELD_NAME_STATUS, Map.of("$ne", ConnectionStatus.DELETED));

        Page<Connection> page = super.find(filter, userDetail);
        page.getItems().forEach(connection -> connection.setConfig(null));
        return page;
    }

    @Override
    public Connection save(Connection dto, UserDetail userDetail) {
        Connection connection = super.save(dto, userDetail);

        if (connection.getStatus() == ConnectionStatus.NOT_DETECTED) {
            Optional.ofNullable(engineService.assignEngine(null, userDetail))
                    .ifPresent(engine -> {
                        try {
                            engineApi.testConnection(engine.getEngineId(), connection);
                        } catch (HareException e) {
                            log.error("Execute test connection failed {}, {}", engine.getEngineId(), e.getErrorCode(), e);
                        } catch (Exception e) {
                            log.error("Execute test connection failed, send message to {} failed", engine.getEngineId(), e);
                        }
                    });
        }
        return connection;
    }

    @Override
    public long deleteById(List<ObjectId> ids, UserDetail userDetail) {

        if (CollectionUtils.isEmpty(ids))
            return 0;

        // 1. 检查连接的引用，当有任务正在使用时，不允许删除
        List<Job> jobs = jobService.getReferenceJob(ids,
                new Field().include(JobEntity.FIELD_NAME_ID, JobEntity.FIELD_NAME_JOB_NAME), userDetail);

        if (!CollectionUtils.isEmpty(jobs)) {
            String jobRefs = jobs.stream().map(j -> String.format("%s(%s)", j.getName(), j.getId().toHexString()))
                    .collect(Collectors.joining(","));
            throw new HareException(ServerErrorCode.CONNECTION_IN_USE, jobRefs);
        }

        // 2. 删除模型等临时数据
        schemaService.deleteAll(Query.query(Criteria.where(FIELD_NAME_CONNECTION_ID).in(ids)), userDetail);

        // 3. 逻辑删除连接
        UpdateResult result = repository.update(
                Query.query(Criteria.where(BaseEntity.FIELD_NAME_ID).in(ids)),
                Update.update(BaseEntity.FIELD_NAME_STATUS, ConnectionStatus.DELETED), userDetail);
        return result.getModifiedCount();
    }

    @AllowRemoteCall
    public void updateTestConnectionResult(ObjectId id, TestConnectionResult testConnectionResult) {
        if (id == null)
            return;
        Update update = Update.update(FIELD_NAME_LAST_TEST_CONNECTION_TIME, new Date())
                .set(FIELD_NAME_STATUS, computedConnectionStatus(testConnectionResult));
        if (testConnectionResult != null) {
            update.set("testConnectionResult", testConnectionResult);
        }
        UpdateResult result = repository.updateById(id, update);
        log.debug("match count {}, modify count {}", result.getMatchedCount(), result.getModifiedCount());
    }

    public Connection copyConnection(ObjectId id, UserDetail userDetail) {
        if (id == null) return null;

        Optional<ConnectionEntity> optional = repository.findById(id);
        if (optional.isEmpty()) return null;

        Connection connection = convertToDto(optional.get(), Connection.class,
                FIELD_NAME_ID,
                FIELD_NAME_STATUS,
                FIELD_NAME_LAST_TEST_CONNECTION_TIME,
                FIELD_NAME_TEST_CONNECTION_RESULT,
                FIELD_NAME_CREATE_BY,
                FIELD_NAME_CREATED_AT,
                FIELD_NAME_UPDATE_BY,
                FIELD_NAME_UPDATED_AT,
                FIELD_NAME_DELETED,
                FIELD_NAME_PROJECT_ID);

        return save(connection, userDetail);
    }

    @Deprecated
    public List<TreeNodeData> getTreeNodeData(ObjectId parentId, String keyword, UserDetail userDetail) {
        if (parentId == null) {
            Criteria criteria = Criteria.where(FIELD_NAME_STATUS).is(ConnectionStatus.READY);

            if (StringUtils.isNotBlank(keyword))
                criteria.and(FIELD_NAME_CONNECTION_NAME).regex(keyword, "i");

            Query query = Query.query(criteria);
            query.fields().exclude(FIELD_NAME_TEST_CONNECTION_RESULT);
            query.limit(100);
            return repository.findAll(query, userDetail)
                    .stream()
                    .map(c -> TreeNodeData.builder()
                        .label(c.getName())
                        .icon(String.format("%s:%s", c.getConnectorName(), c.getConnectorVersion()))
                        .leaf(false)
                        .id(c.getId().toHexString())
                        .attrs(Map.of(FIELD_NAME_CONNECTOR_NAME, c.getConnectorName(),
                                FIELD_NAME_CONNECTOR_VERSION, c.getConnectorVersion(),
                                "type", SchemaType.DATABASE))
                        .build()).toList();
        } else {
            return schemaService.getTablesByConnectionId(parentId, keyword, Arrays.asList("table.name", "_id"), userDetail)
                    .stream()
                    .map(t -> TreeNodeData.builder()
                            .label(t.getTable().getName())
                            .id(t.getId().toHexString())
                            .leaf(true)
                            .icon("table")
                            .attrs(Map.of("type", SchemaType.TABLE))
                            .build()).toList();
        }
    }

    /**
     * 根据连接id获取数据库表
     * @param id 连接id
     * @param filter Filter
     * @param userDetail User detail
     * @return table page data
     */
    public Page<Schema> findTablesByConnectionId(ObjectId id, Filter filter, UserDetail userDetail) {
        filter.where(FIELD_NAME_CONNECTION_ID, id);
        filter.where(FIELD_NAME_SCHEMA_TYPE, SchemaType.TABLE);
        return schemaService.find(filter, userDetail);
    }

    @Autowired
    public void setJobService(JobService jobService) {
        this.jobService = jobService;
    }

    @Autowired
    public void setSchemaService(SchemaService schemaService) {
        this.schemaService = schemaService;
    }
}
