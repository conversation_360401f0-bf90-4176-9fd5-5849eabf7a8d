package com.qz.hare.server.module.engine;

import com.qz.hare.common.exception.HareException;
import com.qz.hare.server.common.model.connection.Connection;
import com.qz.hare.server.common.model.job.Job;
import com.qz.hare.server.websocket.handler.WebSocketServer;
import com.qz.hare.ws.error.WebsocketErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/8/20 18:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EngineApi {
    private final WebSocketServer webSocketServer;

    private static final String connectionServiceName = "connectionService";
    private static final String testConnectionMethodName = "testConnection";

    private static final String jobServiceName = "jobService";
    private static final String startJobMethodName = "startJob";

    public void testConnection(String engineId, Connection connection) throws HareException {

        try {
            webSocketServer.call(engineId, connectionServiceName, testConnectionMethodName,
                    List.of(connection), null);
        } catch (IOException e) {
            log.error("Exception occurred when testConnection: engine {}, connection {}",
                    engineId, connection.getId().toHexString(), e);
            throw new HareException(WebsocketErrorCode.EXECUTE_RPC_METHOD_ERROR,
                    connectionServiceName, testConnectionMethodName, e.getMessage());
        }
    }

    public void startJob(String engineId, Job job) throws HareException {
        try {
            webSocketServer.call(engineId, jobServiceName, startJobMethodName, List.of(job), null);
        } catch (IOException e) {
            log.error("Exception occurred when startJob: engineId {}, job {}", engineId, job.getId().toHexString(), e);
            throw new HareException(WebsocketErrorCode.EXECUTE_RPC_METHOD_ERROR,
                    connectionServiceName, testConnectionMethodName, e.getMessage());
        }
    }
}
