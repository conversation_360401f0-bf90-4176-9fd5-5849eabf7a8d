package com.qz.hare.server.module.engine;

import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.UpdateResult;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.common.os.VmInfo;
import com.qz.hare.server.base.BaseService;
import com.qz.hare.server.common.model.engine.Engine;
import com.qz.hare.server.common.model.engine.EngineStatus;
import com.qz.hare.server.common.params.Where;
import com.qz.hare.server.config.security.UserDetail;
import com.qz.hare.server.error.ServerErrorCode;
import com.qz.hare.server.module.engine.entity.EngineEntity;
import com.qz.hare.server.module.event.EventService;
import com.qz.hare.server.module.event.EventType;
import com.qz.hare.server.module.event.entity.EventEntity;
import com.qz.hare.server.module.metrics.MeterService;
import com.qz.hare.server.module.metrics.entity.MeterEntity;
import com.qz.hare.ws.rpc.AllowRemoteCall;
import com.qz.hare.ws.rpc.AutoInject;
import com.qz.hare.ws.rpc.RemoteService;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

import static com.qz.hare.ws.WebSocketAttributeKeys.ENGINE_ID_PARAM_NAME;
import static com.qz.hare.ws.WebSocketAttributeKeys.USER_DETAIL_PARAM_NAME;

/**
 * <AUTHOR>
 * create at 2022/8/12 下午3:23
 */
@Lazy
@Slf4j
@Service
@RemoteService
public class EngineService extends BaseService<Engine, EngineEntity, ObjectId, EngineRepository> {

    private static final String ENGINE_ID_FIELD_NAME = "engineId";

    private final EventService eventService;
    private final MeterService meterService;

    public EngineService(EngineRepository repository, EventService eventService, MeterService meterService) {
        super(repository);
        this.eventService = eventService;
        this.meterService = meterService;
    }

    /**
     * update engine status to online
     * @param engineId
     */
    public Engine online(String engineId, String clientIp, UserDetail userDetail) {
        Engine dto = updateEngineStatus(engineId, EngineStatus.RUNNING, userDetail,
                new Update().addToSet("clientIp", clientIp));

        //eventService.emit(EventType.ENGINE_ONLINE, )
        EventEntity event = eventService.newEventBuilder(EventType.ENGINE_ONLINE)
                .label(ENGINE_ID_FIELD_NAME, engineId)
                .label("clientIp", clientIp)
                .eventTime(new Date())
                .build();

        eventService.save(event, userDetail);
        return dto;
    }

    /**
     * update engine status to offline
     * @param engineId
     */
    public Engine offline(String engineId, UserDetail userDetail) {
        Engine dto = updateEngineStatus(engineId, EngineStatus.OFFLINE, userDetail, null);

        //eventService.emit(EventType.ENGINE_ONLINE, )
        EventEntity event = eventService.newEventBuilder(EventType.ENGINE_OFFLINE)
                .label(ENGINE_ID_FIELD_NAME, engineId)
                .eventTime(new Date())
                .build();

        eventService.save(event, userDetail);
        return dto;
    }

    /**
     * update engine status
     * @param engineId
     * @param engineStatus
     * @param userDetail
     */
    private Engine updateEngineStatus(String engineId, EngineStatus engineStatus, UserDetail userDetail,
                                      Update update) {
        Query query = Query.query(Criteria.where(ENGINE_ID_FIELD_NAME).is(engineId));
        if (update == null) {
            update = new Update();
        }
        update.set("status", engineStatus);
        EngineEntity result = repository.upsert(query, update, userDetail);
        return convertToDto(result, Engine.class);
    }

    /**
     * Batch update engine metrics
     * @param engineMetrics
     *          {
     *              engineId -> {
     *                  "metrics name": metrics value
     *              }
     *          }
     * @return update count
     */
    public int batchUpdateEngineMetrics(Map<String, Map<String, Number>> engineMetrics) {

        if (CollectionUtils.isEmpty(engineMetrics)) {
            return 0;
        }

        BulkOperations ops = repository.bulkOperations(BulkOperations.BulkMode.UNORDERED);

        AtomicInteger opsCounter = new AtomicInteger(0);
        engineMetrics.forEach( (engineId, metrics) -> {

            if (CollectionUtils.isEmpty(metrics)) {
                return;
            }

            Update update = new Update();
            update.set("lastUpdAt", new Date());
            metrics.forEach((metricsName, metricsValue) -> {
                update.set("metrics." + metricsName, metricsValue);
            });
            ops.updateOne(Query.query(Criteria.where(ENGINE_ID_FIELD_NAME).is(engineId)), update);
            opsCounter.incrementAndGet();
        });
        if (opsCounter.get() > 0) {
            BulkWriteResult result = ops.execute();
            return result.getModifiedCount();
        }
        return 0;
    }

    @AllowRemoteCall
    public void collectorEngineStatus(VmInfo vmInfo, @AutoInject(ENGINE_ID_PARAM_NAME) String engineId) {

        if (engineId == null || vmInfo == null) {
            return;
        }

        Update update = new Update();
        AtomicInteger opsCounter = new AtomicInteger(0);
        Stream.of(VmInfo.class.getDeclaredFields()).forEach(field -> {
            try {
                field.setAccessible(true);
                Object value = field.get(vmInfo);
                if (value != null) {
                    update.set("vmInfo." + field.getName(), value);
                }
                opsCounter.incrementAndGet();
            } catch (Exception e) {
                log.error("Read engine data ({}) failed", field.getName(), e);
            }
        });

        if (opsCounter.get() > 0){
            repository.update(Query.query(Criteria.where(ENGINE_ID_FIELD_NAME).is(engineId)), update);
        }

        long now = vmInfo.getSysTime() != null ? vmInfo.getSysTime() : System.currentTimeMillis();
        if (vmInfo.getCpuUsage() != null){
            meterService.addMeter(MeterEntity.builder().name("hare.engine.cpu_usage").ts(new Date(now)).value(vmInfo.getCpuUsage())
                    .description("Engine cpu usage.").build().tag(ENGINE_ID_FIELD_NAME, engineId));
        }
        if (vmInfo.getMemUsage() != null){
            meterService.addMeter(MeterEntity.builder().name("hare.engine.mem_usage").ts(new Date(now)).value(vmInfo.getMemUsage())
                    .description("Engine memory usage.").build().tag(ENGINE_ID_FIELD_NAME, engineId));
        }

    }

    /**
     * 分配引擎
     * @param where
     * @param userDetail
     * @return
     */
    @AllowRemoteCall
    public Engine assignEngine(Where where, @AutoInject(USER_DETAIL_PARAM_NAME) UserDetail userDetail) {
        if (where == null)
            where = new Where();

        where.and("status", EngineStatus.RUNNING);
        // 在这里可以设置分配引擎规则，例如：负载、地域等
        List<Engine> list = this.findAll(where, userDetail);
        if (CollectionUtils.isEmpty(list)) {
            log.warn("Not engine available {}, {}", where, userDetail.getUserId());
            throw new HareException(ServerErrorCode.NO_ENGINE_AVAILABLE);
        }
        return list.get(0);
    }

    public void checkOnlineEngine() {
        Criteria criteria = Criteria.where("status").is(EngineStatus.RUNNING.name())
                .and("lastUpdAt").lt(new Date(System.currentTimeMillis() - 60000));
        UpdateResult result = repository.update(Query.query(criteria), Update.update("status", EngineStatus.OFFLINE.name()));
        log.debug("Update {} engines to offline", result.getModifiedCount());
    }
}
