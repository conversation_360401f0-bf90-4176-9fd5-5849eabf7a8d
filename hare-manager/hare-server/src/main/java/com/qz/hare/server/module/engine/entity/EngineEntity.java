package com.qz.hare.server.module.engine.entity;

import com.qz.hare.common.os.VmInfo;
import com.qz.hare.server.base.entity.ResourceEntity;
import com.qz.hare.server.common.model.engine.EngineStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * <AUTHOR>
 * create at 2022/8/12 下午2:41
 */
@Data
@Document("engine")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EngineEntity extends ResourceEntity<ObjectId> {

    @Indexed(unique = true)
    private String engineId;

    private EngineStatus status;

    private VmInfo vmInfo;

    private Map<String, String> labels;

    private Map<String, Number> metrics;

}
