package com.qz.hare.server.module.job;

import com.qz.hare.server.base.BaseController;
import com.qz.hare.server.common.model.ResponseMessage;
import com.qz.hare.server.common.model.job.Job;
import com.qz.hare.server.common.params.BatchOptions;
import com.qz.hare.server.common.params.Field;
import com.qz.hare.server.common.params.Page;
import com.qz.hare.server.common.validation.groups.Groups;
import com.qz.hare.server.error.ServerErrorCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

import static com.qz.hare.server.utils.MongoUtils.toObjectId;

/**
 * <AUTHOR>
 * create at 2025/1/13 20:12
 */
@Lazy
@RestController
@RequestMapping("/api/v1/job")
@AllArgsConstructor
@Tag(name = "job", description = "Job API")
public class JobController extends BaseController {

    private JobService jobService;

    @Operation(summary = "Find job of the model matched by filter from the data source.")
    @GetMapping
    public ResponseMessage<Page<Job>> findJob(
            @Parameter(in = ParameterIn.QUERY, description = "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"field\":{\"something\":true|false},\"sort\": [\"name desc\"],\"skip\":1,\"limit\":20}`).")
            @RequestParam(value = "filter", required = false) String filterJson) {
        Page<Job> result = jobService.find(parseFilter(filterJson), getUserDetail());
        return success(result);
    }

    @Operation(summary = "Find job by id")
    @GetMapping("/{id}")
    public ResponseMessage<Job> findJobById(@PathVariable String id,
                                            @Parameter(
                                                    description = "Set to return a specified field, or set to exclude a specified field: e.g. {name: 1}, return the name field; {name: 0} exclude the name field",
                                                    in = ParameterIn.QUERY)
                                            @RequestParam(required = false) String fields,
                                            HttpServletResponse response) {

        Field field = parseField(fields);

        Job job = jobService.findById(toObjectId(id), field, getUserDetail());
        if (job == null) {
            return failed(ServerErrorCode.NOT_FOUND_RESOURCE);
        }
        return success(job);
    }

    @Operation(summary = "Create job.")
    @PostMapping
    public ResponseMessage<Job> createJob(@RequestBody @Validated(Groups.CreateGroup.class) Job jobDto) {
        return success(jobService.createJob(jobDto, getUserDetail()));
    }

    @Operation(summary = "Update job.")
    @PatchMapping("/{id}")
    public ResponseMessage<Job> updateJob(
            @PathVariable String id,
            @RequestBody @Validated(Groups.UpdateGroup.class) Job jobDto) {
        return success(jobService.updateJob(id, jobDto, getUserDetail()));
    }

    @Operation(summary = "Delete jobs.")
    @DeleteMapping
    public ResponseMessage<Long> deleteJob(@RequestBody BatchOptions batchOptions) {
        return success(jobService.deleteById(BatchOptions.getObjectIds(batchOptions), getUserDetail()));
    }

    @Operation(summary = "Delete job by id")
    @DeleteMapping("/{id}")
    public ResponseMessage<Long> deleteJobById(@PathVariable String id) {
        return deleteJob(BatchOptions.builder().ids(Collections.singletonList(id)).build());
    }

    @Operation(summary = "Submit job to execution")
    @PostMapping(path = {"/{id}/submit"})
    public ResponseMessage<Boolean> submitJob(@PathVariable String id) {
        jobService.submitJob(id, getUserDetail());
        return success(true);
    }
}
