package com.qz.hare.server.module.job;

import com.mongodb.client.result.UpdateResult;
import com.qz.hare.common.exception.ErrorCode;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.server.base.BaseService;
import com.qz.hare.server.common.model.job.Job;
import com.qz.hare.server.common.model.job.JobStatus;
import com.qz.hare.server.common.model.job.JobUtils;
import com.qz.hare.server.common.params.Field;
import com.qz.hare.server.common.params.Filter;
import com.qz.hare.server.common.params.Page;
import com.qz.hare.server.config.security.UserDetail;
import com.qz.hare.server.error.ServerErrorCode;
import com.qz.hare.server.module.job.entity.JobEntity;
import com.qz.hare.server.module.job.fsm.JobEvent;
import com.qz.hare.server.module.plugin.PluginStoreService;
import com.qz.hare.ws.rpc.AllowRemoteCall;
import com.qz.hare.ws.rpc.AutoInject;
import com.qz.hare.ws.rpc.RemoteService;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineEventResult;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.qz.hare.server.base.entity.BaseEntity.FIELD_NAME_ID;
import static com.qz.hare.server.base.entity.BaseEntity.FIELD_NAME_STATUS;
import static com.qz.hare.server.module.job.entity.JobEntity.FIELD_NAME_GRAPH_JSON;
import static com.qz.hare.server.module.job.entity.JobEntity.FIELD_NAME_JOB_NAME;
import static com.qz.hare.server.module.job.fsm.JobStateMachineConfig.*;
import static com.qz.hare.server.utils.MongoUtils.toObjectId;

/**
 * <AUTHOR>
 * create at 2025/1/13 19:19
 */
@Slf4j
@Lazy
@Service
@RemoteService
public class JobService extends BaseService<Job, JobEntity, ObjectId, JobRepository> {
    private final PluginStoreService pluginStoreService;
    private final StateMachineFactory<JobStatus, JobEvent> stateMachineFactory;
    private final StateMachinePersister<JobStatus, JobEvent, Job> stateMachinePersister;

    public JobService(JobRepository repository, PluginStoreService pluginStoreService,
                      StateMachineFactory<JobStatus, JobEvent> stateMachineFactory,
                      StateMachinePersister<JobStatus, JobEvent, Job> stateMachinePersister) {
        super(repository);
        this.pluginStoreService = pluginStoreService;
        this.stateMachineFactory = stateMachineFactory;
        this.stateMachinePersister = stateMachinePersister;
    }

    public boolean start(ObjectId id) {
        return false;
    }

    @Override
    public Page<Job> find(Filter filter, UserDetail userDetail) {

        if (filter == null)
            filter = new Filter();
        filter.getWhere().and(FIELD_NAME_STATUS, Map.of("$ne", JobStatus.DELETED));

        return super.find(filter, userDetail);
    }

    public Job createJob(Job job, UserDetail userDetail) {
        log.debug("Create job {} for user {}", job.getName(), userDetail.getUserId());

        Assert.notNull(job, "job is null");
        Assert.notNull(userDetail, "userDetail is null");

        // 0. Check for duplicate job names
        job.setName(renameOnExists(FIELD_NAME_JOB_NAME, job.getName(), job.getId(), userDetail));
        // 1. Check job settings
        JobUtils.validateJobConfiguration(job, pluginStoreService);
        // 2. Set default value if null
        if (job.getStatus() == null) job.setStatus(JobStatus.EDITING);
        // 3. Save job
        return save(job, userDetail);
    }

    public Job updateJob(String id, Job job, UserDetail userDetail) {
        Assert.notNull(id, "job id is null");
        Assert.notNull(job, "job is null");
        Assert.notNull(userDetail, "userDetail is null");
        log.debug("Update job {} for user {}", id, userDetail.getUserId());

        ObjectId jobId = toObjectId(id);
        Assert.notNull(jobId, "job id is invalid");
        job.setId(jobId);
        job.setStatus(null);

        // 1. Check for duplicate job name
        job.setName(renameOnExists(FIELD_NAME_JOB_NAME, job.getName(), jobId, userDetail));
        // 2. Check job settings
        JobUtils.validateJobConfiguration(job, pluginStoreService);
        // 3. Check current job status allow edit job
        JobEntity jobEntity = repository.findById(jobId, userDetail).orElse(null);
        if (jobEntity != null && !JobStatus.canEditingStatus().contains(jobEntity.getStatus())) {
            throw new HareException(ServerErrorCode.NOT_ALLOWED_EDITING, jobEntity.getStatus());
        }
        // 4. 保存任务
        return save(job, userDetail);
    }

    @Override
    public long deleteById(List<ObjectId> ids, UserDetail userDetail) {
        Assert.notNull(ids, "job id is null");
        Assert.notNull(userDetail, "userDetail is null");

        // 1. 检查任务状态是否允许删除
        Query query = Query.query(Criteria.where(FIELD_NAME_ID).in(ids)
                .and(FIELD_NAME_STATUS).in(JobStatus.unableToDeleteStatus()));

        query.fields().include(FIELD_NAME_ID, FIELD_NAME_STATUS, FIELD_NAME_JOB_NAME);

        List<JobEntity> unableToDeleteJobs = repository.findAll(query, userDetail);
        if (!CollectionUtils.isEmpty(unableToDeleteJobs)) {
            String jobInUse = unableToDeleteJobs.stream().map(j -> String.format("%s(%s)", j.getName(), j.getStatus()))
                    .collect(Collectors.joining(","));
            throw new HareException(ServerErrorCode.JOB_IN_USE, jobInUse);
        }

        // 2. 逻辑删除
        UpdateResult result = repository.update(
                Query.query(Criteria.where(FIELD_NAME_ID).in(ids)),
                Update.update(FIELD_NAME_STATUS, JobStatus.DELETED), userDetail);

        return result.getModifiedCount();
    }

    public List<Job> getReferenceJob(List<ObjectId> connectionIds, Field fields, UserDetail userDetail) {

        Criteria criteria = Criteria.where(FIELD_NAME_STATUS).ne(JobStatus.DELETED)
                .and("dag.nodes.params.connectionId").in(
                        connectionIds.stream().map(ObjectId::toHexString).toList());

        return convertToDto(repository.findAll(Query.query(criteria), fields, userDetail));
    }

    /**
     * 发送事件到状态机
     */
    protected void sendEvent(ObjectId jobId, JobEvent event, UserDetail userDetail) {
        try {
            Optional<JobEntity> jobOpt = repository.findById(jobId, new Field().exclude(FIELD_NAME_GRAPH_JSON));
            if (jobOpt.isEmpty()) {
                log.warn("Job not found: {}", jobId);
                throw new HareException(ServerErrorCode.NOT_FOUND_RESOURCE, jobId);
            }

            StateMachine<JobStatus, JobEvent> stateMachine = stateMachineFactory.getStateMachine();
            Job job = convertToDto(jobOpt.get());
            stateMachine = stateMachinePersister.restore(stateMachine, job);
            Message<JobEvent> message = MessageBuilder.withPayload(event)
                    .setHeader(MESSAGE_HEADER_JOB, job)
                    .setHeader(MESSAGE_HEADER_USER_DETAIL, userDetail)
                    .build();
            boolean result = Boolean.TRUE.equals(stateMachine.sendEvent(Mono.just(message))
                    .doOnError(e -> log.error("Send event to state machine failed", e))
                    .switchIfEmpty(Flux.just(StateMachineEventResult.from(stateMachine, message, StateMachineEventResult.ResultType.DENIED)))
                    .reduce(false, (a, r) -> !(a | r.getResultType() == StateMachineEventResult.ResultType.DENIED))
                    .block());
            if (result) {
                result = Boolean.TRUE.equals(stateMachine.getExtendedState().getVariables().get(EXTENDED_STATE_VAR_RESULT));
                if (result) {
                    stateMachinePersister.persist(stateMachine, job);
                    log.debug("Event {} sent to job {}, new state: {}", event, jobId, stateMachine.getState().getId());
                } else {
                    Object error = stateMachine.getExtendedState().getVariables().get(EXTENDED_STATE_VAR_ERROR);
                    if (error instanceof HareException e) {
                        throw e;
                    } else if (error instanceof Exception e){
                        log.error("Event {} handler failed, error: {}", event, e.getMessage(), e);
                        throw new HareException(ServerErrorCode.HANDLER_JOB_ERROR, e, event);
                    } else {
                        log.error("Event {} handler failed, error: {}", event, error);
                        throw new HareException(ServerErrorCode.HANDLER_JOB_ERROR, event);
                    }
                }
            } else {
                log.warn("Failed to send event {} to job {}, maybe state machine event handler has error", event, jobId);
                throw new HareException(ServerErrorCode.HANDLER_JOB_ERROR, event);
            }
        } catch (HareException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error sending event {} to job {}: {}", event, jobId, e.getMessage(), e);
            throw new HareException(ServerErrorCode.HANDLER_JOB_ERROR, e.getMessage(), e);
        }
    }

    public void startJob(StateContext<JobStatus, JobEvent> context) {
        log.info("Submit job({}) to engine, {} -> {}", context.getMessage().getHeaders(),
                context.getSource().getId(), context.getTarget().getId());
    }

    /**
     * Submit job to run
     * @param id job id
     * @param userDetail user detail
     */
    public void submitJob(String id, UserDetail userDetail) {
        Assert.notNull(id, "job id is null");
        Assert.notNull(userDetail, "userDetail is null");
        sendEvent(toObjectId(id), JobEvent.SUBMIT, userDetail);
    }

    @AllowRemoteCall
    public boolean onJobError(String id, ErrorCode code, Object[] args, @AutoInject UserDetail userDetail) {
        log.error("An error occurred while running the job {}, {}, {}", id, code, args);

        return true;
    }
}

