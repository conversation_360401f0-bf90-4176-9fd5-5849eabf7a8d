package com.qz.hare.server.module.job.entity;

import com.qz.hare.server.base.entity.ResourceEntity;
import com.qz.hare.server.common.model.job.JobSetting;
import com.qz.hare.server.common.model.job.JobStatus;
import com.qz.hare.server.common.model.job.JobType;
import com.qz.hare.server.common.model.job.MigrationType;
import com.qz.hare.server.common.model.job.dag.Dag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * create at 2025/1/12 12:10
 */
@Data
@Document("jobs")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class JobEntity extends ResourceEntity<ObjectId> {

    public static final String FIELD_NAME_JOB_NAME = "name";
    public static final String FIELD_NAME_JOB_STATUS = "status";
    public static final String FIELD_NAME_ENGINE_ID = "engineId";
    public static final String FIELD_NAME_GRAPH_JSON = "graphJson";

    private String name;
    private Dag dag;

    private JobStatus status;
    private JobType type;
    private MigrationType migrationType;

    private JobSetting jobSetting;

    private Date lastRunTime;

    private Object graphJson;

    private ObjectId engineId;

}
