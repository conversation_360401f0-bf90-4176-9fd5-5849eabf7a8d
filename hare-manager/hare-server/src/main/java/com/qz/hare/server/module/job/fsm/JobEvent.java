package com.qz.hare.server.module.job.fsm;

/**
 * Job事件枚举 - 用于触发状态机状态转换
 * 
 * <AUTHOR>
 * create at 2025/8/19
 */
public enum JobEvent {
    /**
     * 提交任务 - 从编辑状态提交
     */
    SUBMIT,
    
    /**
     * 启动任务
     */
    START,
    
    /**
     * 调度成功
     */
    SCHEDULE_SUCCESS,
    
    /**
     * 调度失败
     */
    SCHEDULE_FAIL,
    
    /**
     * 任务开始运行
     */
    RUN,
    
    /**
     * 任务运行完成
     */
    FINISH,
    
    /**
     * 停止任务
     */
    STOP,
    
    /**
     * 任务停止完成
     */
    STOP_COMPLETE,
    
    /**
     * 任务发生错误
     */
    ERROR,
    
    /**
     * 重置任务
     */
    RESET,
    
    /**
     * 重置完成
     */
    RESET_COMPLETE,
    
    /**
     * 重置失败
     */
    RESET_FAIL,
    
    /**
     * 删除任务
     */
    DELETE,
    
    /**
     * 重新编辑
     */
    EDIT,
    
    /**
     * 重试
     */
    RETRY
}
