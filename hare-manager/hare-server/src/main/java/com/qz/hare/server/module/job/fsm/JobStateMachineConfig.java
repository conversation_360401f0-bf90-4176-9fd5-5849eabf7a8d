package com.qz.hare.server.module.job.fsm;

import com.qz.hare.server.common.model.job.Job;
import com.qz.hare.server.common.model.job.JobStatus;
import com.qz.hare.server.module.job.fsm.action.ScheduleAndSubmitRunningJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.StateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.transition.Transition;

/**
 * Job状态机配置
 *
 * <AUTHOR>
 * create at 2025/8/19
 */
@Slf4j
@Configuration
@EnableStateMachineFactory
public class JobStateMachineConfig extends StateMachineConfigurerAdapter<JobStatus, JobEvent> {

    public static final String MESSAGE_HEADER_USER_DETAIL = "user";
    public static final String MESSAGE_HEADER_JOB = "job";
    public static final String EXTENDED_STATE_VAR_RESULT = "result";
    public static final String EXTENDED_STATE_VAR_ERROR = "error";

    @Autowired
    private ScheduleAndSubmitRunningJob scheduleAndSubmitRunningJob;

    @Override
    public void configure(StateMachineStateConfigurer<JobStatus, JobEvent> states) throws Exception {
        states
            .withStates()
                .initial(JobStatus.EDITING)
                .states(java.util.EnumSet.allOf(JobStatus.class))
                .end(JobStatus.DELETED);
    }
    
    @Override
    public void configure(StateMachineTransitionConfigurer<JobStatus, JobEvent> transitions) throws Exception {
        transitions
            // 从编辑状态开始
            .withExternal()
                .source(JobStatus.EDITING).target(JobStatus.WAITING_START)
                .event(JobEvent.SUBMIT)
                .action(context -> scheduleAndSubmitRunningJob.execution(context))
            .and()

            // 定时任务启动
            .withExternal()
                .source(JobStatus.WAITING_NEXT_RUN).target(JobStatus.WAITING_START)
                .event(JobEvent.SUBMIT)
                .action(context -> scheduleAndSubmitRunningJob.execution(context))
                .and()
            
            // 启动流程
            .withExternal()
                .source(JobStatus.WAITING_START).target(JobStatus.SCHEDULE_SUCCESS)
                .event(JobEvent.SCHEDULE_SUCCESS)
                .action(context -> log.info("Job scheduled successfully"))
            .and()
            .withExternal()
                .source(JobStatus.WAITING_START).target(JobStatus.SCHEDULE_FAILED)
                .event(JobEvent.SCHEDULE_FAIL)
                .action(context -> log.warn("Job scheduling failed"))
            .and()
            
            // 运行流程
            .withExternal()
                .source(JobStatus.SCHEDULE_SUCCESS).target(JobStatus.RUNNING)
                .event(JobEvent.RUN)
                .action(context -> log.info("Job started running"))
            .and()
            .withExternal()
                .source(JobStatus.RUNNING).target(JobStatus.COMPLETE)
                .event(JobEvent.FINISH)
                .action(context -> log.info("Job completed successfully"))
            .and()
            .withExternal()
                .source(JobStatus.RUNNING).target(JobStatus.WAITING_NEXT_RUN)
                .event(JobEvent.FINISH)
                .action(context -> log.info("Job finished, waiting for next run"))
            .and()
            
            // 停止流程
            .withExternal()
                .source(JobStatus.RUNNING).target(JobStatus.STOPPING)
                .event(JobEvent.STOP)
                .action(context -> log.info("Job stopping requested"))
            .and()
            .withExternal()
                .source(JobStatus.WAITING_NEXT_RUN).target(JobStatus.STOPPING)
                .event(JobEvent.STOP)
                .action(context -> log.info("Job stopping requested"))
            .and()
            .withExternal()
                .source(JobStatus.STOPPING).target(JobStatus.STOPPED)
                .event(JobEvent.STOP_COMPLETE)
                .action(context -> log.info("Job stopped successfully"))
            .and()
            
            // 错误处理
            .withExternal()
                .source(JobStatus.RUNNING).target(JobStatus.ERROR)
                .event(JobEvent.ERROR)
                .action(context -> log.error("Job encountered an error"))
            .and()
            .withExternal()
                .source(JobStatus.WAITING_START).target(JobStatus.ERROR)
                .event(JobEvent.ERROR)
                .action(context -> log.error("Job encountered an error during startup"))
            .and()

            // 重置流程
            .withExternal()
                .source(JobStatus.ERROR).target(JobStatus.RESETTING)
                .event(JobEvent.RESET)
                .action(context -> log.info("Job reset requested"))
            .and()
            .withExternal()
                .source(JobStatus.STOPPED).target(JobStatus.RESETTING)
                .event(JobEvent.RESET)
                .action(context -> log.info("Job reset requested"))
            .and()
            .withExternal()
                .source(JobStatus.COMPLETE).target(JobStatus.RESETTING)
                .event(JobEvent.RESET)
                .action(context -> log.info("Job reset requested"))
            .and()
                .withExternal()
                .source(JobStatus.EDITING).target(JobStatus.RESETTING)
                .event(JobEvent.RESET)
                .action(context -> log.info("Job reset requested"))
                .and()
            .withExternal()
                .source(JobStatus.RESETTING).target(JobStatus.EDITING)
                .event(JobEvent.RESET_COMPLETE)
                .action(context -> log.info("Job reset completed"))
            .and()
            .withExternal()
                .source(JobStatus.RESETTING).target(JobStatus.RESET_FAILED)
                .event(JobEvent.RESET_FAIL)
                .action(context -> log.warn("Job reset failed"))
            .and()
            
            // 重新编辑
            .withExternal()
                .source(JobStatus.STOPPED).target(JobStatus.EDITING)
                .event(JobEvent.EDIT)
                .action(context -> log.info("Job returned to editing state"))
            .and()
            .withExternal()
                .source(JobStatus.ERROR).target(JobStatus.EDITING)
                .event(JobEvent.EDIT)
                .action(context -> log.info("Job returned to editing state"))
            .and()
            
            // 重试
            .withExternal()
                .source(JobStatus.ERROR).target(JobStatus.WAITING_START)
                .event(JobEvent.RETRY)
                .action(context -> log.info("Job retry requested"))
            .and()
            .withExternal()
                .source(JobStatus.SCHEDULE_FAILED).target(JobStatus.WAITING_START)
                .event(JobEvent.RETRY)
                .action(context -> log.info("Job retry requested"))
            .and()
            
            // 删除
            .withExternal()
                .source(JobStatus.EDITING).target(JobStatus.DELETED)
                .event(JobEvent.DELETE)
                .action(context -> log.info("Job deleted"))
            .and()
            .withExternal()
                .source(JobStatus.STOPPED).target(JobStatus.DELETED)
                .event(JobEvent.DELETE)
                .action(context -> log.info("Job deleted"))
            .and()
            .withExternal()
                .source(JobStatus.ERROR).target(JobStatus.DELETED)
                .event(JobEvent.DELETE)
                .action(context -> log.info("Job deleted"))
            .and()
            .withExternal()
                .source(JobStatus.COMPLETE).target(JobStatus.DELETED)
                .event(JobEvent.DELETE)
                .action(context -> log.info("Job deleted"));
    }

    @Bean
    public StateMachinePersister<JobStatus, JobEvent, Job> stateMachinePersister(
            JobStateMachineStatePersist jobStateMachineStatePersist) {
        return new DefaultStateMachinePersister<>(jobStateMachineStatePersist);
    }
    
    @Bean
    public StateMachineListener<JobStatus, JobEvent> stateMachineListener() {
        return new StateMachineListenerAdapter<>() {
            @Override
            public void stateChanged(State<JobStatus, JobEvent> from, State<JobStatus, JobEvent> to) {
                log.info("State changed from {} to {}", 
                    from != null ? from.getId() : "null", 
                    to != null ? to.getId() : "null");
            }
            
            @Override
            public void transition(Transition<JobStatus, JobEvent> transition) {
                log.info("Transition: {} -> {} on event {}", 
                    transition.getSource().getId(),
                    transition.getTarget().getId(),
                    transition.getTrigger().getEvent());
            }
        };
    }
}
