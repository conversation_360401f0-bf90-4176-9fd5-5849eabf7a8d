package com.qz.hare.server.module.job.fsm;

import com.qz.hare.server.common.model.job.Job;
import com.qz.hare.server.common.model.job.JobStatus;
import com.qz.hare.server.module.job.JobRepository;
import com.qz.hare.server.module.job.entity.JobEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.stereotype.Component;

/**
 * Job状态机状态持久化实现
 *
 * <AUTHOR>
 * create at 2025/8/19
 */
@Slf4j
@Component
public class JobStateMachineStatePersist implements StateMachinePersist<JobStatus, JobEvent, Job> {

    private JobRepository jobRepository;

    public JobStateMachineStatePersist(JobRepository jobRepository) {
        this.jobRepository = jobRepository;
    }

    @Override
    public void write(StateMachineContext<JobStatus, JobEvent> context, Job contextObj) throws Exception {
        if (contextObj.getStatus() != context.getState()) {
            log.debug("Persist job({}) state to db: {} -> {}", contextObj.getId(), contextObj.getStatus(), context.getState());
            jobRepository.updateById(contextObj.getId(), Update.update(JobEntity.FIELD_NAME_JOB_STATUS, context.getState()));
        } else {
            log.warn("Persist job({}) state ({}) is not change", contextObj.getId(), context.getState());
        }
    }

    @Override
    public StateMachineContext<JobStatus, JobEvent> read(Job contextObj) throws Exception {
        log.debug("Read job({}) state from job entity: {}", contextObj.getId(), contextObj.getStatus());
        return new DefaultStateMachineContext<>(
                contextObj.getStatus(),
                null,
                null,
                null,
                null,
                contextObj.getId().toHexString()
        );
    }
}
