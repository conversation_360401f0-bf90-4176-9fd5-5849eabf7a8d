package com.qz.hare.server.module.job.fsm.action;

import com.qz.hare.common.exception.HareException;
import com.qz.hare.server.common.model.job.Job;
import com.qz.hare.server.common.model.job.JobStatus;
import com.qz.hare.server.config.security.UserDetail;
import com.qz.hare.server.module.job.JobRepository;
import com.qz.hare.server.module.job.fsm.JobEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.statemachine.StateContext;

import static com.qz.hare.server.module.job.fsm.JobStateMachineConfig.*;

/**
 * <AUTHOR>
 * create at 2025/8/20 17:46
 */
public abstract class JobAction {
    protected JobRepository jobRepository;

    protected UserDetail getUserDetail(StateContext<JobStatus, JobEvent> context) {
        return (UserDetail) context.getMessage().getHeaders().get(MESSAGE_HEADER_USER_DETAIL);
    }

    protected Job getJob(StateContext<JobStatus, JobEvent> context) {
        return (Job) context.getMessage().getHeaders().get(MESSAGE_HEADER_JOB);
    }

    protected void success(StateContext<JobStatus, JobEvent> context) {
        context.getExtendedState().getVariables().put(EXTENDED_STATE_VAR_RESULT, true);
    }

    protected void failed(StateContext<JobStatus, JobEvent> context, HareException e) {
        context.getExtendedState().getVariables().put(EXTENDED_STATE_VAR_RESULT, false);
        context.getExtendedState().getVariables().put(EXTENDED_STATE_VAR_ERROR, e);
    }

    public abstract void execution(StateContext<JobStatus, JobEvent> context);

    @Autowired
    public void setJobRepository(JobRepository jobRepository) {
        this.jobRepository = jobRepository;
    }
}
