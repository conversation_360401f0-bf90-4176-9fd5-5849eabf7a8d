package com.qz.hare.server.module.job.fsm.action;

import com.qz.hare.common.exception.HareException;
import com.qz.hare.server.common.model.engine.Engine;
import com.qz.hare.server.common.model.job.Job;
import com.qz.hare.server.common.model.job.JobStatus;
import com.qz.hare.server.common.model.job.dag.Element;
import com.qz.hare.server.common.model.plugin.PluginKey;
import com.qz.hare.server.common.params.Where;
import com.qz.hare.server.config.security.UserDetail;
import com.qz.hare.server.error.ServerErrorCode;
import com.qz.hare.server.module.engine.EngineApi;
import com.qz.hare.server.module.engine.EngineService;
import com.qz.hare.server.module.job.fsm.JobEvent;
import com.qz.hare.server.module.plugin.PluginStoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.statemachine.StateContext;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * create at 2025/8/20 17:25
 */
@Slf4j
@Component
public class ScheduleAndSubmitRunningJob extends JobAction {

    private EngineService engineService;
    private EngineApi engineApi;
    private PluginStoreService pluginStoreService;

    /**
     * 1. assign engine to job
     * 2. notify engine to run job
     * @param context
     */
    public void execution(StateContext<JobStatus, JobEvent> context) {

        Job job = getJob(context);
        UserDetail userDetail = getUserDetail(context);

        Assert.notNull(job, "job is null");
        Assert.notNull(userDetail, "userDetail is null");

        try {
            Engine engine = engineService.assignEngine(new Where(), userDetail);
            List<PluginKey> plugins = job.getDag().getNodes().stream()
                    .map(Element::getPluginKey)
                    .filter(Objects::nonNull).toList();

            // 补全pluginKey 的元信息（checksum），用于客户端判断是否重新下载plugin
            pluginStoreService.completionMetaData(plugins);

            engineApi.startJob(engine.getEngineId(), job);

            success(context);

        } catch (HareException e) {
            log.error("Start job ({}) failed", job.getId().toHexString());
            failed(context, e);
        } catch (Exception e) {
            log.error("Start job ({}) failed, can't call engine to submit job", job.getId().toHexString(), e);
            failed(context, new HareException(ServerErrorCode.NOTIFY_ENGINE_ERROR, e.getMessage()));
        }
    }

    @Autowired
    public void setEngineService(EngineService engineService) {
        this.engineService = engineService;
    }

    @Autowired
    public void setEngineClient(EngineApi engineApi) {
        this.engineApi = engineApi;
    }

    @Autowired
    public void setPluginStoreService(PluginStoreService pluginStoreService) {
        this.pluginStoreService = pluginStoreService;
    }
}
