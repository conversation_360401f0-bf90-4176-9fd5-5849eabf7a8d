package com.qz.hare.server.module.job.fsm.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.statemachine.event.OnStateMachineError;
import org.springframework.statemachine.event.StateMachineEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create at 2025/8/22 11:51
 */
@Slf4j
@Component
public class StateMachineErrorListener implements ApplicationListener<StateMachineEvent> {
    @Override
    public void onApplicationEvent(StateMachineEvent event) {
        if (event instanceof OnStateMachineError error) {
            log.error("event:{}", error.getException().getMessage(), error.getException());
        } else {
            log.debug("event: {}", event);
        }
    }
}
