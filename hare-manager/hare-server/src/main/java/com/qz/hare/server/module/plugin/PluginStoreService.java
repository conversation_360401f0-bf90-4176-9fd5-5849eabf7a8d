package com.qz.hare.server.module.plugin;

import com.networknt.schema.JsonSchema;
import com.qz.hare.common.constants.PluginConstants;
import com.qz.hare.common.exception.CommonErrorCode;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.common.util.ChecksumUtils;
import com.qz.hare.common.util.CompressUtils;
import com.qz.hare.common.util.Configuration;
import com.qz.hare.common.util.PluginUtils;
import com.qz.hare.model.plugin.PluginType;
import com.qz.hare.plugin.api.config.PluginConfig;
import com.qz.hare.server.base.BaseService;
import com.qz.hare.server.common.model.file.FileItem;
import com.qz.hare.server.common.model.plugin.Plugin;
import com.qz.hare.server.common.model.plugin.PluginKey;
import com.qz.hare.server.common.model.plugin.PluginStore;
import com.qz.hare.server.common.params.Filter;
import com.qz.hare.server.common.params.Page;
import com.qz.hare.server.common.params.Where;
import com.qz.hare.server.config.security.UserDetail;
import com.qz.hare.server.error.ServerErrorCode;
import com.qz.hare.server.module.file.FileService;
import com.qz.hare.server.module.plugin.entity.PluginEntity;
import com.qz.hare.server.utils.JsonSchemaUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;

import static com.qz.hare.common.util.CommonUtils.ignoreAnyError;
import static com.qz.hare.server.base.entity.BaseEntity.FIELD_NAME_CREATE_BY;
import static com.qz.hare.server.module.plugin.entity.PluginEntity.*;

/**
 * <AUTHOR>
 * create at 2024/12/31 21:36
 */
@Lazy
@Service
@Slf4j
public class PluginStoreService extends BaseService<Plugin, PluginEntity, ObjectId, PluginRepository> implements PluginStore {
    private final FileService fileService;

    public PluginStoreService(PluginRepository repository, FileService fileService) {
        super(repository);
        this.fileService = fileService;
    }

    public Page<Plugin> find(Filter filter, PluginType pluginType, UserDetail userDetail) {
        if (filter == null) {
            filter = new Filter();
        }
        if (filter.getWhere() == null)
            filter.setWhere(new Where());
        filter.getWhere().and(PluginConstants.PLUGIN_TYPE, pluginType);
        filterDeletedInWhere(filter.getWhere());
        filter.setSort(List.of("metadata.order asc"));
        return find(filter, userDetail);
    }

    /**
     * 上传 connector 到仓库
     *   1. 验证connector是否正确
     *   2. 计算 sha1sum
     *   3. 保存文件到仓库（GridFS或者其他存储）
     *   4. 保存 connector 到数据库
     * @param file
     * @param userDetail
     * @return
     */
    @CacheEvict(value = "plugin", allEntries = true)
    public Plugin uploadPlugin(MultipartFile file, UserDetail userDetail) {

        String filename = file.getOriginalFilename();

        if (filename == null)
            throw new HareException(ServerErrorCode.NOT_FOUND_FILENAME);

        if (!FilenameUtils.isExtension(filename.toLowerCase(), "zip"))
            throw new HareException(CommonErrorCode.UNSUPPORTED_COMPRESS_FORMAT);

        java.io.File tempFileDir = new java.io.File(FileUtils.getTempDirectory(),
                String.format("%s-%s", System.currentTimeMillis(), filename));
        try {
            CompressUtils.unzip(file.getInputStream(), tempFileDir);

            Configuration configuration = PluginUtils.loadPluginConfig(Collections.emptyList(), tempFileDir.getAbsolutePath());
            configuration = unwindPluginConfig(configuration);

            PluginConfig pluginConfig = new PluginConfig(configuration);
            pluginConfig.validate();

            Map<String, Object> metadata = computeMetadata(pluginConfig, userDetail);

            // store icon
            Path iconPath = pluginConfig.getPluginIconPath();
            FileItem iconFile = fileService.storeFile(
                    Files.newInputStream(iconPath),
                    iconPath.getFileName().toString(),
                    Files.probeContentType(iconPath), metadata);

            // store zip
            long checksum = ChecksumUtils.checksumCRC32C(file.getInputStream());
            metadata.put("checksum", checksum);
            FileItem pluginFile = fileService.storeFile(file.getInputStream(), filename, file.getContentType(), metadata);

            PluginType pluginType = pluginConfig.getPluginType();

            PluginEntity entity = PluginEntity.builder()
                    .name(pluginConfig.getPluginName())
                    .version(pluginConfig.getPluginVersion())
                    .latest(pluginConfig.getPluginLatest())
                    .developer(pluginConfig.getPluginDeveloper())
                    .email(pluginConfig.getPluginEmail())
                    .description(pluginConfig.getPluginDescription())

                    .scopeType(pluginConfig.getPluginScopeType())

                    .datasourceType(pluginConfig.getPluginDatasourceType())
                    .pluginType(pluginType)
                    .connectorFormConfig(pluginType == PluginType.connector ? pluginConfig.getPluginConnectorFormConfig() : null)
                    .connectorFormJsonSchema(pluginType == PluginType.connector ? pluginConfig.getPluginConnectorJsonSchema() : null)
                    .parameterFormConfig(pluginConfig.getPluginParameterFormConfig())
                    .parameterFormJsonSchema(pluginConfig.getPluginParameterJsonSchema())

                    .tags(pluginConfig.getPluginTags())

                    .projectId(userDetail.getProjectId())
                    .iconFile(FileItem.builder()
                            .id(iconFile.getId())
                            .filename(iconFile.getFilename())
                            .length(iconFile.getLength())
                            .build())
                    .pluginFile(FileItem.builder()
                            .id(pluginFile.getId())
                            .filename(filename)
                            .length(pluginFile.getLength())
                            .build().checksum(checksum)
                    )

                    .build();

            Criteria criteria = Criteria.where(PluginConstants.PLUGIN_DATASOURCE_TYPE).is(entity.getDatasourceType())
                    .and(PluginConstants.PLUGIN_VERSION).is(entity.getVersion())
                    .and(PluginConstants.PLUGIN_TYPE).is(entity.getPluginType())
                    .and("scopeType").is(entity.getScopeType())
                    .and(FIELD_NAME_CREATE_BY).is(userDetail.getUserId());

            PluginEntity existsEntity = repository.findOne(Query.query(criteria)).orElse(null);

            if (existsEntity != null) {
                fileService.scheduledDeleteFiles(
                                List.of(existsEntity.getIconFile().getId(), existsEntity.getPluginFile().getId()),
                        "Upload new plugin", "plugin", existsEntity.getId(), userDetail);
                entity.setId(existsEntity.getId());
                entity = repository.save(entity, userDetail);
            } else {
                entity = repository.insert(entity, userDetail);
            }
            fileService.setMetadata(List.of(iconFile.getId(), pluginFile.getId()), "srcId", entity.getId());
            return convertToDto(entity, Plugin.class);

        } catch (HareException e) {
            throw e;
        } catch (Exception e) {
            log.error("Process upload connector failed {}", e.getMessage(), e);
            throw new HareException(ServerErrorCode.READ_FILE_DATA_ERROR, e.getMessage(), e);
        } finally {
            ignoreAnyError(() -> Files.deleteIfExists(tempFileDir.toPath()));
        }
    }

    private Map<String, Object> computeMetadata(PluginConfig pluginConfig, UserDetail userDetail) {
        Map<String, Object> metadata = new HashMap<>(5);

        metadata.put("owner", userDetail.getUserId());
        metadata.put("project", userDetail.getProjectId());
        metadata.put("src", "plugin");
        metadata.put(PluginConstants.PLUGIN_TYPE, pluginConfig.getPluginType());
        return metadata;
    }

    protected Configuration unwindPluginConfig(Configuration configuration) {
        int deep = PluginUtils.PLUGIN_KEY_FORMAT.split("\\.").length;

        while (deep-- > 0) {
            Set<String> keys = configuration.getKeys(false);
            var key = keys.stream().findFirst().orElse(null);
            if (key == null)
                return null;
            configuration = configuration.getConfiguration(key);
        }
        return configuration;
    }

    @Cacheable(value = "plugin", key="#pluginType+'-'+#name+'-'+#version+''+#userDetail.userId", condition = "#result != null")
    public Plugin findOne(PluginType pluginType, String name, String version, UserDetail userDetail) {
        Criteria criteria = Criteria.where(FIELD_NAME_PLUGIN_TYPE).is(pluginType)
                .and(FIELD_NAME_PLUGIN_NAME).is(name)
                .and(FIELD_NAME_PLUGIN_VERSION).is(version);
        filterDeletedInQuery(criteria);
        return repository.findOne(Query.query(criteria), userDetail).map(p -> convertToDto(p, Plugin.class)).orElse(null);
    }

    @Override
    public JsonSchema getPluginConnectorFormJsonSchema(PluginKey pluginKey) {
        PluginEntity plugin = getPlugin(pluginKey).orElse(null);
        if (plugin != null && plugin.getConnectorFormJsonSchema() != null) {
            return JsonSchemaUtils.getJsonSchema(plugin.getConnectorFormJsonSchema());
        }
        return null;
    }

    @Override
    public JsonSchema getPluginParameterFormJsonSchema(PluginKey pluginKey) {
        PluginEntity plugin = getPlugin(pluginKey).orElse(null);
        if (plugin != null && plugin.getParameterFormJsonSchema() != null) {
            return JsonSchemaUtils.getJsonSchema(plugin.getParameterFormJsonSchema());
        }
        return null;
    }

    private Criteria queryForPluginKey(PluginKey pluginKey) {
        Assert.notNull(pluginKey, "pluginKey must not be null");
        Assert.notNull(pluginKey.getName(), "plugin name must not be null");
        Assert.notNull(pluginKey.getVersion(), "plugin version must not be null");

        Criteria criteria = Criteria.where(FIELD_NAME_PLUGIN_NAME).is(pluginKey.getName())
                .and(FIELD_NAME_PLUGIN_VERSION).is(pluginKey.getVersion());

        if (pluginKey.getScopeType() != null)
            criteria.and(FIELD_NAME_SCOPE_TYPE).is(pluginKey.getScopeType());
        if (pluginKey.getPluginType() != null)
            criteria.and(FIELD_NAME_PLUGIN_TYPE).is(pluginKey.getPluginType());
        if (StringUtils.isNotEmpty(pluginKey.getProjectId()))
            criteria.and(FIELD_NAME_PROJECT_ID).is(pluginKey.getProjectId());
        return criteria;
    }

    public Optional<PluginEntity> getPlugin(PluginKey pluginKey) {
        return repository.findOne(Query.query(queryForPluginKey(pluginKey)));
    }

    public List<PluginEntity> completionMetaData(List<PluginKey> pluginsKeys) {
        List<PluginEntity> plugins = repository.findAll(
                Query.query(new Criteria().orOperator(pluginsKeys.stream().map(this::queryForPluginKey).toList())));

        Map<PluginKey, PluginEntity> pluginMaps = new HashMap<>();
        plugins.forEach(p -> pluginMaps.put(p.getPluginKey(), p));

        pluginsKeys.forEach(pluginKey -> {
            pluginMaps.keySet().stream().filter(k ->
                    k.getName().equals(pluginKey.getName()) &&
                            k.getVersion().equals(pluginKey.getVersion()) &&
                            k.getPluginType().equals(pluginKey.getPluginType()) ).findFirst().ifPresent(pk -> {
                pluginKey.setChecksum(pluginMaps.get(pk).getPluginFile().getChecksum());
            });
        });
        return plugins;
    }
}
