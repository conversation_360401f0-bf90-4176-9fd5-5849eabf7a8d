package com.qz.hare.server.module.plugin.entity;

import com.qz.hare.model.plugin.PluginType;
import com.qz.hare.model.plugin.ScopeType;
import com.qz.hare.server.base.annotation.SetOnInsert;
import com.qz.hare.server.base.entity.ResourceEntity;
import com.qz.hare.server.common.model.file.FileItem;
import com.qz.hare.server.common.model.plugin.DependVersion;
import com.qz.hare.server.common.model.plugin.PluginKey;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * <AUTHOR>
 * create at 2024/10/17 09:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Document("plugins")
@SuperBuilder
@NoArgsConstructor
@CompoundIndexes(
        @CompoundIndex(unique = true, def = "{name: 1, pluginType: 1, version: 1, scopeType: 1, projectId: 1}")
)
public class PluginEntity extends ResourceEntity<ObjectId> {

    public static final String FIELD_NAME_PLUGIN_TYPE = "pluginType";
    public static final String FIELD_NAME_PLUGIN_NAME = "name";
    public static final String FIELD_NAME_PLUGIN_VERSION = "version";
    public static final String FIELD_NAME_SCOPE_TYPE = "scopeType";
    public static final String FIELD_NAME_PLUGIN_LATEST = "latest";

    /**
     * 开发者自定义名称
     */
    private String name;
    @SetOnInsert
    private String version;
    private Boolean latest;
    private String developer;
    private String email;
    private String description;

    /**
     * 相比name，更标准简化，用于区分不同种类的数据源，例如 mysql,mongodb 等;
     */
    @SetOnInsert
    private String datasourceType;
    /**
     * 插件类型，connector 或者 transformer
     */
    @SetOnInsert
    private PluginType pluginType;

    private FileItem iconFile;
    private FileItem pluginFile;

    private Map<String, Object> connectorFormConfig;    // 创建数据源连接的配置，pluginType == connector 时有效，transformer 直接初始化使用即可，无需预先创建，因此不需要createFormConfig
    private Map<String, Object> connectorFormJsonSchema;// 数据源连接的参数描述

    private Map<String, Object> parameterFormConfig;    // 构建Pipeline时的表单参数配置
    private Map<String, Object> parameterFormJsonSchema;// 构建Pipeline的表单参数描述

    private DependVersion depend;       // 依赖引擎版本

    private Map<String, String> metadata;
    private Map<String, String> tags;

    @SetOnInsert
    private ScopeType scopeType;

    private Boolean isLatest;

    public PluginKey getPluginKey() {
        return PluginKey.builder()
                .name(this.getName())
                .version(this.getVersion())
                .pluginType(this.getPluginType())
                .scopeType(this.getScopeType())
                .build();
    }
}
