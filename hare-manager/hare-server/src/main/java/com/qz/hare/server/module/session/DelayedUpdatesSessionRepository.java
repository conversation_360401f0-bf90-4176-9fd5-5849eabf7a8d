package com.qz.hare.server.module.session;

import com.qz.hare.server.utils.DebounceUtil;
import org.springframework.cache.Cache;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.session.data.mongo.MongoIndexedSessionRepository;
import org.springframework.session.data.mongo.MongoSession;

/**
 * <AUTHOR>
 * create at 2025/8/21 17:20
 */
public class DelayedUpdatesSessionRepository extends MongoIndexedSessionRepository {
    private Cache sessionCache;
    public DelayedUpdatesSessionRepository(MongoOperations mongoOperations, Cache sessionCache) {
        super(mongoOperations);
        this.sessionCache = sessionCache;
    }

    @Override
    public MongoSession findById(String id) {
        MongoSession session = sessionCache.get(id, MongoSession.class);
        if (session == null) {
            session = super.findById(id);
            sessionCache.put(id, session);
        }
        DebounceUtil.debounce("read-" + id, 2000, () -> sessionCache.evict(id));
        return session;
    }

    @Override
    public void save(MongoSession session) {
        sessionCache.put(session.getId(), session);
        DebounceUtil.debounce("write-" + session.getId(), 2000, () -> super.save(session));
    }

    @Override
    public void deleteById(String id) {
        sessionCache.evict(id);
        super.deleteById(id);
    }
}
