package com.qz.hare.server.schedule;

import com.qz.hare.server.module.file.FileService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create at 2024/10/17 20:35
 */
@Slf4j
@Component
public class FileSchedule {

    private final FileService fileService;

    public FileSchedule(FileService fileService) {
        this.fileService = fileService;
    }

    /**
     * clean up files that need to be deleted
     */
    @Scheduled(cron = "0 */5 * * * ?")
    @SchedulerLock(name = "FileSchedule.cleanupFile", lockAtMostFor = "PT0H4M", lockAtLeastFor = "PT0H4M")
    public void cleanupFile() {
        log.debug("clean up files that need to be deleted");
        fileService.cleanupWaitingDeleteFiles();
    }
}
