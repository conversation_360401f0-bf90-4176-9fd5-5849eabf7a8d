package com.qz.hare.server.utils;

import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SpecVersion;
import com.qz.hare.common.util.JsonUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * create at 2025/8/18 09:41
 */
public class JsonSchemaUtils {
    private JsonSchemaUtils() {}

    public static JsonSchema getJsonSchema(Map<String, Object> jsonSchemaMap) {
        return JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V7)
                .getSchema(JsonUtil.convertToJsonNode(jsonSchemaMap));
    }
}
