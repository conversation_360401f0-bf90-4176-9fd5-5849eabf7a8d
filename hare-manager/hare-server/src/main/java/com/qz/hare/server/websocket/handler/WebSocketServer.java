package com.qz.hare.server.websocket.handler;

import com.qz.hare.server.cluster.Cluster;
import com.qz.hare.server.common.model.engine.Engine;
import com.qz.hare.server.config.security.UserDetail;
import com.qz.hare.server.module.engine.EngineService;
import com.qz.hare.ws.WebSocketAttributeKeys;
import com.qz.hare.ws.WebSocketSessionContext;
import com.qz.hare.ws.WebSocketSessionManager;
import com.qz.hare.ws.handler.BaseHandler;
import com.qz.hare.ws.protol.Message;
import com.qz.hare.ws.protol.ReturnCallback;
import com.qz.hare.ws.rpc.message.RpcCallMessage;
import io.micrometer.core.instrument.*;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.PongMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

import static com.qz.hare.server.websocket.WebSocketConnectionKeepalive.CLIENT_REPLY_PONG_TIME;
import static com.qz.hare.ws.WebSocketSessionContext.WEB_SOCKET_CLIENT_ROLE_ENGINE;

/**
 * <AUTHOR>
 * create at 2022/8/12 下午12:46
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketServer extends BaseHandler {

	private final EngineService engineService;
	private final MeterRegistry meterRegistry;
	private final Cluster cluster;

	private Counter websocketConnectCount;
	private Counter websocketDisconnectCount;
	private AtomicLong websocketConnectionActiveCount;

	@PostConstruct
	public void init() {
		Tags tags = Tags.of(Tag.of("client", "engine"));
		websocketConnectCount = Counter.builder("websocket.server.connect")
				.description("The count of Websocket connect")
				.tags(tags)
				.register(meterRegistry);
		websocketDisconnectCount = Counter.builder("websocket.server.disconnect")
				.description("The count of Websocket disconnect")
				.tags(tags)
				.register(meterRegistry);
		websocketConnectionActiveCount = new AtomicLong();
		Gauge.builder("websocket.server.connection.active", () -> websocketConnectionActiveCount)
				.tags(tags)
				.description("Current active websocket connections")
				.register(meterRegistry);
	}

	@Override
	public void afterConnectionEstablished(WebSocketSession session) throws Exception {
		log.debug("Websocket {} connected, extensions {}.", session.getId(), session.getExtensions());
		WebSocketSessionContext sessionContext = WebSocketSessionManager.addClient(session);

		Timer timer = Timer.builder("websocket.server.connection.lag")
				.tags(Tags.of(Tag.of("client", "engine"),
						Tag.of("id", sessionContext.getId()),
						Tag.of("remote", (session.getRemoteAddress() != null ? session.getRemoteAddress() : "").toString())))
				.description("The time from sending the ping message from the server to the client and receiving the reply pong message from the client")
				.register(meterRegistry);
		sessionContext.setTimer(timer);

		List<String> tags = new ArrayList<>(3);
		String group = null;
		String clientRole = sessionContext.getRole();
		if (WEB_SOCKET_CLIENT_ROLE_ENGINE.equals(clientRole)) {
			Engine engine = engineService.online(sessionContext.getEngineId(),
					sessionContext.getString(WebSocketAttributeKeys.CLIENT_IP_FIELD_NAME),
					(UserDetail) sessionContext.getAttribute(WebSocketAttributeKeys.USER_DETAIL_PARAM_NAME));
			tags.add("id=" + engine.getId().toHexString());
			tags.add("projectId=" + engine.getProjectId());
			group = engine.getProjectId();
		}

		tags.add("role=" + clientRole);
		tags.add("lang=" + sessionContext.getLang());

		cluster.getSelf().newClient(sessionContext.getId(), sessionContext.getEngineId(),
				group, tags.toArray(new String[0]));

		websocketConnectCount.increment();
		websocketConnectionActiveCount.incrementAndGet();
	}

	@Override
	public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
		log.debug("{} disconnected.", session.getId());
		WebSocketSessionContext sessionContext = WebSocketSessionManager.removeClient(session);

		if (WEB_SOCKET_CLIENT_ROLE_ENGINE.equals(sessionContext.getRole())) {
			engineService.offline(sessionContext.getEngineId(),
					(UserDetail) sessionContext.getAttribute(WebSocketAttributeKeys.USER_DETAIL_PARAM_NAME));
		}

		cluster.getSelf().removeClient(sessionContext.getId());

		websocketDisconnectCount.increment();
		websocketConnectionActiveCount.decrementAndGet();
		if (sessionContext.getTimer() != null) {
			meterRegistry.remove(sessionContext.getTimer());
		}
	}

	@Override
	protected void handlePongMessage(WebSocketSession session, PongMessage message) throws Exception {
		log.trace("{} receive pong message.", session.getId());
		session.getAttributes().put(CLIENT_REPLY_PONG_TIME, System.currentTimeMillis());
	}

	public void sendMessage(String clientId, Message message) throws IOException {
		sendMessage(clientId, message, null);
	}
	public <T> void sendMessage(String clientId, Message message, ReturnCallback<T> returnCallback) throws IOException {
		WebSocketSessionContext context = getClientContext(clientId);
		if (context == null) {
			returnCallback.onResult(null, "NotFoundClient", "Not found client " + clientId);
			return;
		}
		context.sendMessage(message, returnCallback);
	}

	public <T> void call(String clientId, String beanName, String methodName, List<Object> args, ReturnCallback<T> returnCallback) throws IOException {
		RpcCallMessage message = RpcCallMessage.builder()
				.compress(true)
				.beanName(beanName)
				.methodName(methodName)
				.args(args)
				.dst(clientId)
				.requireReturn(returnCallback != null)
				.build();
		sendMessage(clientId, message, returnCallback);
	}

	public <T> T callSync(String clientId, String beanName, String methodName, List<Object> args, Class<T> returnType) throws IOException, InterruptedException {
		RpcCallMessage message = RpcCallMessage.builder()
				.compress(true)
				.beanName(beanName)
				.methodName(methodName)
				.args(args)
				.dst(clientId)
				.requireReturn(true)
				.build();
		WebSocketSessionContext context = getClientContext(clientId);
		if (context == null) return null;
		return context.executeRpcCallSync(message, returnType);
	}

	private WebSocketSessionContext getClientContext(String clientId) {
		WebSocketSessionContext context = WebSocketSessionManager.getClient(clientId);
		if (context == null) {
			context = WebSocketSessionManager.getClientByEngineId(clientId);
		}
		if (context == null) {
			log.warn("Send message failed, not found WebSocket client by id {}", clientId);
			return null;
		}
		return context;
	}

	@Override
	protected boolean forwardMessage(Message message, String dst, WebSocketSessionContext sessionContext) throws IOException {
		boolean send = super.forwardMessage(message, dst, sessionContext);
		if (!send) {
			cluster.sendMessage(dst, message);
		}
		return true;
	}
}
