application:
  title: 'Hare Manager Server'
  version: '@project.version@'
  description: 'Hare Manager Server'
  build: '@maven.build.timestamp@'

spring:
  application:
    name: Hare Manager Server
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true

  data:
    mongodb:
      uri: 'mongodb://localhost/hare'
      cursorBatchSize: 1000

  messages:
    encoding: UTF-8

  mvc:
    static-path-pattern: /**
    format:
      date-time: yyyyMM-dd HH:mm:ss
  jackson:
    default-property-inclusion: non_null

  codec:
    max-in-memory-size: 10MB
    messages:
      encoding: UTF-8
      cache-seconds: 1
      basename: messages

  lifecycle:
    timeout-per-shutdown-phase: 30s
  web:
    resources:
      static-locations: classpath:/static/

  security:
    filter:
      order: 1
  session:
    mongodb:
      collection-name: sessions
    timeout: 8h

  servlet:
    multipart:
      enabled: true
      max-request-size: 500MB
      max-file-size: 100MB


# https://springdoc.org/#properties
springdoc:
  packagesToScan: com.qz.hare.server
  api-docs:
    path: /openapi.json
    groups:
      enabled: true
    version: OPENAPI_3_1
    enabled: true
  swagger-ui:
    path: /api-docs
    display-request-duration: true
    groups-order: ASC
    operationsSorter: method
  remove-broken-reference-definitions: false

server:
  shutdown: graceful
  port: 9080
  error:
    path: /error
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  http2:
    enabled: true
  max-http-request-header-size: 2MB
  tomcat:
    max-http-form-post-size: 500MB


#  tomcat:
#    mbeanregistry:
#      enabled: true
management:
  endpoints:
    web:
      exposure:
        include: health,loggers,prometheus,metrics
#  server:
#    port: 8081
#  endpoint:
#    health:
#      show-details: always