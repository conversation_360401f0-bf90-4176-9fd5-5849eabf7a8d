SystemError=Catching unknown exceptions {0}.
NotLogin=User not login
IllegalState=Illegal State: {0}
IllegalArgument=Illegal Argument: {0}
WrongPassword=Password is wrong.
StoreFileFailed=Store file failed.
NotFoundOnlineClient=Not found online client.
SendMessageFailed=Send message failed.
NotFoundResourceService=Not found resource service by name {0}
NotFoundResourceModel=Not found resource model by name {0}
NotFoundFilename=Not found filename in request body.
ReadFileDataError=Read file data error {0}
ReadNextSequenceFailed=Read next sequence for '{0}' failed.
NotFoundDatabaseById=Not found database metadata by id {0}
NotFoundResource=Not found resource
ConnectionInUse=Connection in use {0}, cannot be deleted.
JobInUse=Job in use {0}, cannot be deleted.
EmptyPipeline=Job data pipeline cannot be empty
InvalidPipeline=Job data pipeline configuration is invalid {0}
PipelineCycle=Job data pipeline cannot have cycle {0}
MultiplePipeline=Job data pipeline cannot have multiple pipeline instances
NoEngineAvailable=No engine available.
NotAllowedEditing=Not allowed editing {0}
NotifyEngineError=Notify engine error {0}
HandlerJobError=Handler jobRunner error {0}
