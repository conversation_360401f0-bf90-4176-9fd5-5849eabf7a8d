SystemError=\u6355\u83b7\u672a\u77e5\u5f02\u5e38 {0}\u3002
NotLogin=\u7528\u6237\u672a\u767b\u5f55
IllegalState=\u975e\u6cd5\u72b6\u6001\uff1a{0}
IllegalArgument=\u975e\u6cd5\u53c2\u6570\uff1a{0}
WrongPassword=\u5bc6\u7801\u9519\u8bef\u3002
StoreFileFailed=\u5b58\u50a8\u6587\u4ef6\u5931\u8d25\u3002
NotFoundOnlineClient=\u672a\u627e\u5230\u5728\u7ebf\u5ba2\u6237\u7aef\u3002
SendMessageFailed=\u53d1\u9001\u6d88\u606f\u5931\u8d25\u3002
NotFoundResourceService=\u672a\u627e\u5230\u540d\u4e3a {0} \u7684\u8d44\u6e90\u670d\u52a1
NotFoundResourceModel=\u672a\u627e\u5230\u540d\u4e3a {0} \u7684\u8d44\u6e90\u6a21\u578b
NotFoundFilename=\u672a\u5728\u8bf7\u6c42\u6b63\u6587\u4e2d\u627e\u5230\u6587\u4ef6\u540d\u3002
ReadFileDataError=\u8bfb\u53d6\u6587\u4ef6\u6570\u636e\u9519\u8bef {0}
ReadNextSequenceFailed=\u8bfb\u53d6\u201c{0}\u201d\u7684\u4e0b\u4e00\u4e2a\u5e8f\u5217\u5931\u8d25\u3002
NotFoundDatabaseById=\u672a\u627e\u5230\u7b26\u5408 ID \u7684\u6570\u636e\u5e93\u5143\u6570\u636e {0}
NotFoundResource=\u672a\u627e\u5230\u8d44\u6e90
ConnectionInUse=\u8fde\u63a5\u6b63\u5728\u4f7f\u7528\u4e2d {0}\uff0c\u65e0\u6cd5\u5220\u9664\u3002
JobInUse=\u4f5c\u4e1a\u6b63\u5728\u4f7f\u7528\u4e2d {0}\uff0c\u65e0\u6cd5\u5220\u9664\u3002
EmptyPipeline=\u4f5c\u4e1a\u6570\u636e\u7ba1\u9053\u4e0d\u80fd\u4e3a\u7a7a
InvalidPipeline=\u4f5c\u4e1a\u6570\u636e\u7ba1\u9053\u914d\u7f6e\u65e0\u6548 {0}
PipelineCycle=\u4f5c\u4e1a\u6570\u636e\u7ba1\u9053\u4e0d\u80fd\u6709\u5faa\u73af {0}
MultiplePipeline=\u4f5c\u4e1a\u6570\u636e\u7ba1\u9053\u4e0d\u80fd\u6709\u591a\u4e2a\u7ba1\u9053\u5b9e\u4f8b
NoEngineAvailable=\u65e0\u53ef\u7528\u5f15\u64ce\u3002
NotAllowedEditing=\u4e0d\u5141\u8bb8\u7f16\u8f91 {0}
NotifyEngineError=\u901a\u77e5\u5f15\u64ce\u9519\u8bef {0}
HandlerJobError=\u5904\u7406\u7a0b\u5e8f\u4f5c\u4e1a\u9519\u8bef {0}
