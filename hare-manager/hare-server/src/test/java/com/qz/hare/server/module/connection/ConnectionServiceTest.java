package com.qz.hare.server.module.connection;

import com.qz.hare.security.SecurityUtil;
import com.qz.hare.server.common.model.connection.Connection;
import com.qz.hare.server.module.engine.EngineApi;
import com.qz.hare.server.module.engine.EngineService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;

import static org.mockito.Mockito.mock;

/**
 * <AUTHOR>
 * create at 2025/4/17 07:15
 */
public class ConnectionServiceTest {

    @Nested
    class TestDescribeDatasource {
        private ConnectionService connectionService;

        @BeforeEach
        void setup() {

            connectionService = new ConnectionService(
                    mock(ConnectionRepository.class),
                    mock(EngineApi.class),
                    mock(EngineService.class));
        }
        @org.junit.jupiter.api.Test
        void test() {

            Assertions.assertDoesNotThrow(() -> connectionService.describeDatasource(null));

            Connection connection = new Connection();
            Assertions.assertDoesNotThrow(() -> connectionService.describeDatasource(connection));

            connection.setConfig(
                    SecurityUtil.encryptAES("""
                                    {"host":"127.0.0.1","port":"3306","database":"test","username":"root","password":"root"}
                                    """,
                    SecurityUtil.DEFAULT_AES_SECRET_KEY));

            Assertions.assertDoesNotThrow(() -> connectionService.describeDatasource(connection));

            Assertions.assertNotNull(connection.getConfigPlain());

            connection.setConfig(
                    SecurityUtil.encryptAES("""
                                    {"database":"test","username":"root","password":"root"}
                                    """,
                            SecurityUtil.DEFAULT_AES_SECRET_KEY));

            Assertions.assertDoesNotThrow(() -> connectionService.describeDatasource(connection));

            Assertions.assertNull(connection.getConfigPlain());
        }
    }


}
