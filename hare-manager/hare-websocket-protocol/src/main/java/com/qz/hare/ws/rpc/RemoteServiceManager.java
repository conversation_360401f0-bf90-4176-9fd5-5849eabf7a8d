package com.qz.hare.ws.rpc;

import com.qz.hare.common.async.AsyncTaskManager;
import com.qz.hare.common.exception.HareException;
import com.qz.hare.ws.WebSocketSessionContext;
import com.qz.hare.ws.error.WebsocketErrorCode;
import com.qz.hare.ws.protol.ReturnCallback;
import com.qz.hare.ws.rpc.converter.DataConvert;
import com.qz.hare.ws.rpc.message.RpcCallMessage;
import com.qz.hare.ws.rpc.message.RpcCallReturnMessage;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Modifier;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2024/11/27 09:04
 */
@Slf4j
public class RemoteServiceManager {

    @Getter
    private static RemoteServiceManager instance = new RemoteServiceManager();
    private ApplicationContext applicationContext;
    private RemoteServiceManager () {}
    private DataConvert dataConvert = DataConvert.builder().build();

    private Map<String, RemoteServiceHolder> remoteServiceHolders = new HashMap<>();

    public static void loadRemoteServiceOnStarted(ApplicationContext applicationContext) {
        RemoteServiceManager.getInstance()
                .setApplicationContext(applicationContext)
                .loadRemoteService("com.qz.hare");
    }

    public RemoteServiceManager setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
        return this;
    }

    public void loadRemoteService(String... basePackages) {
        ClassPathScanningCandidateComponentProvider scanner =
                new ClassPathScanningCandidateComponentProvider(false);

        scanner.addIncludeFilter(new AnnotationTypeFilter(RemoteService.class));
        for (String basePackage : basePackages) {
            scanner.findCandidateComponents(basePackage)
                    .forEach(this::describeRemoteService);
        }
    }

    private void describeRemoteService(BeanDefinition beanDefinition) {
        try {
            Class<?> serviceType = Class.forName(beanDefinition.getBeanClassName());

            Object service = getBeanFromApplicationContext(serviceType);

            if (service == null)
                service = serviceType.getDeclaredConstructor().newInstance();

            String serviceName = getServiceName(serviceType);

            Object finalService = service;
            boolean includeAllPublicMethod = Arrays.stream(serviceType.getDeclaredMethods())
                    .noneMatch(m -> m.isAnnotationPresent(AllowRemoteCall.class));
            Arrays.stream(serviceType.getDeclaredMethods())
                    .filter(m -> Modifier.isPublic(m.getModifiers()))
                    .filter( m -> includeAllPublicMethod || m.isAnnotationPresent(AllowRemoteCall.class))
                    .forEach(m -> {
                        Set<Parameter> parameters = Arrays.stream(m.getParameters())
                                .filter(p -> !p.isAnnotationPresent(AutoInject.class)).collect(Collectors.toSet());
                        remoteServiceHolders.put(computeCacheKey(serviceName, m.getName(), parameters.size()),
                                RemoteServiceHolder.builder()
                                        .targetService(finalService)
                                        .targetMethod(m)
                                        .dataConvert(dataConvert)
                                        .build());
                    });

        } catch (ClassNotFoundException e) {
            log.error("Not found class {}", beanDefinition.getBeanClassName(), e);
        } catch (InvocationTargetException | InstantiationException | IllegalAccessException | NoSuchMethodException e) {
            log.error("New instance failed {}", beanDefinition.getBeanClassName(), e);
        }
    }

    private Object getBeanFromApplicationContext(Class<?> serviceType) {
        if (applicationContext == null)
            return null;

        String[] names = applicationContext.getBeanNamesForType(serviceType);
        if (names.length == 1)
            return applicationContext.getBean(names[0]);

        if (names.length == 0)
            return null;

        String serviceName = getServiceName(serviceType);
        return applicationContext.getBean(serviceName);
    }

    private String getServiceName(Class<?> serviceType) {
        String[] names = applicationContext.getBeanNamesForType(serviceType);
        if (names.length == 1) {
            return names[0];
        }

        Service serviceAnnotation = serviceType.getAnnotation(Service.class);
        String serviceName = null;
        if (serviceAnnotation != null)
            serviceName = serviceAnnotation.value();

        if (!StringUtils.hasText(serviceName)) {
            Component componentAnnotation = serviceType.getAnnotation(Component.class);
            if (componentAnnotation != null) {
                serviceName = componentAnnotation.value();
            }
        }

        if (!StringUtils.hasText(serviceName)) {
            serviceName = serviceType.getSimpleName();
            if (Character.isLowerCase(serviceName.charAt(0))) {
                return serviceName;
            }
            return Character.toLowerCase(serviceName.charAt(0)) + serviceName.substring(1);
        }
        return serviceName;
    }

    private String computeCacheKey(String beanName, String methodName, int argumentCount) {
        return String.format("%s.%s_%d", beanName, methodName, argumentCount);
    }

    public RpcCallReturnMessage executeRpcCall(RpcCallMessage rpcCallMessage, WebSocketSessionContext webSocketSessionContext) {
        String cacheKey = computeCacheKey(
                rpcCallMessage.getBeanName(), rpcCallMessage.getMethodName(), rpcCallMessage.getArgumentCount());

        RpcCallReturnMessage.RpcCallReturnMessageBuilder<?,?> builder =
                RpcCallReturnMessage.builder().reqId(rpcCallMessage.getReqId());

        if (!remoteServiceHolders.containsKey(cacheKey)) {
            String message = String.format("Not found service for %s.%s arguments count %d",
                    rpcCallMessage.getBeanName(), rpcCallMessage.getMethodName(), rpcCallMessage.getArgumentCount());
            log.warn(message);
            return builder.code(RpcCallReturnMessage.NOT_FOUND_SERVICE).message(message).build();
        }
        RemoteServiceHolder serviceHolder = remoteServiceHolders.get(cacheKey);
        try {
            List<Object> args = rpcCallMessage.getArgs();
            Object data = serviceHolder.invokeMethod(args != null ? args.toArray() : null, webSocketSessionContext, rpcCallMessage);

            if (serviceHolder.isVoid())
                return null;
            if (!Boolean.TRUE.equals(rpcCallMessage.getRequireReturn()))
                return null;

            return builder
                    .compress(rpcCallMessage.isCompress())
                    .code(RpcCallReturnMessage.OK)
                    .data(data)
                    .build();
        } catch (HareException e) {
            throw e;
        } catch (Throwable e) {
            log.error("Execute rpc failed: {}", e.getMessage(), e);
            throw new HareException(WebsocketErrorCode.EXECUTE_RPC_METHOD_ERROR, e,
                    rpcCallMessage.getBeanName(), rpcCallMessage.getMethodName(), e.getMessage());
        }
    }

    public void handlerRpcCallReturn(RpcCallReturnMessage result) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(result.getDst())) {

            return;
        }
        ReturnCallback<Object> callback = AsyncTaskManager.getInstance().getAndRemoveTask(result.getReqId());
        if (callback != null) {
            try {
                if (RpcCallReturnMessage.OK.equals(result.getCode())) {
                    Class<?> type = callback.getReturnType();
                    if (type == null)
                        type = callback.getClass().getDeclaredMethods()[0].getParameterTypes()[0];

                    var tmp = dataConvert.convert(result.getData(), type);
                    callback.onResult(tmp, null, null);
                    log.trace("Callback method success {}, argument {}", result.getReqId(), tmp);
                } else {
                    callback.onResult(null, result.getCode(), result.getMessage());
                    log.trace("Callback method error {}", result.getReqId());
                }

            } catch (Exception e) {
                log.error("Callback method failed {}", result, e);
                callback.onResult(null, "ExecuteOnResultError", e.getMessage());
            }
        } else {
            log.debug("Can't found callback for request {}, {}", result.getReqId(), result);
        }
    }

    public Map<String, Object> getStatus() {

        Map<String, Object> status = new LinkedHashMap<>();
        status.put("service", remoteServiceHolders.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getInvokeCount())));
        status.put("waitingCallback", AsyncTaskManager.getInstance().getStatus());
        return status;
    }

    public void destroy() {

    }
}
