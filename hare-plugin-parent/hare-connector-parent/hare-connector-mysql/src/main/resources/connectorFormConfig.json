{"settings": {"i18n": {"zh-CN": {"mysql": {"deployModel": "部署模式", "deployModelEnum": {"singleton": "单机部署", "replicaSet": "主从架构"}, "hostname": "地址", "hostnameTip": "数据库的地址，它可以是IP地址或者域名，例如：*************", "port": "端口号", "portTip": "数据库的端口号，Mysql默认端口3306", "database": "数据库", "databaseTip": "数据库名称，可以通过 show databases 命令列出Mysql所有数据库，区分大小写", "username": "账号", "usernameTip": "访问数据库的账号名称", "password": "密码", "passwordTip": "访问数据库的账号密码，可以为空", "properties": "连接参数", "propertiesTip": "数据库连接参数，每一个参数使用`key=value`方式编写，多个参数使用`&`符号间隔；<br>例如：key1=value1&key2=value2", "timezone": "时区", "timezoneTip": "指定时区，默认使用数据库默认时区", "includeTables": "包含表", "includeTablesTip": "指定被读取的表，默认读取当前数据库中的所有表，可以使用通配符`*`匹配任意字符，多个表名称使用`,`分割；例如：user,sys_*,order_*_detail,*_log"}}, "en": {"mysql": {"deployModel": "Deployment mode", "deployModelEnum": {"singleton": "Single-machine deployment", "replicaSet": "Master-slave architecture"}, "hostname": "Host", "hostnameTip": "Database host, which can be an IP address or domain name, for example: *************", "port": "Port number", "portTip": "Database port number, Mysql default port 3306", "database": "Database", "databaseTip": "Database name, you can use the show databases command to list all Mysql databases, case-sensitive", "username": "Account", "usernameTip": "Account name to access the database", "password": "Password", "passwordTip": "Account password to access the database, can be empty", "properties": "Connection parameters", "propertiesTip": "Database connection parameters, each parameter is written in the `key=value` format, and multiple parameters are separated by the `&` symbol; <br>For example: key1=value1&key2=value2", "timezone": "Time zone", "timezoneTip": "Specify the time zone, the default time zone of the database is used by default", "includeTables": "Include tables", "includeTablesTip": "Specify the table to be read, all tables in the current database are read by default, and the wildcard `*` can be used to match any character. Multiple table names are separated by `,`; For example: user,sys_*,order_*_detail,*_log"}}}}, "components": [{"field": "connectionType", "label": "connection.connectionType", "component": "Segmented", "formItemProps": {"rules": [{"required": true, "message": "common.required"}]}, "value": "SOURCE_AND_TARGET", "componentProps": {"options": [{"label": "connection.connectionTypeEnum.SOURCE_AND_TARGET", "value": "SOURCE_AND_TARGET"}, {"label": "connection.connectionTypeEnum.SOURCE", "value": "SOURCE"}, {"label": "connection.connectionTypeEnum.TARGET", "value": "TARGET"}]}, "colProps": {"span": 12}}, {"field": "deployModel", "label": "mysql.deployModel", "component": "Select", "value": "<PERSON><PERSON>", "formItemProps": {"rules": [{"required": true, "message": "common.required"}]}, "componentProps": {"options": [{"label": "mysql.deployModelEnum.singleton", "value": "<PERSON><PERSON>"}, {"label": "mysql.deployModelEnum.replicaSet", "value": "ReplicaSet"}]}}, {"field": "hostname", "label": "mysql.hostname", "component": "Input", "helpTip": "mysql.hostnameTip", "formItemProps": {"rules": [{"required": true, "message": "common.required"}]}, "colProps": {"span": 18}}, {"field": "port", "label": "mysql.port", "component": "InputNumber", "helpTip": "mysql.portTip", "value": 3306, "formItemProps": {"rules": [{"required": true, "message": "common.required"}]}, "colProps": {"span": 6}}, {"field": "database", "label": "mysql.database", "component": "Input", "helpTip": "mysql.databaseTip", "formItemProps": {"rules": [{"required": true, "message": "common.required"}]}, "colProps": {"span": 24}}, {"field": "username", "label": "mysql.username", "component": "Input", "helpTip": "mysql.usernameTip", "formItemProps": {"rules": []}, "colProps": {"span": 24}}, {"field": "password", "label": "mysql.password", "component": "Input", "helpTip": "mysql.passwordTip", "colProps": {"span": 24}}, {"field": "properties", "label": "mysql.properties", "component": "Input", "helpTip": "mysql.propertiesTip", "value": "useUnicode=yes&characterEncoding=UTF-8", "colProps": {"span": 24}}, {"field": "timezone", "label": "mysql.timezone", "helpTip": "mysql.timezoneTip", "component": "Select", "componentProps": {"options": [{"label": "UTC-12", "value": -12}, {"label": "UTC-11", "value": -11}, {"label": "UTC-10", "value": -10}, {"label": "UTC-9", "value": -9}, {"label": "UTC-8", "value": -8}, {"label": "UTC-7", "value": -7}, {"label": "UTC-6", "value": -6}, {"label": "UTC-5", "value": -5}, {"label": "UTC-4", "value": -4}, {"label": "UTC-3", "value": -3}, {"label": "UTC-2", "value": -2}, {"label": "UTC-1", "value": -1}, {"label": "UTC", "value": 0}, {"label": "UTC+1", "value": 1}, {"label": "UTC+2", "value": 2}, {"label": "UTC+3", "value": 3}, {"label": "UTC+4", "value": 4}, {"label": "UTC+5", "value": 5}, {"label": "UTC+6", "value": 6}, {"label": "UTC+7", "value": 7}, {"label": "UTC+8", "value": 8}, {"label": "UTC+9", "value": 9}, {"label": "UTC+10", "value": 10}, {"label": "UTC+11", "value": 11}, {"label": "UTC+12", "value": 12}, {"label": "UTC+13", "value": 13}, {"label": "UTC+14", "value": 14}]}}, {"field": "includeTables", "label": "mysql.includeTables", "helpTip": "mysql.includeTablesTip", "component": "Input", "colProps": {"span": 24}}], "jsonSchema": {}}