{"name": "MySQL", "version": "@project.version@", "latest": true, "description": "MySQL 是世界上最受欢迎的开源关系型数据库之一。与其他关系型数据库一样，MySQL 将数据存储在由行和列组成的表中。用户可以使用结构化查询语言（通常称为 SQL）定义、操作、控制和查询数据。", "datasourceType": "MySQL", "pluginType": "connector", "icon": "mysql.svg", "tags": {"owner": "leon"}, "connectorFormConfig": "@connectorFormConfig.json", "connectorJsonSchema": "@connectorJsonSchema.json", "parameterFormConfig": "@parameterFormConfig.json", "parameterJsonSchema": "@parameterJsonSchema.json", "class": "com.qz.hare.connector.mysql.MySQLConnector", "package": "com.qz.hare.connector.mysql", "developer": "leon", "email": "<EMAIL>"}