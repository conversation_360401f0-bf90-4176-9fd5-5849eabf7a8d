{"name": "@hare/cloud-ts", "version": "1.0.0", "description": "<PERSON>e", "author": "<PERSON> <<EMAIL>>", "private": false, "type": "module", "scripts": {"i": "pnpm install", "dev": "pnpm vite --mode dev", "dev:mock": "pnpm vite --mode mock", "ts:check": "pnpm vue-tsc --noEmit --skipLib<PERSON><PERSON>ck", "build:pro": "pnpm vite build --mode pro", "build:gitee": "pnpm vite build --mode gitee", "build:dev": "pnpm vite build --mode dev", "build:test": "pnpm vite build --mode test", "serve:pro": "pnpm vite preview --mode pro", "serve:dev": "pnpm vite preview --mode dev", "serve:test": "pnpm vite preview --mode test", "npm:check": "pnpx npm-check-updates -u", "clean": "pnpx rimraf node_modules", "clean:cache": "pnpx rimraf node_modules/.cache", "lint:eslint": "eslint . --fix \"src/**/*.{js,ts,tsx,vue,html}\"", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,vue,html,md}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.cjs", "prepare": "husky install", "p": "plop", "icon": "esno ./scripts/icon.ts"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.2", "@hare/server-sdk": "workspace:*", "@hare/utils": "workspace:*", "@iconify/iconify": "^3.1.1", "@iconify/vue": "^4.3.0", "@vueuse/core": "^10.11.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.11.0", "cropperjs": "^1.6.2", "dayjs": "^1.11.15", "driver.js": "^1.3.6", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.11.1", "events": "^3.3.0", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "monaco-editor": "^0.50.0", "nprogress": "^0.2.0", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^3.2.3", "qrcode": "^1.5.4", "qs": "^6.14.0", "url": "^0.11.4", "vue": "^3.5.20", "vue-draggable-plus": "^0.5.6", "vue-i18n": "^10.0.8", "vue-json-pretty": "^2.5.0", "vue-router": "^4.5.1", "vue-types": "^5.1.3", "xgplayer": "^3.0.23"}, "dependenciesMeta": {"@hare/utils": {"annotate": "Fix vite import error: The requested module '<>' does not provide an export named '<>'", "injected": true}}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.34.0", "@iconify/json": "^2.2.379", "@intlify/unplugin-vue-i18n": "^4.0.0", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.9", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/node": "^20.19.11", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.14.0", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@unocss/transformer-variant-group": "^0.61.9", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "autoprefixer": "^10.4.21", "chalk": "^5.6.0", "consola": "^3.4.2", "eslint": "^9.34.0", "eslint-config-prettier": "^9.1.2", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^9.33.0", "esno": "^4.8.0", "fs-extra": "^11.3.1", "husky": "^9.1.7", "inquirer": "^10.2.2", "less": "^4.4.1", "lint-staged": "^15.5.2", "mockjs": "^1.1.0", "plop": "^4.0.1", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "postcss-less": "^6.0.0", "prettier": "^3.6.2", "rimraf": "^6.0.1", "rollup": "^4.49.0", "rollup-plugin-visualizer": "^5.14.0", "stylelint": "^16.23.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^14.0.1", "stylelint-config-standard": "^36.0.1", "stylelint-order": "^6.0.4", "terser": "^5.43.1", "typescript": "~5.6.3", "typescript-eslint": "^8.41.0", "unocss": "^0.65.4", "vite": "^6.3.5", "vite-plugin-ejs": "^1.7.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-mock": "^3.0.2", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-url-copy": "^1.1.4", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^2.2.12"}, "packageManager": "pnpm@8.1.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.1.0"}, "license": "MIT"}