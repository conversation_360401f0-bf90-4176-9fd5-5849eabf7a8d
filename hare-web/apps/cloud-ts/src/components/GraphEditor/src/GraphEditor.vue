<script setup lang="ts">
import BorderLayout from '../../BoderLayout'
import { useDesign } from '@/hooks/web/useDesign'
import GraphEditorHeader from './components/GraphEditorHeader.vue'
import { Editor, PROVIDE_KEY_EDITOR } from './Editor'
import GraphEditorContainer from './components/GraphEditorContainer.vue'
import { onMounted, onUnmounted, PropType, provide, ref, unref } from 'vue'
import GraphEditorLeftSidebarV2 from '@/components/GraphEditor/src/components/GraphEditorLeftSidebarV2.vue'
import { Job, JobMigrationTypeEnum, JobTypeEnum } from '@hare/server-sdk'
import { useI18n } from '@/hooks/web/useI18n'
import dayjs from 'dayjs'
import { fromGraphJson } from '@/components/GraphEditor/src/utils'
import { jobApi } from '@/api'
import { showError, showSuccess } from '@/hooks/web/useMessageBox'
import { log } from '@hare/utils'

const { t } = useI18n()
const { getPrefixCls } = useDesign()
const props = defineProps({
  jobType: {
    type: String as PropType<JobTypeEnum>,
    default: JobTypeEnum.Migration
  },
  jobId: {
    type: String,
    required: false
  }
})

let editJob: Job | undefined
const className = getPrefixCls('graph-editor')
const graphContainer = ref<ComponentRef<GraphEditorContainer>>()
const editorHeader = ref<ComponentRef<GraphEditorHeader>>()
const editorLeftSidebar = ref<ComponentRef<GraphEditorLeftSidebarV2>>()
const editor: Editor = new Editor({
  jobType: unref(props.jobType)
})
let alreadyInitialized = false
const initial = () => {
  if (alreadyInitialized) return
  alreadyInitialized = true
  editor.initGraph(graphContainer.value.getContainer())
  editor.on('contextmenu', (e) => {})

  if (props.jobId) {
    jobApi
      .findJobById(props.jobId)
      .then(({ data }) => {
        editJob = data.data as Job
        editor.fromJSON(editJob.graphJson)
        editorHeader.value.setJobName(editJob.name)
      })
      .catch((error) => {
        log.error(error)
      })
  } else {
    editorHeader.value.setJobName(
      t('router.' + props.jobType.toLowerCase()) + dayjs().format('-YYYYMMDDHHmmss')
    )
  }
}

provide(PROVIDE_KEY_EDITOR, editor)

const getJobConfig = (): Job => {
  const graphJson = editor.toJSON()
  return {
    name: editorHeader.value.getJobName(),
    type: props.jobType,
    migrationType: JobMigrationTypeEnum.CdcMigration,
    setting: {},
    dag: fromGraphJson(graphJson),
    graphJson: graphJson
  } as Job
}

const doSave = async () => {
  // 1. 检查节点及任务配置正确
  if (!editor.isValid()) {
    return
  }
  // 2. 调用api保存到管理端
  // 3. 返回保存结果：{ code: 'ok', data: 'job id', message: 'error message' }
  try {
    const job = getJobConfig()

    let response
    if (editJob?.id) {
      response = await jobApi.updateJob(editJob.id, job)
    } else {
      response = await jobApi.createJob(job)
    }
    showSuccess(t('common.saveSuccess'))
    editJob = response.data?.data as Job
    return true
  } catch (e) {
    showError(e)
  }
  return false
}

const doStart = async () => {
  if (await doSave()) {
    try {
      const response = await jobApi.submitJob(editJob?.id)
      if (response.data.data) showSuccess(t('job.action.submitSuccess'))
      else showError(t('job.action.submitFailed') + ', ' + response.data.message)
    } catch (e) {
      log.error(e)
      showError(e)
    }
  }
}

onMounted(() => {
  initial()
})
onUnmounted(() => {
  editor.destroy()
  alreadyInitialized = false
})
</script>

<template>
  <BorderLayout
    :left="{ draggable: true, max: 500, min: 100, width: 300 }"
    :right="{ draggable: true, collapse: true, max: 500, min: 100, width: 200 }"
    :top="{ draggable: false, height: 50 }"
    :bottom="{ draggable: true, max: 500, min: 100, width: 200 }"
    :class="className"
  >
    <template #top>
      <GraphEditorHeader ref="editorHeader" :do-save="doSave" :do-start="doStart" />
    </template>
    <template #left>
      <!-- <GraphEditorLeftSidebar ref="editorLeftSidebar" />-->
      <GraphEditorLeftSidebarV2 ref="editorLeftSidebar" />
    </template>
    <template #right>
      <div>Right Sidebar</div>
    </template>

    <GraphEditorContainer ref="graphContainer" />
  </BorderLayout>
</template>

<style lang="less">
@import 'style.module';
@prefix-cls: ~'@{adminNamespace}-graph-editor';
.@{prefix-cls} {
  --border-radius: 10px;
  --border: 1px solid var(--border-color);
  --node-border-color: @nodeBorderColor;
  --node-border-color-active: @highlightColor;
  --edgeColor: @edgeColor;
  --edgeHighlightColor: @highlightColor;

  .x6-node {
    foreignObject > body {
      border-radius: var(--border-radius);
      border: var(--border);
      overflow: hidden;
    }
    .x6-port-in [magnet='true'] {
      cursor: move;
    }
    .x6-port {
      //display: none;
      opacity: 0;
      .x6-port-body {
        transform: scale(1);
        transition: all 0.1s linear;
        &:hover {
          transform: scale(1.3);
          transition: all 0.1s linear;
        }
      }
    }
    &:hover,
    &.x6-node-selected {
      .x6-port {
        //display: block;
        opacity: 1;
      }
      body {
        border-color: var(--node-border-color-active);
        box-shadow: 2px 5px 12px var(--box-shadow-color-12);
      }
    }
  }

  .snap-line line {
    stroke: var(--node-border-color-active);
    stroke-dasharray: 5, 5;
    stroke-linecap: round;
  }
}
</style>
