import { Graph, Node } from '@antv/x6'

export const components = (g: Graph): string[][] => {
  const visited: Record<string, boolean> = {}
  const cmpts: string[][] = []
  let cmpt: string[] = []

  function dfs(n: Node<Node.Properties>): void {
    const v = n.id
    if (v in visited) return
    visited[v] = true
    cmpt.push(v)
    g.getSuccessors(n).forEach(dfs)
    g.getPredecessors(n).forEach(dfs)
  }

  g.getNodes().forEach((n) => {
    cmpt = []
    dfs(n)
    if (cmpt.length > 0) {
      cmpts.push(cmpt)
    }
  })

  return cmpts
}
