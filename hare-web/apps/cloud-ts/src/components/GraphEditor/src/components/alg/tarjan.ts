import { Graph, Node } from '@antv/x6'

interface VisitedEntry {
  onStack: boolean
  lowLink: number
  index: number
}

function tarjan(graph: Graph): string[][] {
  let index: number = 0
  const stack: string[] = []
  const visited: Record<string, VisitedEntry> = {}
  const results: string[][] = []

  function dfs(node: Node<Node.Properties>): void {
    const v = node.id
    const entry: VisitedEntry = (visited[v] = {
      onStack: true,
      lowLink: index,
      index: index++
    })
    stack.push(v)

    graph.getSuccessors(node).forEach((n) => {
      const w = n.id
      if (!(w in visited)) {
        dfs(n)
        entry.lowLink = Math.min(entry.lowLink, visited[w].lowLink)
      } else if (visited[w].onStack) {
        entry.lowLink = Math.min(entry.lowLink, visited[w].index)
      }
    })

    if (entry.lowLink === entry.index) {
      const cmpt: string[] = []
      let w: string
      do {
        w = stack.pop()!
        visited[w].onStack = false
        cmpt.push(w)
      } while (v !== w)
      results.push(cmpt)
    }
  }

  graph.getNodes().forEach((node) => {
    if (!(node.id in visited)) {
      dfs(node)
    }
  })

  return results
}

export { tarjan }
