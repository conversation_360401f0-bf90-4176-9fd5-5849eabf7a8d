<script setup lang="ts">
import {
  Connection,
  ConnectionConnectionTypeEnum,
  HareError,
  PluginPluginTypeEnum,
  StepItem
} from '@hare/server-sdk'
import { inject, PropType, ref } from 'vue'
import { connectionApi } from '@/api'
import { log } from '@hare/utils'
import { ElEmpty, ElPagination, ElTooltip } from 'element-plus'
import { QueryInput } from '@/components/QueryInput'
import { useI18n } from '@/hooks/web/useI18n'
import { useDesign } from '@/hooks/web/useDesign'
import { PROVIDE_KEY_EDITOR } from '@/components/GraphEditor/src/Editor'
import { getImageUrl, getNodeParamsFromConnection, getTags } from './../../utils'
import { setSelectedCls } from './index'
import { loadSchema } from '@/views/Common/commons'
import { showError, showSuccess } from '@/hooks/web/useMessageBox'
import { Refresh } from '@/components/Icon'

const emits = defineEmits([
  'loaded',
  'selected',
  'itemMousedown',
  'itemMousemove',
  'itemMouseup',
  'itemDblClick'
])
const props = defineProps({
  include: {
    type: Array as PropType<ConnectionConnectionTypeEnum>,
    default: undefined
  }
})

const editor = inject(PROVIDE_KEY_EDITOR)
const { t } = useI18n()

const { getPrefixCls } = useDesign()
const className = getPrefixCls('database-data-list')
let selectedConnection: Connection = null
const connectionLoadding = ref<boolean>(false)
const where: Where = {}
const connectionPageData = ref<Page>({ total: 0, items: [], pageSize: 10, currentPage: 1 })
const searchConnectionByKeyWord = (val: string | undefined) => {
  if (val) {
    const like = { $regex: val, $options: 'i' }
    where.$or = [{ name: like }, { configPlain: like }]
  } else {
    delete where.$or
  }
  loadConnections(1)
}

const loadConnections = async (page: number) => {
  try {
    connectionLoadding.value = true
    connectionPageData.value.currentPage = page
    const filter = {
      skip: (page - 1) * connectionPageData.value.pageSize,
      limit: connectionPageData.value.pageSize,
      where: where,
      fields: {
        name: 1,
        connectionType: 1,
        connectorName: 1,
        connectorVersion: 1,
        configPlain: 1,
        totalTables: 1
      },
      sort: ['updatedAt desc']
    } as Filter
    const { data } = await connectionApi.findConnection(JSON.stringify(filter))
    connectionPageData.value.total = data.data?.total
    connectionPageData.value.items = data.data?.items
    emits('loaded', connectionPageData.value)
  } catch (e) {
    log.error(e)
  } finally {
    connectionLoadding.value = false
  }
}
const handlerLoadSchema = async (row: Connection) => {
  if (!row) return
  row.loadingSchema = true
  row.loadingSchemaProgress = 0

  await loadSchema(row.id, (loadSchemaProgress) => {
    for (let i = 0; i < connectionPageData.value.items.length; i++) {
      const connection = connectionPageData.value.items[i]
      if (connection.id === loadSchemaProgress.connectionId) {
        connection.loadingSchemaProgress = loadSchemaProgress.progress
        if (loadSchemaProgress.success !== undefined) {
          connection.loadingSchema = false
          if (loadSchemaProgress.success) {
            showSuccess(connection.name + t('connection.loadSchemaSuccess'))
            loadConnections(connectionPageData.value.currentPage)
          } else {
            const failedStepItem: StepItem | null = loadSchemaProgress.stepItems.find(
              (v) => v.result == false
            )
            const error = new HareError()
            error.code = failedStepItem?.code
            error.message = failedStepItem?.msg
            showError(error)
          }
        }
        break
      }
    }
  })
}
const handleConnectionPageSizeChange = (size: number) => {
  connectionPageData.value.pageSize = size
  loadConnections(connectionPageData.value.currentPage)
}
const handlerSelectedConnection = (e, connection: Connection) => {
  setSelectedCls(e)
  if (selectedConnection?.id !== connection.id) {
    selectedConnection = connection
    emits('selected', selectedConnection, e)
  }
}
if (props.include && props.include.length > 0) {
  where['connectionType'] = {
    $in: props.include
  }
}
loadConnections(connectionPageData.value.currentPage)

defineExpose({
  loadConnections
})
</script>

<template>
  <div :class="className">
    <QueryInput ref="queryInput" class="w-full" size="small" @input="searchConnectionByKeyWord" />
    <ElEmpty
      v-if="connectionPageData.items.length === 0"
      :image-size="80"
      :description="t('common.noData')"
      v-loading="connectionLoadding"
    />
    <ul v-else class="database-list" v-loading="connectionLoadding">
      <li
        v-for="conn in connectionPageData?.items"
        :key="conn.id"
        @click="(e) => handlerSelectedConnection(e, conn)"
        @mousedown="(e) => emits('itemMousedown', e)"
        @mousemove="
          (e) =>
            emits('itemMousemove', e, {
              type: editor?.isDataDevelopment() ? 'table' : 'database',
              pluginKey: {
                name: conn.connectorName,
                version: conn.connectorVersion,
                pluginType: PluginPluginTypeEnum.Connector
              },
              ...getNodeParamsFromConnection(conn)
            })
        "
        @mouseup="(e) => emits('itemMouseup', e)"
        @dblclick="(e) => emits('itemDblClick', conn, e)"
      >
        <div
          class="flex items-center"
          :title="` ${t('connection.connectionTypeEnum.' + conn.connectionType)}: ${conn.configPlain}`"
        >
          <img class="icon" :src="getImageUrl(conn)" />
          <div class="flex-1 text flex flex-col p-l-10px">
            <div class="title flex items-center">
              <div class="flex-1 overflow-hidden text-ellipsis">
                {{ conn.name }}
                <span class="desc">
                  ({{ conn.totalTables || 0 }}
                  {{
                    conn?.loadingSchema === true
                      ? ' - ' + (conn?.loadingSchemaProgress || 0) + '%'
                      : ''
                  }})
                </span>
              </div>
              <div class="tags">
                <ElTooltip :raw-content="true" :content="t('connection.loadSchema')">
                  <BaseButton
                    :icon="Refresh"
                    :border="false"
                    circle
                    :disabled="conn?.loadingSchema === true"
                    @click="
                      (e) => {
                        handlerLoadSchema(conn)
                        e.stopPropagation()
                      }
                    "
                  />
                </ElTooltip>
                <span :class="tag" v-for="tag in getTags(conn.connectionType)" :key="tag">
                  {{ tag }}
                </span>
              </div>
            </div>
            <div class="desc">{{ conn.configPlain }}</div>
          </div>
        </div>
      </li>
    </ul>
    <ElPagination
      hide-on-single-page
      v-model:currentPage="connectionPageData.currentPage"
      :page-size="connectionPageData.pageSize"
      :pager-count="5"
      :page-sizes="[10, 20, 30, 50, 100]"
      layout="prev, pager, next, ->, sizes"
      :total="connectionPageData.total"
      size="small"
      @currentChange="loadConnections"
      @sizeChange="handleConnectionPageSizeChange"
    />
  </div>
</template>

<style lang="less">
@import './data-list';
@cls: ~'@{adminNamespace}-database-data-list';
</style>
