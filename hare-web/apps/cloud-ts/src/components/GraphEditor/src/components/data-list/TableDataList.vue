<script setup lang="ts">
import { computed, inject, PropType, ref, watch } from 'vue'
import { connectionApi } from '@/api'
import { log } from '@hare/utils'
import { ElEmpty, ElPagination } from 'element-plus'
import { QueryInput } from '@/components/QueryInput'
import { PROVIDE_KEY_EDITOR } from '@/components/GraphEditor/src/Editor'
import { Connection, PluginPluginTypeEnum, Schema } from '@hare/server-sdk'
import { useI18n } from '@/hooks/web/useI18n'
import { useDesign } from '@/hooks/web/useDesign'
import { getImageUrl, getNodeParamsFromConnection, getTags } from './../../utils'
import { setSelectedCls } from './index'

const props = defineProps({
  selectedConnection: {
    type: Object as PropType<Connection>,
    default: undefined
  }
})
const emits = defineEmits(['selected', 'itemMousedown', 'itemMousemove', 'itemMouseup'])
let selectedTable: Schema = null
const editor = inject(PROVIDE_KEY_EDITOR)
const { t } = useI18n()
const { getPrefixCls } = useDesign()
const className = getPrefixCls('table-data-list')
const tableLoading = ref<boolean>(false)
const searchTableWhere: Where = {}
const tablePageData = ref<Page>({ total: 0, items: [], pageSize: 10, currentPage: 1 })
const searchTableByKeyWord = (val: string | undefined) => {
  if (val) {
    const like = { $regex: val, $options: 'i' }
    searchTableWhere.$or = [{ name: like }, { configPlain: like }]
  } else {
    delete searchTableWhere.$or
  }
  loadTable(1)
}

const selectedConnection = computed(() => props.selectedConnection)

const loadTable = async (page: number) => {
  if (!selectedConnection.value) return
  try {
    tableLoading.value = true
    tablePageData.value.currentPage = page
    const filter = {
      skip: (page - 1) * tablePageData.value.pageSize,
      limit: tablePageData.value.pageSize,
      where: searchTableWhere,
      fields: {
        id: 1,
        name: 1,
        connectionId: 1,
        version: 1,
        schemaType: 1,
        'table.type': 1,
        'table.schema': 1,
        'table.defaultCharsetName': 1
      }
    } as Filter
    const { data } = await connectionApi.findTableByConnectionId(
      selectedConnection.value.id,
      JSON.stringify(filter)
    )
    tablePageData.value.total = data.data?.total
    tablePageData.value.items = data.data?.items
  } catch (e) {
    log.error(e)
  } finally {
    tableLoading.value = false
  }
}

const handlerSelectedTable = (e, table) => {
  setSelectedCls(e)
  if (selectedTable?.id !== table.id) {
    selectedTable = table
    emits('selected', selectedTable, e)
  }
}
const handleTablePageSizeChange = (size: number) => {
  tablePageData.value.pageSize = size
  loadTable(tablePageData.value.currentPage)
}

watch(selectedConnection, () => loadTable(1))
getImageUrl('')
</script>

<template>
  <div :class="className">
    <QueryInput ref="queryInput" class="w-full" size="small" @input="searchTableByKeyWord" />
    <ElEmpty
      v-if="tablePageData.items.length === 0"
      :image-size="80"
      :description="t('common.noData')"
      v-loading="tableLoading"
    />
    <ul v-else class="database-list" v-loading="tableLoading">
      <li
        data-type="database"
        v-for="table in tablePageData?.items"
        :key="table.id"
        @click="(e) => handlerSelectedTable(e, table)"
        @mousedown="(e) => emits('itemMousedown', e)"
        @mousemove="
          (e) =>
            emits(
              'itemMousemove',
              e,
              Object.assign(
                {
                  pluginKey: {
                    name: selectedConnection.connectorName,
                    version: selectedConnection.connectorVersion,
                    pluginType: PluginPluginTypeEnum.Connector
                  }
                },
                getNodeParamsFromConnection(selectedConnection),
                {
                  type: 'table',
                  name: table.name,
                  tableId: table.id
                }
              )
            )
        "
        @mouseup="(e) => emits('itemMouseup', e)"
      >
        <div class="flex items-center">
          <Icon icon="carbon:table-split" :size="20" color="#1a91f8" />
          <div class="flex-1 text flex flex-col p-l-10px">
            <div class="title flex items-center">
              <div class="flex-1 overflow-hidden text-ellipsis">
                {{ table.name }}
              </div>
              <div class="tags">
                <span
                  :class="tag"
                  v-for="tag in getTags(selectedConnection?.connectionType)"
                  :key="tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
            <div class="desc">{{
              (table.table?.schema ? table.table?.schema + '.' : '') + table.name
            }}</div>
          </div>
        </div>
      </li>
    </ul>
    <ElPagination
      hide-on-single-page
      v-model:current-page="tablePageData.currentPage"
      :page-size="tablePageData.pageSize"
      :pager-count="5"
      :page-sizes="[10, 20, 30, 50, 100]"
      layout="prev, pager, next, ->, sizes"
      :total="tablePageData.total"
      size="small"
      @currentChange="loadTable"
      @sizeChange="handleTablePageSizeChange"
    />
  </div>
</template>

<style scoped lang="less">
@import './data-list';
@cls: ~'@{adminNamespace}-table-data-list';
.@{cls} {
}
</style>
