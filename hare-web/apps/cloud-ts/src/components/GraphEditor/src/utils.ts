import variables from './style.module.less'
import { nodeBorderColorActive } from '@/components/GraphEditor/src/variables'
import { Connection, ConnectionConnectionTypeEnum, Dag } from '@hare/server-sdk'
import { PATH_URL } from '@/axios'
import { Cell } from '@antv/x6'

export const PORT_GROUP_IN = 'in'
export const PORT_GROUP_OUT = 'out'

export const addEdgeTools = (edge) => {
  edge.addTools([
    {
      name: 'button-remove',
      args: {
        markup: [
          {
            tagName: 'circle',
            selector: 'button',
            attrs: {
              r: 10,
              fill: 'red',
              cursor: 'pointer'
            }
          },
          {
            tagName: 'path',
            selector: 'icon',
            attrs: {
              d: 'M -5 -5 5 5 M -5 5 5 -5',
              fill: 'none',
              stroke: '#FFFFFF',
              'stroke-width': 2,
              'pointer-events': 'none'
            }
          }
        ],
        distance: '50%'
      }
    },
    {
      name: 'target-arrowhead',
      args: {
        attrs: {
          fill: nodeBorderColorActive
        }
      }
    }
  ])
}

export const removeEdgeTools = (edge) => {
  edge.removeTools()
}

export const highlightEdge = (edge) => {
  edge.setAttrs({
    line: {
      stroke: variables.edgeHighlightColor
    }
  })
}

export const isOutPort = (port) => {
  return port?.group === PORT_GROUP_OUT
}

export const isInPort = (port) => {
  return port?.group === PORT_GROUP_IN
}

export const unhighlightEdge = (edge) => {
  edge.setAttrs({
    line: {
      stroke: variables.edgeColor
    }
  })
}

export const canbeAsSource: boolean = (connectionType: ConnectionConnectionTypeEnum) => {
  return (
    connectionType === ConnectionConnectionTypeEnum.Source ||
    connectionType === ConnectionConnectionTypeEnum.SourceAndTarget
  )
}

export const canbeAsTarget: boolean = (connectionType: ConnectionConnectionTypeEnum) => {
  return (
    connectionType === ConnectionConnectionTypeEnum.Target ||
    connectionType === ConnectionConnectionTypeEnum.SourceAndTarget
  )
}

export const getImageUrl: string = (conn?: Connection) => {
  if (!conn) return
  return `${PATH_URL}/api/v1/plugin/connector/icon?plugin=${conn.connectorName}:${conn.connectorVersion}`
}

export const getTags: string[] = (connectionType: ConnectionConnectionTypeEnum) => {
  switch (connectionType) {
    case ConnectionConnectionTypeEnum.SourceAndTarget:
      return ['S', 'T']
    case ConnectionConnectionTypeEnum.Source:
      return ['S']
    case ConnectionConnectionTypeEnum.Target:
      return ['T']
    default:
      return []
  }
}

export const getNodeParamsFromConnection: object = (conn: Connection) => {
  return {
    name: conn.name,
    connectionId: conn.id,
    icon: getImageUrl(conn),
    connectionType: conn.connectionType
  }
}

export const fromGraphJson: Dag = (graphJson: { cells: Cell.Properties[] }) => {
  const dag = {
    nodes: [],
    edges: []
  } as Dag

  graphJson.cells.forEach((cell) => {
    const params = Object.assign({}, cell?.data?.params || {})

    if (cell.source && cell.target) {
      dag.edges?.push({
        id: cell.id,
        src: cell.source?.cell || cell.source,
        dst: cell.target?.cell || cell.target,
        params
      })
    } else {
      const pluginKey = params.pluginKey
      delete params.pluginKey
      dag.nodes?.push({
        id: cell.id,
        name: params?.name || 'Default Name',
        type: params?.type,
        params,
        pluginKey
      })
    }
  })
  return dag
}
