import { HareError } from '@hare/server-sdk/src'
import { ElLink, ElMessage, ElMessageBox, ElMessageBoxOptions, ElNotification } from 'element-plus'
import { h } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useDialog } from '@/hooks/web/useDialog'
import { NotificationOptions } from 'element-plus/es/components/notification/src/notification'

const { t } = useI18n('message')

const showErrorStack = (title: string, e: Error | HareError | string) => {
  const stack: { [key: string]: string } = {}
  let message: string = ''
  if (e instanceof HareError) {
    message = e.message
    if (e.errorStack) stack.b = e.errorStack
    if (e.stack) stack.f = e.stack
  } else if (e instanceof Error) {
    message = e.message
    if (e.stack) stack.f = e.stack
  } else {
    stack.f = e
  }

  const renderStack = (t: string, s: string) => {
    return s
      ? h('details', {}, [
          h('summary', {}, [t]),
          h(
            'pre',
            {
              class: 'w-full overflow-auto bg-#eee mx-0 mt-5px mb-24px p-5px max-h-500px'
            },
            s
          )
        ])
      : undefined
  }

  const { openDialog } = useDialog(
    h(
      'p',
      {
        class: 'p-24px'
      },
      [
        message
          ? h(
              'p',
              {
                class: 'w-full text-wrap'
              },
              message
            )
          : undefined,
        renderStack('Backend Stack: ', stack.b),
        renderStack('Frontend Stack: ', stack.f)
      ]
    ),
    {
      dialogProps: {
        width: 1000,
        title,
        destroyOnClose: true
      }
    }
  )
  openDialog()
  /*ElMessageBox({
        title,
        dangerouslyUseHTMLString: true,
        message: h('p', {}, [
            renderStack('Backend', stack.b),
            renderStack('Frontend', stack.f),
        ])
    }).then(() => {})*/
}

let messageBox: ElMessage = undefined
let timeoutId = null
const closeMessageBox = (callback) => {
  const fn = typeof callback === 'function' ? callback : () => {}
  if (timeoutId) {
    clearTimeout(timeoutId)
  }
  if (messageBox) {
    messageBox.close()
  }
  timeoutId = setTimeout(fn, 100)
}

const showError = (e: Error | HareError | string) => {
  let message: string
  let title: string = t('title')
  if (typeof e === 'string') {
    message = e
  } else if (e instanceof HareError) {
    title = e.code ? `${title} (${e.code})` : title
    message = e.message + (e.code ? '(' + e.code + ')' : '')
  } else {
    message = e.message
  }
  const isError = e instanceof HareError || e instanceof Error
  closeMessageBox(() => {
    messageBox = ElMessage({
      message: h('p', [
        h(
          'span',
          {
            style: {
              'font-size': 'var(--el-link-font-size)',
              'vertical-align': 'middle',
              color: 'red'
            }
          },
          message
        ),
        isError
          ? h(
              ElLink,
              {
                type: 'primary',
                class: 'ml-10px',
                onClick: () => showErrorStack(title, e)
              },
              t('detail')
            )
          : undefined
      ]),
      type: 'error',
      plain: false,
      showClose: true,
      duration: 5000,
      grouping: true
    })
  })
}
const showMessage = (m: string, t: string) => {
  closeMessageBox(() => {
    messageBox = ElMessage({
      message: m,
      type: t,
      plain: false,
      grouping: true
    })
  })
}
const showInfo = (m: string) => {
  showMessage(m, 'info')
}
const showWarn = (m: string) => {
  showMessage(m, 'warning')
}
const confirm = (message: string, title: string, options?: ElMessageBoxOptions) => {
  return ElMessageBox.confirm(message, title, options)
}
const showSuccess = (m: string) => {
  showMessage(m, 'success')
}

const showNotify = (notifyOption: NotificationOptions) => {
  ElNotification(notifyOption)
}

export { showSuccess, showInfo, showWarn, confirm, showError, showErrorStack, showNotify }
