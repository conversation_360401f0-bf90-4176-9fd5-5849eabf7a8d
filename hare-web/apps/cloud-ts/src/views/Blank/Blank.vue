<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { showError, showSuccess, showWarn } from '@/hooks/web/useMessageBox'

let messageBox = undefined

const closeMessageBox = (callback) => {
  if (messageBox) {
    messageBox.close()
    setTimeout(callback, 0)
  } else {
    callback()
  }
}

const open1 = () => {
  //showSuccess('保存成功')
  closeMessageBox(() => {
    messageBox = ElMessage.success({
      message: '保存成功' + Math.random(),
      grouping: true
    })
  })
}
const open2 = () => {
  //showError(new HareError('展示错误信息'))
  closeMessageBox(() => {
    messageBox = ElMessage.error({
      message: '保存失败'
    })
  })
}
const open3 = () => {
  closeMessageBox(() => {
    messageBox = ElMessage.warning({
      message: '无法排序'
    })
  })
}
const open4 = () => {
  showSuccess('保存成功')
  showWarn('警告')
}
const open5 = () => {
  showError('保存失败')
}
</script>

<template>
  <div>
    <BaseButton @click="open1">Success</BaseButton>
    <BaseButton @click="open2">Error</BaseButton>
    <BaseButton @click="open3">Warn</BaseButton>
    <BaseButton @click="open4">Success</BaseButton>
    <BaseButton @click="open5">Error</BaseButton>
  </div>
</template>

<style scoped lang="less"></style>
