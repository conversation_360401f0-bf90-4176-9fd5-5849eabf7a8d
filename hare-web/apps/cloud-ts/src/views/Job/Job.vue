<script setup lang="tsx">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import { ElSpace, ElTag } from 'element-plus'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { computed, reactive, ref, unref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useEventBus } from '@/hooks/event/useEventBus'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { Link } from '@/components/Link'
import { formatToDateTime } from '@/utils/dateUtil'
import { QueryInput } from '@/components/QueryInput'
import { BaseButton } from '@/components/Button'
import { Delete, Plus, Refresh } from '@/components/Icon'
import { jobApi } from '@/api'
import { Job, JobTypeEnum } from '@hare/server-sdk/src'
import { log } from '@hare/utils'
import { showError } from '@/hooks/web/useMessageBox'

const { push } = useRouter()
const route = useRoute()

const jobType = route.meta.jobType || JobTypeEnum.Migration

const ids = ref<string[]>([])
const handleSelectedChanged = (selected: Job[]) => {
  ids.value = selected?.map((v) => unref(v).id).filter((v) => v !== undefined)
}
const batchSelected = computed(() => unref(ids).length > 0)
const where: Where = {
  type: jobType
}
const searchByKeyWord = (val: string | undefined) => {
  if (val) {
    const like = { $regex: val, $options: 'i' }
    where.$or = [{ name: like }, { configPlain: like }]
  } else {
    delete where.$or
  }
  getList()
}
const sortProps: { [key: string]: string } = { updatedAt: 'descending' }
const defaultSort = Object.keys(sortProps).map((k) => {
  return { prop: k, order: sortProps[k] }
})[0]
const handleSortChange = (sort: { column: any; prop: string; order: any }) => {
  if (sort.order) {
    sortProps[sort.prop] = sort.order
  } else {
    delete sortProps[sort.prop]
  }
  Object.keys(sortProps).forEach((k) => {
    if (sort.prop !== k) delete sortProps[k]
  })
  getList()
}

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const { currentPage, pageSize } = tableState
    const filter = {
      skip: (unref(currentPage) - 1) * unref(pageSize),
      limit: unref(pageSize),
      where: where,
      sort: Object.keys(sortProps).map(
        (k) => `${k} ${sortProps[k] === 'ascending' ? 'asc' : 'desc'}`
      ),
      fields: {
        dag: 0,
        graphJson: 0
      }
    } as Filter
    const { data } = await jobApi.findJob(JSON.stringify(filter))
    return data.data as Page
  },
  fetchDelApi: async () => {
    const res = await jobApi.deleteJob({ ids: unref(ids) })
    return !!(res.data?.data && res.data?.data > 0)
  }
})
const { loading, dataList, total, currentPage, pageSize } = tableState
const { getList, getElTableExpose, delList } = tableMethods

useEventBus({
  name: 'getList',
  callback: (type: string) => {
    if (type === 'add') {
      currentPage.value = 1
    }
    getList()
  }
})

const { t } = useI18n()

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    table: {
      type: 'selection'
    }
  },
  {
    field: 'name',
    label: 'job.name',
    //width: 230,
    sortable: true
  },
  {
    field: 'migrationType',
    label: 'job.migrationTypeLabel',
    width: 150,
    slots: {
      default: (data: any) => {
        return <ElTag>{t('job.migrationType.' + data?.row?.migrationType)} </ElTag>
      }
    }
  },
  {
    field: 'status',
    label: 'job.statusLabel',
    width: 110,
    sortable: true,
    slots: {
      default: (data: any) => {
        return <ElTag>{t('job.status.' + data?.row?.status)}</ElTag>
      }
    }
  },
  {
    field: 'updatedAt',
    label: 'common.updatedAt',
    width: 155,
    sortable: true,
    formatter: (row: any, col: any, val: any) => {
      if (val) return formatToDateTime(val)
      else return val
    }
  },
  {
    field: 'action',
    width: 200,
    label: 'common.action',
    headerAlign: 'left',
    align: 'left',
    table: {
      slots: {
        default: (data: any) => {
          return (
            <>
              <Link type="primary" onClick={async () => await action(data.row, 'copy')}>
                {t('job.copy')}
              </Link>
              <Link type="primary" onClick={() => gotoJobEditor(data.row)}>
                {t('common.edit')}
              </Link>
              <Link type="danger" onClick={() => delData(data.row)}>
                {t('common.del')}
              </Link>
            </>
          )
        }
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const delLoading = ref(false)
const delData = async (row: Job | null) => {
  try {
    const elTableExpose = await getElTableExpose()
    ids.value = row ? [row?.id] : elTableExpose?.getSelectionRows().map((v: Job) => v.id) || []
    delLoading.value = true
    await delList(unref(ids).length)
  } catch (e) {
    log.error(e)
    showError(e)
  } finally {
    delLoading.value = false
  }
}

const action = async (row: Job, type: string) => {
  if (type === 'copy') {
    try {
      const job = (await jobApi.copyJobById(row.id)).data?.data
      await getList()
    } catch (e) {
      log.error(e)
      showError(e)
    }
  }
}

const gotoJobEditor = async (job?: Job) => {
  if (job?.id) {
    window.open(`/${jobType.toLowerCase()}/edit/${job.id}`, 'editJob_' + job.id)
  } else {
    window.open(`/${jobType.toLowerCase()}/create`)
  }
}
</script>

<template>
  <ContentWrap :title="jobType === 'MIGRATION' ? t('router.migration') : t('router.development')">
    <template #header>
      <ElSpace class="flex items-center justify-right w-full" size="large">
        <QueryInput @input="searchByKeyWord" />

        <BaseButton @click="getList" :disabled="loading">
          <template #icon>
            <Refresh />
          </template>
        </BaseButton>

        <BaseButton type="primary" @click="gotoJobEditor" :icon="Plus">
          {{ t('common.add') }}
        </BaseButton>

        <BaseButton
          :loading="delLoading"
          @click="delData(null)"
          :disabled="!batchSelected"
          :type="batchSelected ? 'danger' : 'default'"
          :icon="Delete"
        >
          {{ t('common.del') }}
        </BaseButton>
      </ElSpace>
    </template>

    <Table
      v-model:pageSize="pageSize"
      v-model:currentPage="currentPage"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      :pagination="{ total: total }"
      :defaultSort="defaultSort"
      @register="tableRegister"
      @selected:change="handleSelectedChanged"
      @sort:change="handleSortChange"
    />
  </ContentWrap>
</template>

<style scoped lang="less"></style>
