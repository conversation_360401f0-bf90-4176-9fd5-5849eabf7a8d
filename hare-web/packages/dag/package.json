{"name": "@hare/dag", "private": true, "version": "0.0.0", "type": "module", "main": "src/index.ts", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.2", "@hare/utils": "workspace:*", "vue": "^3.5.20"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "@vue/tsconfig": "^0.7.0", "typescript": "~5.6.3", "vite": "^6.3.5", "vue-tsc": "^2.2.12"}}