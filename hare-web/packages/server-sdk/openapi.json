{"openapi": "3.1.0", "info": {"title": "Hare Manager Server API Spec", "description": "Hare Manager Server", "contact": {"url": "https://quezhanai.com", "email": "<EMAIL>"}, "version": "0.0.1-SNAPSHOT (v0.0.4-5-g81843ec-dirty - 20250821-1731) "}, "servers": [{"url": "http://localhost:9080", "description": "Generated server url"}], "tags": [{"name": "job", "description": "Job API"}, {"name": "project", "description": "项目管理接口"}, {"name": "plugin", "description": "Plugin API"}, {"name": "User", "description": "Management User's API"}, {"name": "category", "description": "Category management API"}, {"name": "file", "description": "File download/upload API"}, {"name": "Engine", "description": "Management Engine Instance's API"}, {"name": "connection", "description": "Data source connection API"}, {"name": "<PERSON><PERSON>", "description": "Mock API."}, {"name": "Health", "description": "Health API"}], "paths": {"/api/v1/plugin/upload": {"post": {"tags": ["plugin"], "operationId": "uploadPlugin", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessagePlugin"}}}}}}}, "/api/v1/job": {"get": {"tags": ["job"], "summary": "Find job of the model matched by filter from the data source.", "operationId": "<PERSON><PERSON><PERSON>", "parameters": [{"name": "filter", "in": "query", "description": "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"field\":{\"something\":true|false},\"sort\": [\"name desc\"],\"skip\":1,\"limit\":20}`).", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessagePageJob"}}}}}}, "post": {"tags": ["job"], "summary": "Create job.", "operationId": "createJob", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Job"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageJob"}}}}}}, "delete": {"tags": ["job"], "summary": "Delete jobs.", "operationId": "deleteJob", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchOptions"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageLong"}}}}}}}, "/api/v1/job/{id}/submit": {"post": {"tags": ["job"], "summary": "Submit job to execution", "operationId": "submitJob", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageBoolean"}}}}}}}, "/api/v1/file/": {"post": {"tags": ["file"], "operationId": "uploadFile", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageFileItem"}}}}}}}, "/api/v1/engine/assign": {"post": {"tags": ["Engine"], "summary": "assign available engine", "operationId": "assignEngine", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Where"}}}}, "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageEngine"}}}}}}}, "/api/v1/connection": {"get": {"tags": ["connection"], "summary": "Find datasource of the model matched by filter from the data source.", "operationId": "findConnection", "parameters": [{"name": "filter", "in": "query", "description": "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"field\":{\"something\":true|false},\"sort\": [\"name desc\"],\"skip\":1,\"limit\":20}`).", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessagePageConnection"}}}}}}, "post": {"tags": ["connection"], "summary": "Create datasource connection.", "operationId": "createConnection", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Connection"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageConnection"}}}}}}, "delete": {"tags": ["connection"], "summary": "Delete datasource connection.", "operationId": "deleteConnection", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchOptions"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageLong"}}}}}}}, "/api/v1/job/{id}": {"get": {"tags": ["job"], "summary": "Find job by id", "operationId": "findJobById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fields", "in": "query", "description": "Set to return a specified field, or set to exclude a specified field: e.g. {name: 1}, return the name field; {name: 0} exclude the name field", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageJob"}}}}}}, "delete": {"tags": ["job"], "summary": "Delete job by id", "operationId": "deleteJobById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageLong"}}}}}}, "patch": {"tags": ["job"], "summary": "Update job.", "operationId": "updateJob", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Job"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageJob"}}}}}}}, "/status": {"get": {"tags": ["Health"], "description": "管理端内部状态", "operationId": "status", "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageMapStringObject"}}}}}}}, "/health": {"get": {"tags": ["Health"], "description": "健康检查接口，返回200表示服务健康，否则服务有异常", "operationId": "index", "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}}, "/error": {"get": {"tags": ["Health"], "operationId": "error", "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageBoolean"}}}}}}}, "/api/v1/user": {"get": {"tags": ["User"], "description": "查询当前登录用户信息", "operationId": "getUserInfo", "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageUser"}}}}}}}, "/api/v1/user/{userId}/menus": {"get": {"tags": ["User"], "description": "查询当前登录用户菜单", "operationId": "getMenus", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageListMenu"}}}}}}}, "/api/v1/project": {"get": {"tags": ["project"], "summary": "Find project of the model matched by filter from the data source.", "operationId": "findProject", "parameters": [{"name": "filter", "in": "query", "description": "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"field\":{\"something\":true|false},\"sort\": [\"name desc\"],\"skip\":1,\"limit\":20}`).", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessagePageProject"}}}}}}}, "/api/v1/plugin/{type}/icon": {"get": {"tags": ["plugin"], "description": "Get plugin icon", "operationId": "findPluginIcon", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "plugin", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "Download plugin icon file"}}}}, "/api/v1/plugin/transformer": {"get": {"tags": ["plugin"], "summary": "Find transformer of the model matched by filter from the plugins.", "operationId": "findTransformer", "parameters": [{"name": "filter", "in": "query", "description": "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"field\":{\"something\":true|false},\"sort\": [\"name desc\"],\"skip\":1,\"limit\":20}`).", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessagePagePlugin"}}}}}}}, "/api/v1/plugin/transformer/first": {"get": {"tags": ["plugin"], "operationId": "findTransformerByNameAndVersion", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "version", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessagePlugin"}}}}}, "deprecated": true}}, "/api/v1/plugin/connector": {"get": {"tags": ["plugin"], "summary": "Find connector of the model matched by filter from the plugins.", "operationId": "findConnector", "parameters": [{"name": "filter", "in": "query", "description": "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"field\":{\"something\":true|false},\"sort\": [\"name desc\"],\"skip\":1,\"limit\":20}`).", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessagePagePlugin"}}}}}}}, "/api/v1/plugin/connector/first": {"get": {"tags": ["plugin"], "operationId": "findConnectorByNameAndVersion", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "version", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessagePlugin"}}}}}, "deprecated": true}}, "/api/v1/file/{id}": {"get": {"tags": ["file"], "operationId": "downloadFile", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/file/cleanup": {"get": {"tags": ["file"], "operationId": "cleanWaitingDeleteFiles", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}}, "/api/v1/connection/{id}": {"get": {"tags": ["connection"], "summary": "Find datasource connection by id", "operationId": "findConnectionById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageConnection"}}}}}}, "delete": {"tags": ["connection"], "summary": "Delete datasource connection.", "operationId": "deleteConnection_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageLong"}}}}}}}, "/api/v1/connection/{id}/tables": {"get": {"tags": ["connection"], "summary": "Find table by connection id", "operationId": "findTableByConnectionId", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "filter", "in": "query", "description": "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"field\":{\"something\":true|false},\"sort\": [\"name desc\"],\"skip\":1,\"limit\":20}`).", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessagePageSchema"}}}}}}}, "/api/v1/connection/copy/{id}": {"get": {"tags": ["connection"], "summary": "Copy datasource connection by id", "operationId": "copyConnectionById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageConnection"}}}}}}}, "/api/v1/category/tree": {"get": {"tags": ["category"], "operationId": "getCategory", "parameters": [{"name": "pid", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageListTreeNodeData"}}}}}}}, "/api/v1/workplace/{type}": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "workplace", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "put": {"tags": ["<PERSON><PERSON>"], "operationId": "workplace_2", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "post": {"tags": ["<PERSON><PERSON>"], "operationId": "workplace_1", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "delete": {"tags": ["<PERSON><PERSON>"], "operationId": "workplace_3", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "options": {"tags": ["<PERSON><PERSON>"], "operationId": "workplace_6", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "head": {"tags": ["<PERSON><PERSON>"], "operationId": "workplace_5", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "patch": {"tags": ["<PERSON><PERSON>"], "operationId": "workplace_4", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}}, "/api/v1/analysis/{type}": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "analysis", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "put": {"tags": ["<PERSON><PERSON>"], "operationId": "analysis_2", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "post": {"tags": ["<PERSON><PERSON>"], "operationId": "analysis_1", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "delete": {"tags": ["<PERSON><PERSON>"], "operationId": "analysis_3", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "options": {"tags": ["<PERSON><PERSON>"], "operationId": "analysis_6", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "head": {"tags": ["<PERSON><PERSON>"], "operationId": "analysis_5", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}, "patch": {"tags": ["<PERSON><PERSON>"], "operationId": "analysis_4", "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "successful operation", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMessageObject"}}}}}}}}, "components": {"schemas": {"StepItem": {"type": "object", "properties": {"code": {"type": "string"}, "level": {"type": "string", "enum": ["error", "warn", "info"]}, "msg": {"type": "string"}, "skip": {"type": "boolean"}, "result": {"type": "boolean"}, "progress": {"type": "integer", "format": "int32"}, "time": {"type": "integer", "format": "int64"}}}, "TestConnectionResult": {"type": "object", "description": "Test Connection Result", "properties": {"checkItems": {"type": "array", "items": {"$ref": "#/components/schemas/StepItem"}}, "success": {"type": "boolean"}, "metadata": {"type": "object", "additionalProperties": true}}}, "DependVersion": {"type": "object", "properties": {"mainVersion": {"type": "string"}, "maxVersion": {"type": "string"}}}, "FileItem": {"type": "object", "properties": {"id": {"type": "string", "description": "Primary key", "maxLength": 24, "minLength": 24}, "customerId": {"type": "string"}, "projectId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createBy": {"type": "string"}, "updateBy": {"type": "string"}, "createUser": {"type": "string"}, "filename": {"type": "string"}, "length": {"type": "integer", "format": "int64"}, "uploadDate": {"type": "string", "format": "date-time"}, "metadata": {"type": "object", "additionalProperties": {}}, "checksum": {"type": "integer", "format": "int64"}, "contentType": {"type": "string"}}}, "Plugin": {"type": "object", "properties": {"id": {"type": "string", "description": "Primary key", "maxLength": 24, "minLength": 24}, "customerId": {"type": "string"}, "projectId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createBy": {"type": "string"}, "updateBy": {"type": "string"}, "createUser": {"type": "string"}, "name": {"type": "string"}, "version": {"type": "string"}, "latest": {"type": "boolean"}, "developer": {"type": "string"}, "email": {"type": "string"}, "description": {"type": "string"}, "datasourceType": {"type": "string"}, "pluginType": {"type": "string", "enum": ["connector", "transformer"]}, "iconFile": {"$ref": "#/components/schemas/FileItem"}, "pluginFile": {"$ref": "#/components/schemas/FileItem"}, "connectorFormConfig": {"type": "object", "additionalProperties": {}}, "connectorFormJsonSchema": {"type": "object", "additionalProperties": {}}, "parameterFormConfig": {"type": "object", "additionalProperties": {}}, "parameterFormJsonSchema": {"type": "object", "additionalProperties": {}}, "depend": {"$ref": "#/components/schemas/DependVersion"}, "metadata": {"type": "object", "additionalProperties": {}}, "tags": {"type": "object", "additionalProperties": {"type": "string"}}, "scopeType": {"type": "string", "enum": ["PUBLIC", "PRIVATE"]}, "pluginKey": {"$ref": "#/components/schemas/PluginKey"}}}, "PluginKey": {"type": "object", "properties": {"name": {"type": "string"}, "version": {"type": "string"}, "scopeType": {"type": "string", "enum": ["PUBLIC", "PRIVATE"]}, "pluginType": {"type": "string", "enum": ["connector", "transformer"]}, "projectId": {"type": "string"}}}, "ResponseMessagePlugin": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/Plugin"}, "errorStack": {"type": "string"}}}, "Dag": {"type": "object", "properties": {"nodes": {"type": "array", "items": {"$ref": "#/components/schemas/Node"}}, "edges": {"type": "array", "items": {"$ref": "#/components/schemas/Edge"}}}}, "Edge": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "pluginKey": {"$ref": "#/components/schemas/PluginKey"}, "params": {"type": "object", "additionalProperties": {}}, "label": {"type": "string"}, "src": {"type": "string"}, "dst": {"type": "string"}}}, "Job": {"type": "object", "properties": {"id": {"type": "string", "description": "Primary key", "maxLength": 24, "minLength": 24}, "customerId": {"type": "string"}, "projectId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createBy": {"type": "string"}, "updateBy": {"type": "string"}, "createUser": {"type": "string"}, "name": {"type": "string"}, "dag": {"$ref": "#/components/schemas/Dag"}, "state": {"type": "string", "enum": ["EDITING", "SCHEDULE_FAILED", "SCHEDULE_SUCCESS", "WAITING_START", "WAITING_NEXT_RUN", "RUNNING", "STOPPING", "COMPLETE", "STOPPED", "ERROR", "RESETTING", "RESET_FAILED", "DELETED"]}, "type": {"type": "string", "enum": ["MIGRATION", "DEVELOPMENT"]}, "migrationType": {"type": "string", "enum": ["FULL_MIGRATION", "CDC_MIGRATION", "FULL_AND_CDC"]}, "setting": {"$ref": "#/components/schemas/JobSetting"}, "lastRunTime": {"type": "string", "format": "date-time"}, "graphJson": {}}, "required": ["dag", "migrationType", "name", "setting", "type"]}, "JobSetting": {"type": "object", "properties": {"dryRun": {"type": "boolean"}}}, "Node": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "pluginKey": {"$ref": "#/components/schemas/PluginKey"}, "params": {"type": "object", "additionalProperties": {}}, "type": {"type": "string"}}}, "ResponseMessageJob": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/Job"}, "errorStack": {"type": "string"}}}, "ResponseMessageBoolean": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "boolean"}, "errorStack": {"type": "string"}}}, "ResponseMessageFileItem": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/FileItem"}, "errorStack": {"type": "string"}}}, "Where": {"type": "object", "additionalProperties": {}, "properties": {"empty": {"type": "boolean"}}}, "Engine": {"type": "object", "properties": {"id": {"type": "string", "description": "Primary key", "maxLength": 24, "minLength": 24}, "customerId": {"type": "string"}, "projectId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createBy": {"type": "string"}, "updateBy": {"type": "string"}, "createUser": {"type": "string"}, "engineId": {"type": "string"}, "labels": {"type": "object", "additionalProperties": {"type": "string"}}, "stringLabels": {"type": "array", "items": {"type": "string"}}}}, "ResponseMessageEngine": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/Engine"}, "errorStack": {"type": "string"}}}, "Connection": {"type": "object", "properties": {"id": {"type": "string", "description": "Primary key", "maxLength": 24, "minLength": 24}, "customerId": {"type": "string"}, "projectId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createBy": {"type": "string"}, "updateBy": {"type": "string"}, "createUser": {"type": "string"}, "name": {"type": "string"}, "connectionType": {"type": "string", "enum": ["SOURCE_AND_TARGET", "SOURCE", "TARGET"]}, "status": {"type": "string", "enum": ["NOT_DETECTED", "INVALID", "READY", "TESTING", "DELETED"]}, "datasourceType": {"type": "string"}, "connectorName": {"type": "string"}, "connectorVersion": {"type": "string"}, "config": {"type": "string"}, "configPlain": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": {}}, "tags": {"type": "object", "additionalProperties": {"type": "string"}}, "lastTestConnectionTime": {"type": "string", "format": "date-time"}, "testConnectionResult": {"$ref": "#/components/schemas/TestConnectionResult"}, "totalTables": {"type": "integer", "format": "int64"}}, "required": ["config", "connectionType", "connectorName", "connectorVersion", "datasourceType", "name"]}, "ResponseMessageConnection": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/Connection"}, "errorStack": {"type": "string"}}}, "ResponseMessageMapStringObject": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "additionalProperties": {}}, "errorStack": {"type": "string"}}}, "ResponseMessageObject": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {}, "errorStack": {"type": "string"}}}, "ResponseMessageUser": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/User"}, "errorStack": {"type": "string"}}}, "User": {"type": "object", "properties": {"id": {"type": "string", "description": "Primary key", "maxLength": 24, "minLength": 24}, "customerId": {"type": "string"}, "projectId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createBy": {"type": "string"}, "updateBy": {"type": "string"}, "createUser": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "photo": {"type": "string"}, "emailVerified": {"type": "boolean"}, "status": {"type": "string", "enum": ["ACTIVATED", "LOCKED", "WAITING_DELETED", "DELETED"]}, "roles": {"type": "array", "items": {"type": "string"}}}}, "Menu": {"type": "object", "properties": {"id": {"type": "string", "description": "Primary key", "maxLength": 24, "minLength": 24}, "customerId": {"type": "string"}, "projectId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createBy": {"type": "string"}, "updateBy": {"type": "string"}, "createUser": {"type": "string"}, "name": {"type": "string"}, "path": {"type": "string"}, "component": {"type": "string"}, "redirect": {"type": "string"}, "meta": {"type": "object", "additionalProperties": {}}, "status": {"type": "string", "enum": ["ENABLE", "DISABLE"]}, "permissions": {"type": "array", "items": {"type": "string"}}}}, "ResponseMessageListMenu": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Menu"}}, "errorStack": {"type": "string"}}}, "PageProject": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}}}}, "Project": {"type": "object", "properties": {"id": {"type": "string", "description": "Primary key", "maxLength": 24, "minLength": 24}, "customerId": {"type": "string"}, "projectId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createBy": {"type": "string"}, "updateBy": {"type": "string"}, "createUser": {"type": "string"}, "name": {"type": "string"}}}, "ResponseMessagePageProject": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageProject"}, "errorStack": {"type": "string"}}}, "PagePlugin": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Plugin"}}}}, "ResponseMessagePagePlugin": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PagePlugin"}, "errorStack": {"type": "string"}}}, "PageJob": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Job"}}}}, "ResponseMessagePageJob": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageJob"}, "errorStack": {"type": "string"}}}, "PageConnection": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Connection"}}}}, "ResponseMessagePageConnection": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageConnection"}, "errorStack": {"type": "string"}}}, "Database": {"type": "object", "properties": {"name": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": {}}, "defaultCharset": {"type": "string"}, "version": {"type": "string"}, "totalTables": {"type": "integer", "format": "int64"}, "partitionTables": {"type": "integer", "format": "int64"}, "totalIndexes": {"type": "integer", "format": "int64"}, "totalFunctions": {"type": "integer", "format": "int64"}, "totalProcedure": {"type": "integer", "format": "int64"}, "totalTriggers": {"type": "integer", "format": "int64"}}}, "Field": {"type": "object", "properties": {"name": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": {}}, "position": {"type": "integer", "format": "int32"}, "defaultValue": {}, "nullable": {"type": "boolean"}, "dataType": {"type": "string"}, "maxLength": {"type": "integer", "format": "int64"}, "numberPrecision": {"type": "integer", "format": "int64"}, "numericScale": {"type": "integer", "format": "int64"}, "datetimePrecision": {"type": "integer", "format": "int32"}, "charsetName": {"type": "string"}, "columnType": {"type": "string"}, "comment": {"type": "string"}, "primaryKey": {"type": "boolean"}, "unique": {"type": "boolean"}, "autoIncrement": {"type": "boolean"}}}, "Index": {"type": "object", "properties": {"name": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": {}}, "type": {"type": "string"}, "indexFields": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/IndexField"}}, "unique": {"type": "boolean"}}}, "IndexField": {"type": "object", "properties": {"primary": {"type": "boolean"}, "name": {"type": "string"}, "sort": {"type": "string", "enum": ["ASC", "DESC"]}}}, "PageSchema": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Schema"}}}}, "Partition": {"type": "object", "properties": {"type": {"type": "string", "enum": ["HASH", "RANGE", "LIST", "INHERIT"]}}}, "ResponseMessagePageSchema": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageSchema"}, "errorStack": {"type": "string"}}}, "Schema": {"type": "object", "properties": {"id": {"type": "string", "description": "Primary key", "maxLength": 24, "minLength": 24}, "customerId": {"type": "string"}, "projectId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createBy": {"type": "string"}, "updateBy": {"type": "string"}, "createUser": {"type": "string"}, "name": {"type": "string"}, "version": {"type": "integer", "format": "int64"}, "connectionId": {"type": "string", "description": "MongoDB ObjectId", "maxLength": 24, "minLength": 24}, "schemaType": {"type": "string", "enum": ["DATABASE", "TABLE", "CONSTRAINT", "INDEX", "PROCEDURE", "TRIGGER", "FUNCTION", "SEQUENCE"]}, "superId": {"type": "string", "description": "MongoDB ObjectId", "maxLength": 24, "minLength": 24}, "database": {"$ref": "#/components/schemas/Database"}, "table": {"$ref": "#/components/schemas/Table"}, "sequence": {"$ref": "#/components/schemas/Sequence"}}}, "Sequence": {"type": "object", "properties": {"name": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": {}}, "currentValue": {"type": "integer", "format": "int64"}}}, "Table": {"type": "object", "properties": {"name": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": {}}, "type": {"type": "string", "enum": ["BASIC_TABLE", "VIEW", "SYSTEM_VIEW"]}, "version": {"type": "integer", "format": "int32"}, "schema": {"type": "string"}, "defaultCharsetName": {"type": "string"}, "comment": {"type": "string"}, "fields": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Field"}}, "indexes": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Index"}}, "primaryKeys": {"type": "array", "items": {"type": "string"}}, "partition": {"$ref": "#/components/schemas/Partition"}, "query": {"type": "string"}}}, "ResponseMessageListTreeNodeData": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TreeNodeData"}}, "errorStack": {"type": "string"}}}, "TreeNodeData": {"type": "object", "properties": {"id": {"type": "string"}, "label": {"type": "string"}, "icon": {"type": "string"}, "disable": {"type": "boolean"}, "leaf": {"type": "boolean"}, "attrs": {"type": "object", "additionalProperties": {}}}}, "BatchOptions": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}}}, "ResponseMessageLong": {"type": "object", "properties": {"reqId": {"type": "string"}, "ts": {"type": "integer", "format": "int64"}, "code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "integer", "format": "int64"}, "errorStack": {"type": "string"}}}}, "securitySchemes": {"basicScheme": {"type": "http", "scheme": "basic"}}}}