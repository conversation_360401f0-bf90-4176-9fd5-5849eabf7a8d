{"name": "@hare/server-sdk", "version": "1.0.0", "description": "Hare Server SDK", "main": "src/index.ts", "author": "<PERSON>", "license": "MIT", "dependencies": {"@hare/utils": "workspace:*", "axios": "^1.11.0", "events": "^3.3.0", "websocket-ts": "^2.2.1"}, "scripts": {"gen": "pnpm run gen:openapi && pnpm run gen:generator && pnpm run build && pnpm run test", "gen:openapi": "curl -H 'Authorization: Basic YWRtaW46YWRtaW4=' -o openapi.json http://localhost:9080/openapi.json", "gen:generator": "openapi-generator-cli generate -i ./openapi.json -g typescript-axios -o ./src -t ./template", "build": "tsc && pnpm run test", "test": "vitest run"}, "devDependencies": {"@openapitools/openapi-generator-cli": "^2.23.1", "@types/events": "^3.0.3", "msw": "^2.10.5", "typescript": "^5.9.2", "vitest": "^2.1.9"}}