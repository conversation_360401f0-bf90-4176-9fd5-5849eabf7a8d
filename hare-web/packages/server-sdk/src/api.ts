/* tslint:disable */
/* eslint-disable */
/**
 * Hare Manager Server API Spec
 * Hare Manager Server
 *
 * The version of the OpenAPI document: 0.0.1-SNAPSHOT (v0.0.4-5-g81843ec-dirty - 20250821-1731) 
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * 
 * @export
 * @interface BatchOptions
 */
export interface BatchOptions {
    /**
     * 
     * @type {Array<string>}
     * @memberof BatchOptions
     */
    'ids'?: Array<string>;
}
/**
 * 
 * @export
 * @interface Connection
 */
export interface Connection {
    /**
     * Primary key
     * @type {string}
     * @memberof Connection
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'customerId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'projectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'createBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'updateBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'createUser'?: string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'connectionType': ConnectionConnectionTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'status'?: ConnectionStatusEnum;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'datasourceType': string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'connectorName': string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'connectorVersion': string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'config': string;
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'configPlain'?: string;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Connection
     */
    'metadata'?: { [key: string]: any; };
    /**
     * 
     * @type {{ [key: string]: string; }}
     * @memberof Connection
     */
    'tags'?: { [key: string]: string; };
    /**
     * 
     * @type {string}
     * @memberof Connection
     */
    'lastTestConnectionTime'?: string;
    /**
     * 
     * @type {TestConnectionResult}
     * @memberof Connection
     */
    'testConnectionResult'?: TestConnectionResult;
    /**
     * 
     * @type {number}
     * @memberof Connection
     */
    'totalTables'?: number;
}

export const ConnectionConnectionTypeEnum = {
    SourceAndTarget: 'SOURCE_AND_TARGET',
    Source: 'SOURCE',
    Target: 'TARGET'
} as const;

export type ConnectionConnectionTypeEnum = typeof ConnectionConnectionTypeEnum[keyof typeof ConnectionConnectionTypeEnum];
export const ConnectionStatusEnum = {
    NotDetected: 'NOT_DETECTED',
    Invalid: 'INVALID',
    Ready: 'READY',
    Testing: 'TESTING',
    Deleted: 'DELETED'
} as const;

export type ConnectionStatusEnum = typeof ConnectionStatusEnum[keyof typeof ConnectionStatusEnum];

/**
 * 
 * @export
 * @interface Dag
 */
export interface Dag {
    /**
     * 
     * @type {Array<Node>}
     * @memberof Dag
     */
    'nodes'?: Array<Node>;
    /**
     * 
     * @type {Array<Edge>}
     * @memberof Dag
     */
    'edges'?: Array<Edge>;
}
/**
 * 
 * @export
 * @interface Database
 */
export interface Database {
    /**
     * 
     * @type {string}
     * @memberof Database
     */
    'name'?: string;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Database
     */
    'metadata'?: { [key: string]: any; };
    /**
     * 
     * @type {string}
     * @memberof Database
     */
    'defaultCharset'?: string;
    /**
     * 
     * @type {string}
     * @memberof Database
     */
    'version'?: string;
    /**
     * 
     * @type {number}
     * @memberof Database
     */
    'totalTables'?: number;
    /**
     * 
     * @type {number}
     * @memberof Database
     */
    'partitionTables'?: number;
    /**
     * 
     * @type {number}
     * @memberof Database
     */
    'totalIndexes'?: number;
    /**
     * 
     * @type {number}
     * @memberof Database
     */
    'totalFunctions'?: number;
    /**
     * 
     * @type {number}
     * @memberof Database
     */
    'totalProcedure'?: number;
    /**
     * 
     * @type {number}
     * @memberof Database
     */
    'totalTriggers'?: number;
}
/**
 * 
 * @export
 * @interface DependVersion
 */
export interface DependVersion {
    /**
     * 
     * @type {string}
     * @memberof DependVersion
     */
    'mainVersion'?: string;
    /**
     * 
     * @type {string}
     * @memberof DependVersion
     */
    'maxVersion'?: string;
}
/**
 * 
 * @export
 * @interface Edge
 */
export interface Edge {
    /**
     * 
     * @type {string}
     * @memberof Edge
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Edge
     */
    'name'?: string;
    /**
     * 
     * @type {PluginKey}
     * @memberof Edge
     */
    'pluginKey'?: PluginKey;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Edge
     */
    'params'?: { [key: string]: any; };
    /**
     * 
     * @type {string}
     * @memberof Edge
     */
    'label'?: string;
    /**
     * 
     * @type {string}
     * @memberof Edge
     */
    'src'?: string;
    /**
     * 
     * @type {string}
     * @memberof Edge
     */
    'dst'?: string;
}
/**
 * 
 * @export
 * @interface Engine
 */
export interface Engine {
    /**
     * Primary key
     * @type {string}
     * @memberof Engine
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Engine
     */
    'customerId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Engine
     */
    'projectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Engine
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Engine
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Engine
     */
    'createBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Engine
     */
    'updateBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Engine
     */
    'createUser'?: string;
    /**
     * 
     * @type {string}
     * @memberof Engine
     */
    'engineId'?: string;
    /**
     * 
     * @type {{ [key: string]: string; }}
     * @memberof Engine
     */
    'labels'?: { [key: string]: string; };
    /**
     * 
     * @type {Array<string>}
     * @memberof Engine
     */
    'stringLabels'?: Array<string>;
}
/**
 * 
 * @export
 * @interface Field
 */
export interface Field {
    /**
     * 
     * @type {string}
     * @memberof Field
     */
    'name'?: string;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Field
     */
    'metadata'?: { [key: string]: any; };
    /**
     * 
     * @type {number}
     * @memberof Field
     */
    'position'?: number;
    /**
     * 
     * @type {any}
     * @memberof Field
     */
    'defaultValue'?: any;
    /**
     * 
     * @type {boolean}
     * @memberof Field
     */
    'nullable'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof Field
     */
    'dataType'?: string;
    /**
     * 
     * @type {number}
     * @memberof Field
     */
    'maxLength'?: number;
    /**
     * 
     * @type {number}
     * @memberof Field
     */
    'numberPrecision'?: number;
    /**
     * 
     * @type {number}
     * @memberof Field
     */
    'numericScale'?: number;
    /**
     * 
     * @type {number}
     * @memberof Field
     */
    'datetimePrecision'?: number;
    /**
     * 
     * @type {string}
     * @memberof Field
     */
    'charsetName'?: string;
    /**
     * 
     * @type {string}
     * @memberof Field
     */
    'columnType'?: string;
    /**
     * 
     * @type {string}
     * @memberof Field
     */
    'comment'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Field
     */
    'primaryKey'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof Field
     */
    'unique'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof Field
     */
    'autoIncrement'?: boolean;
}
/**
 * 
 * @export
 * @interface FileItem
 */
export interface FileItem {
    /**
     * Primary key
     * @type {string}
     * @memberof FileItem
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof FileItem
     */
    'customerId'?: string;
    /**
     * 
     * @type {string}
     * @memberof FileItem
     */
    'projectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof FileItem
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof FileItem
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof FileItem
     */
    'createBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof FileItem
     */
    'updateBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof FileItem
     */
    'createUser'?: string;
    /**
     * 
     * @type {string}
     * @memberof FileItem
     */
    'filename'?: string;
    /**
     * 
     * @type {number}
     * @memberof FileItem
     */
    'length'?: number;
    /**
     * 
     * @type {string}
     * @memberof FileItem
     */
    'uploadDate'?: string;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof FileItem
     */
    'metadata'?: { [key: string]: any; };
    /**
     * 
     * @type {number}
     * @memberof FileItem
     */
    'checksum'?: number;
    /**
     * 
     * @type {string}
     * @memberof FileItem
     */
    'contentType'?: string;
}
/**
 * 
 * @export
 * @interface Index
 */
export interface Index {
    /**
     * 
     * @type {string}
     * @memberof Index
     */
    'name'?: string;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Index
     */
    'metadata'?: { [key: string]: any; };
    /**
     * 
     * @type {string}
     * @memberof Index
     */
    'type'?: string;
    /**
     * 
     * @type {{ [key: string]: IndexField; }}
     * @memberof Index
     */
    'indexFields'?: { [key: string]: IndexField; };
    /**
     * 
     * @type {boolean}
     * @memberof Index
     */
    'unique'?: boolean;
}
/**
 * 
 * @export
 * @interface IndexField
 */
export interface IndexField {
    /**
     * 
     * @type {boolean}
     * @memberof IndexField
     */
    'primary'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof IndexField
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof IndexField
     */
    'sort'?: IndexFieldSortEnum;
}

export const IndexFieldSortEnum = {
    Asc: 'ASC',
    Desc: 'DESC'
} as const;

export type IndexFieldSortEnum = typeof IndexFieldSortEnum[keyof typeof IndexFieldSortEnum];

/**
 * 
 * @export
 * @interface Job
 */
export interface Job {
    /**
     * Primary key
     * @type {string}
     * @memberof Job
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'customerId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'projectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'createBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'updateBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'createUser'?: string;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'name': string;
    /**
     * 
     * @type {Dag}
     * @memberof Job
     */
    'dag': Dag;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'state'?: JobStateEnum;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'type': JobTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'migrationType': JobMigrationTypeEnum;
    /**
     * 
     * @type {JobSetting}
     * @memberof Job
     */
    'setting': JobSetting;
    /**
     * 
     * @type {string}
     * @memberof Job
     */
    'lastRunTime'?: string;
    /**
     * 
     * @type {any}
     * @memberof Job
     */
    'graphJson'?: any;
}

export const JobStateEnum = {
    Editing: 'EDITING',
    ScheduleFailed: 'SCHEDULE_FAILED',
    ScheduleSuccess: 'SCHEDULE_SUCCESS',
    WaitingStart: 'WAITING_START',
    WaitingNextRun: 'WAITING_NEXT_RUN',
    Running: 'RUNNING',
    Stopping: 'STOPPING',
    Complete: 'COMPLETE',
    Stopped: 'STOPPED',
    Error: 'ERROR',
    Resetting: 'RESETTING',
    ResetFailed: 'RESET_FAILED',
    Deleted: 'DELETED'
} as const;

export type JobStateEnum = typeof JobStateEnum[keyof typeof JobStateEnum];
export const JobTypeEnum = {
    Migration: 'MIGRATION',
    Development: 'DEVELOPMENT'
} as const;

export type JobTypeEnum = typeof JobTypeEnum[keyof typeof JobTypeEnum];
export const JobMigrationTypeEnum = {
    FullMigration: 'FULL_MIGRATION',
    CdcMigration: 'CDC_MIGRATION',
    FullAndCdc: 'FULL_AND_CDC'
} as const;

export type JobMigrationTypeEnum = typeof JobMigrationTypeEnum[keyof typeof JobMigrationTypeEnum];

/**
 * 
 * @export
 * @interface JobSetting
 */
export interface JobSetting {
    /**
     * 
     * @type {boolean}
     * @memberof JobSetting
     */
    'dryRun'?: boolean;
}
/**
 * 
 * @export
 * @interface Menu
 */
export interface Menu {
    /**
     * Primary key
     * @type {string}
     * @memberof Menu
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'customerId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'projectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'createBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'updateBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'createUser'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'path'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'component'?: string;
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'redirect'?: string;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Menu
     */
    'meta'?: { [key: string]: any; };
    /**
     * 
     * @type {string}
     * @memberof Menu
     */
    'status'?: MenuStatusEnum;
    /**
     * 
     * @type {Array<string>}
     * @memberof Menu
     */
    'permissions'?: Array<string>;
}

export const MenuStatusEnum = {
    Enable: 'ENABLE',
    Disable: 'DISABLE'
} as const;

export type MenuStatusEnum = typeof MenuStatusEnum[keyof typeof MenuStatusEnum];

/**
 * 
 * @export
 * @interface Node
 */
export interface Node {
    /**
     * 
     * @type {string}
     * @memberof Node
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Node
     */
    'name'?: string;
    /**
     * 
     * @type {PluginKey}
     * @memberof Node
     */
    'pluginKey'?: PluginKey;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Node
     */
    'params'?: { [key: string]: any; };
    /**
     * 
     * @type {string}
     * @memberof Node
     */
    'type'?: string;
}
/**
 * 
 * @export
 * @interface PageConnection
 */
export interface PageConnection {
    /**
     * 
     * @type {number}
     * @memberof PageConnection
     */
    'total'?: number;
    /**
     * 
     * @type {Array<Connection>}
     * @memberof PageConnection
     */
    'items'?: Array<Connection>;
}
/**
 * 
 * @export
 * @interface PageJob
 */
export interface PageJob {
    /**
     * 
     * @type {number}
     * @memberof PageJob
     */
    'total'?: number;
    /**
     * 
     * @type {Array<Job>}
     * @memberof PageJob
     */
    'items'?: Array<Job>;
}
/**
 * 
 * @export
 * @interface PagePlugin
 */
export interface PagePlugin {
    /**
     * 
     * @type {number}
     * @memberof PagePlugin
     */
    'total'?: number;
    /**
     * 
     * @type {Array<Plugin>}
     * @memberof PagePlugin
     */
    'items'?: Array<Plugin>;
}
/**
 * 
 * @export
 * @interface PageProject
 */
export interface PageProject {
    /**
     * 
     * @type {number}
     * @memberof PageProject
     */
    'total'?: number;
    /**
     * 
     * @type {Array<Project>}
     * @memberof PageProject
     */
    'items'?: Array<Project>;
}
/**
 * 
 * @export
 * @interface PageSchema
 */
export interface PageSchema {
    /**
     * 
     * @type {number}
     * @memberof PageSchema
     */
    'total'?: number;
    /**
     * 
     * @type {Array<Schema>}
     * @memberof PageSchema
     */
    'items'?: Array<Schema>;
}
/**
 * 
 * @export
 * @interface Partition
 */
export interface Partition {
    /**
     * 
     * @type {string}
     * @memberof Partition
     */
    'type'?: PartitionTypeEnum;
}

export const PartitionTypeEnum = {
    Hash: 'HASH',
    Range: 'RANGE',
    List: 'LIST',
    Inherit: 'INHERIT'
} as const;

export type PartitionTypeEnum = typeof PartitionTypeEnum[keyof typeof PartitionTypeEnum];

/**
 * 
 * @export
 * @interface Plugin
 */
export interface Plugin {
    /**
     * Primary key
     * @type {string}
     * @memberof Plugin
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'customerId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'projectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'createBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'updateBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'createUser'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'version'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Plugin
     */
    'latest'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'developer'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'email'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'description'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'datasourceType'?: string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'pluginType'?: PluginPluginTypeEnum;
    /**
     * 
     * @type {FileItem}
     * @memberof Plugin
     */
    'iconFile'?: FileItem;
    /**
     * 
     * @type {FileItem}
     * @memberof Plugin
     */
    'pluginFile'?: FileItem;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Plugin
     */
    'connectorFormConfig'?: { [key: string]: any; };
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Plugin
     */
    'connectorFormJsonSchema'?: { [key: string]: any; };
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Plugin
     */
    'parameterFormConfig'?: { [key: string]: any; };
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Plugin
     */
    'parameterFormJsonSchema'?: { [key: string]: any; };
    /**
     * 
     * @type {DependVersion}
     * @memberof Plugin
     */
    'depend'?: DependVersion;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Plugin
     */
    'metadata'?: { [key: string]: any; };
    /**
     * 
     * @type {{ [key: string]: string; }}
     * @memberof Plugin
     */
    'tags'?: { [key: string]: string; };
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'scopeType'?: PluginScopeTypeEnum;
    /**
     * 
     * @type {PluginKey}
     * @memberof Plugin
     */
    'pluginKey'?: PluginKey;
}

export const PluginPluginTypeEnum = {
    Connector: 'connector',
    Transformer: 'transformer'
} as const;

export type PluginPluginTypeEnum = typeof PluginPluginTypeEnum[keyof typeof PluginPluginTypeEnum];
export const PluginScopeTypeEnum = {
    Public: 'PUBLIC',
    Private: 'PRIVATE'
} as const;

export type PluginScopeTypeEnum = typeof PluginScopeTypeEnum[keyof typeof PluginScopeTypeEnum];

/**
 * 
 * @export
 * @interface PluginKey
 */
export interface PluginKey {
    /**
     * 
     * @type {string}
     * @memberof PluginKey
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof PluginKey
     */
    'version'?: string;
    /**
     * 
     * @type {string}
     * @memberof PluginKey
     */
    'scopeType'?: PluginKeyScopeTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof PluginKey
     */
    'pluginType'?: PluginKeyPluginTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof PluginKey
     */
    'projectId'?: string;
}

export const PluginKeyScopeTypeEnum = {
    Public: 'PUBLIC',
    Private: 'PRIVATE'
} as const;

export type PluginKeyScopeTypeEnum = typeof PluginKeyScopeTypeEnum[keyof typeof PluginKeyScopeTypeEnum];
export const PluginKeyPluginTypeEnum = {
    Connector: 'connector',
    Transformer: 'transformer'
} as const;

export type PluginKeyPluginTypeEnum = typeof PluginKeyPluginTypeEnum[keyof typeof PluginKeyPluginTypeEnum];

/**
 * 
 * @export
 * @interface Project
 */
export interface Project {
    /**
     * Primary key
     * @type {string}
     * @memberof Project
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'customerId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'projectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'createBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'updateBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'createUser'?: string;
    /**
     * 
     * @type {string}
     * @memberof Project
     */
    'name'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageBoolean
 */
export interface ResponseMessageBoolean {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageBoolean
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageBoolean
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageBoolean
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageBoolean
     */
    'message'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ResponseMessageBoolean
     */
    'data'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageBoolean
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageConnection
 */
export interface ResponseMessageConnection {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageConnection
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageConnection
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageConnection
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageConnection
     */
    'message'?: string;
    /**
     * 
     * @type {Connection}
     * @memberof ResponseMessageConnection
     */
    'data'?: Connection;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageConnection
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageEngine
 */
export interface ResponseMessageEngine {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageEngine
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageEngine
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageEngine
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageEngine
     */
    'message'?: string;
    /**
     * 
     * @type {Engine}
     * @memberof ResponseMessageEngine
     */
    'data'?: Engine;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageEngine
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageFileItem
 */
export interface ResponseMessageFileItem {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageFileItem
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageFileItem
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageFileItem
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageFileItem
     */
    'message'?: string;
    /**
     * 
     * @type {FileItem}
     * @memberof ResponseMessageFileItem
     */
    'data'?: FileItem;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageFileItem
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageJob
 */
export interface ResponseMessageJob {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageJob
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageJob
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageJob
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageJob
     */
    'message'?: string;
    /**
     * 
     * @type {Job}
     * @memberof ResponseMessageJob
     */
    'data'?: Job;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageJob
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageListMenu
 */
export interface ResponseMessageListMenu {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageListMenu
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageListMenu
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageListMenu
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageListMenu
     */
    'message'?: string;
    /**
     * 
     * @type {Array<Menu>}
     * @memberof ResponseMessageListMenu
     */
    'data'?: Array<Menu>;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageListMenu
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageListTreeNodeData
 */
export interface ResponseMessageListTreeNodeData {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageListTreeNodeData
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageListTreeNodeData
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageListTreeNodeData
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageListTreeNodeData
     */
    'message'?: string;
    /**
     * 
     * @type {Array<TreeNodeData>}
     * @memberof ResponseMessageListTreeNodeData
     */
    'data'?: Array<TreeNodeData>;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageListTreeNodeData
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageLong
 */
export interface ResponseMessageLong {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageLong
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageLong
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageLong
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageLong
     */
    'message'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageLong
     */
    'data'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageLong
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageMapStringObject
 */
export interface ResponseMessageMapStringObject {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageMapStringObject
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageMapStringObject
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageMapStringObject
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageMapStringObject
     */
    'message'?: string;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof ResponseMessageMapStringObject
     */
    'data'?: { [key: string]: any; };
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageMapStringObject
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageObject
 */
export interface ResponseMessageObject {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageObject
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageObject
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageObject
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageObject
     */
    'message'?: string;
    /**
     * 
     * @type {any}
     * @memberof ResponseMessageObject
     */
    'data'?: any;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageObject
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessagePageConnection
 */
export interface ResponseMessagePageConnection {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageConnection
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessagePageConnection
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageConnection
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageConnection
     */
    'message'?: string;
    /**
     * 
     * @type {PageConnection}
     * @memberof ResponseMessagePageConnection
     */
    'data'?: PageConnection;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageConnection
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessagePageJob
 */
export interface ResponseMessagePageJob {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageJob
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessagePageJob
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageJob
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageJob
     */
    'message'?: string;
    /**
     * 
     * @type {PageJob}
     * @memberof ResponseMessagePageJob
     */
    'data'?: PageJob;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageJob
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessagePagePlugin
 */
export interface ResponseMessagePagePlugin {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePagePlugin
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessagePagePlugin
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePagePlugin
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePagePlugin
     */
    'message'?: string;
    /**
     * 
     * @type {PagePlugin}
     * @memberof ResponseMessagePagePlugin
     */
    'data'?: PagePlugin;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePagePlugin
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessagePageProject
 */
export interface ResponseMessagePageProject {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageProject
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessagePageProject
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageProject
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageProject
     */
    'message'?: string;
    /**
     * 
     * @type {PageProject}
     * @memberof ResponseMessagePageProject
     */
    'data'?: PageProject;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageProject
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessagePageSchema
 */
export interface ResponseMessagePageSchema {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageSchema
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessagePageSchema
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageSchema
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageSchema
     */
    'message'?: string;
    /**
     * 
     * @type {PageSchema}
     * @memberof ResponseMessagePageSchema
     */
    'data'?: PageSchema;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePageSchema
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessagePlugin
 */
export interface ResponseMessagePlugin {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePlugin
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessagePlugin
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePlugin
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePlugin
     */
    'message'?: string;
    /**
     * 
     * @type {Plugin}
     * @memberof ResponseMessagePlugin
     */
    'data'?: Plugin;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessagePlugin
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface ResponseMessageUser
 */
export interface ResponseMessageUser {
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageUser
     */
    'reqId'?: string;
    /**
     * 
     * @type {number}
     * @memberof ResponseMessageUser
     */
    'ts'?: number;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageUser
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageUser
     */
    'message'?: string;
    /**
     * 
     * @type {User}
     * @memberof ResponseMessageUser
     */
    'data'?: User;
    /**
     * 
     * @type {string}
     * @memberof ResponseMessageUser
     */
    'errorStack'?: string;
}
/**
 * 
 * @export
 * @interface Schema
 */
export interface Schema {
    /**
     * Primary key
     * @type {string}
     * @memberof Schema
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof Schema
     */
    'customerId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Schema
     */
    'projectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Schema
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Schema
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof Schema
     */
    'createBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Schema
     */
    'updateBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof Schema
     */
    'createUser'?: string;
    /**
     * 
     * @type {string}
     * @memberof Schema
     */
    'name'?: string;
    /**
     * 
     * @type {number}
     * @memberof Schema
     */
    'version'?: number;
    /**
     * MongoDB ObjectId
     * @type {string}
     * @memberof Schema
     */
    'connectionId'?: string;
    /**
     * 
     * @type {string}
     * @memberof Schema
     */
    'schemaType'?: SchemaSchemaTypeEnum;
    /**
     * MongoDB ObjectId
     * @type {string}
     * @memberof Schema
     */
    'superId'?: string;
    /**
     * 
     * @type {Database}
     * @memberof Schema
     */
    'database'?: Database;
    /**
     * 
     * @type {Table}
     * @memberof Schema
     */
    'table'?: Table;
    /**
     * 
     * @type {Sequence}
     * @memberof Schema
     */
    'sequence'?: Sequence;
}

export const SchemaSchemaTypeEnum = {
    Database: 'DATABASE',
    Table: 'TABLE',
    Constraint: 'CONSTRAINT',
    Index: 'INDEX',
    Procedure: 'PROCEDURE',
    Trigger: 'TRIGGER',
    Function: 'FUNCTION',
    Sequence: 'SEQUENCE'
} as const;

export type SchemaSchemaTypeEnum = typeof SchemaSchemaTypeEnum[keyof typeof SchemaSchemaTypeEnum];

/**
 * 
 * @export
 * @interface Sequence
 */
export interface Sequence {
    /**
     * 
     * @type {string}
     * @memberof Sequence
     */
    'name'?: string;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Sequence
     */
    'metadata'?: { [key: string]: any; };
    /**
     * 
     * @type {number}
     * @memberof Sequence
     */
    'currentValue'?: number;
}
/**
 * 
 * @export
 * @interface StepItem
 */
export interface StepItem {
    /**
     * 
     * @type {string}
     * @memberof StepItem
     */
    'code'?: string;
    /**
     * 
     * @type {string}
     * @memberof StepItem
     */
    'level'?: StepItemLevelEnum;
    /**
     * 
     * @type {string}
     * @memberof StepItem
     */
    'msg'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof StepItem
     */
    'skip'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof StepItem
     */
    'result'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof StepItem
     */
    'progress'?: number;
    /**
     * 
     * @type {number}
     * @memberof StepItem
     */
    'time'?: number;
}

export const StepItemLevelEnum = {
    Error: 'error',
    Warn: 'warn',
    Info: 'info'
} as const;

export type StepItemLevelEnum = typeof StepItemLevelEnum[keyof typeof StepItemLevelEnum];

/**
 * 
 * @export
 * @interface Table
 */
export interface Table {
    /**
     * 
     * @type {string}
     * @memberof Table
     */
    'name'?: string;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof Table
     */
    'metadata'?: { [key: string]: any; };
    /**
     * 
     * @type {string}
     * @memberof Table
     */
    'type'?: TableTypeEnum;
    /**
     * 
     * @type {number}
     * @memberof Table
     */
    'version'?: number;
    /**
     * 
     * @type {string}
     * @memberof Table
     */
    'schema'?: string;
    /**
     * 
     * @type {string}
     * @memberof Table
     */
    'defaultCharsetName'?: string;
    /**
     * 
     * @type {string}
     * @memberof Table
     */
    'comment'?: string;
    /**
     * 
     * @type {{ [key: string]: Field; }}
     * @memberof Table
     */
    'fields'?: { [key: string]: Field; };
    /**
     * 
     * @type {{ [key: string]: Index; }}
     * @memberof Table
     */
    'indexes'?: { [key: string]: Index; };
    /**
     * 
     * @type {Array<string>}
     * @memberof Table
     */
    'primaryKeys'?: Array<string>;
    /**
     * 
     * @type {Partition}
     * @memberof Table
     */
    'partition'?: Partition;
    /**
     * 
     * @type {string}
     * @memberof Table
     */
    'query'?: string;
}

export const TableTypeEnum = {
    BasicTable: 'BASIC_TABLE',
    View: 'VIEW',
    SystemView: 'SYSTEM_VIEW'
} as const;

export type TableTypeEnum = typeof TableTypeEnum[keyof typeof TableTypeEnum];

/**
 * Test Connection Result
 * @export
 * @interface TestConnectionResult
 */
export interface TestConnectionResult {
    /**
     * 
     * @type {Array<StepItem>}
     * @memberof TestConnectionResult
     */
    'checkItems'?: Array<StepItem>;
    /**
     * 
     * @type {boolean}
     * @memberof TestConnectionResult
     */
    'success'?: boolean;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof TestConnectionResult
     */
    'metadata'?: { [key: string]: any; };
}
/**
 * 
 * @export
 * @interface TreeNodeData
 */
export interface TreeNodeData {
    /**
     * 
     * @type {string}
     * @memberof TreeNodeData
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof TreeNodeData
     */
    'label'?: string;
    /**
     * 
     * @type {string}
     * @memberof TreeNodeData
     */
    'icon'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof TreeNodeData
     */
    'disable'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof TreeNodeData
     */
    'leaf'?: boolean;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof TreeNodeData
     */
    'attrs'?: { [key: string]: any; };
}
/**
 * 
 * @export
 * @interface User
 */
export interface User {
    /**
     * Primary key
     * @type {string}
     * @memberof User
     */
    'id'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'customerId'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'projectId'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'createdAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'updatedAt'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'createBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'updateBy'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'createUser'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'username'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'email'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'phone'?: string;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'photo'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof User
     */
    'emailVerified'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof User
     */
    'status'?: UserStatusEnum;
    /**
     * 
     * @type {Array<string>}
     * @memberof User
     */
    'roles'?: Array<string>;
}

export const UserStatusEnum = {
    Activated: 'ACTIVATED',
    Locked: 'LOCKED',
    WaitingDeleted: 'WAITING_DELETED',
    Deleted: 'DELETED'
} as const;

export type UserStatusEnum = typeof UserStatusEnum[keyof typeof UserStatusEnum];

/**
 * 
 * @export
 * @interface Where
 */
export interface Where {
    [key: string]: any;

    /**
     * 
     * @type {boolean}
     * @memberof Where
     */
    'empty'?: boolean;
}

/**
 * CategoryApi - axios parameter creator
 * @export
 */
export const CategoryApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} pid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCategory: async (pid: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'pid' is not null or undefined
            assertParamExists('getCategory', 'pid', pid)
            const localVarPath = `/api/v1/category/tree`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (pid !== undefined) {
                localVarQueryParameter['pid'] = pid;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CategoryApi - functional programming interface
 * @export
 */
export const CategoryApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CategoryApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} pid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCategory(pid: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageListTreeNodeData>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCategory(pid, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CategoryApi.getCategory']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CategoryApi - factory interface
 * @export
 */
export const CategoryApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CategoryApiFp(configuration)
    return {
        /**
         * 
         * @param {string} pid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCategory(pid: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageListTreeNodeData> {
            return localVarFp.getCategory(pid, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CategoryApi - object-oriented interface
 * @export
 * @class CategoryApi
 * @extends {BaseAPI}
 */
export class CategoryApi extends BaseAPI {
    /**
     * 
     * @param {string} pid 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CategoryApi
     */
    public getCategory(pid: string, options?: RawAxiosRequestConfig) {
        return CategoryApiFp(this.configuration).getCategory(pid, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * ConnectionApi - axios parameter creator
 * @export
 */
export const ConnectionApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Copy datasource connection by id
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        copyConnectionById: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('copyConnectionById', 'id', id)
            const localVarPath = `/api/v1/connection/copy/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Create datasource connection.
         * @param {Connection} connection 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createConnection: async (connection: Connection, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'connection' is not null or undefined
            assertParamExists('createConnection', 'connection', connection)
            const localVarPath = `/api/v1/connection`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(connection, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete datasource connection.
         * @param {BatchOptions} batchOptions 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteConnection: async (batchOptions: BatchOptions, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'batchOptions' is not null or undefined
            assertParamExists('deleteConnection', 'batchOptions', batchOptions)
            const localVarPath = `/api/v1/connection`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(batchOptions, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete datasource connection.
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteConnection1: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteConnection1', 'id', id)
            const localVarPath = `/api/v1/connection/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Find datasource of the model matched by filter from the data source.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findConnection: async (filter?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1/connection`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Find datasource connection by id
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findConnectionById: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('findConnectionById', 'id', id)
            const localVarPath = `/api/v1/connection/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Find table by connection id
         * @param {string} id 
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findTableByConnectionId: async (id: string, filter?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('findTableByConnectionId', 'id', id)
            const localVarPath = `/api/v1/connection/{id}/tables`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ConnectionApi - functional programming interface
 * @export
 */
export const ConnectionApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ConnectionApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Copy datasource connection by id
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async copyConnectionById(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageConnection>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.copyConnectionById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConnectionApi.copyConnectionById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Create datasource connection.
         * @param {Connection} connection 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createConnection(connection: Connection, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageConnection>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createConnection(connection, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConnectionApi.createConnection']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Delete datasource connection.
         * @param {BatchOptions} batchOptions 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteConnection(batchOptions: BatchOptions, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageLong>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteConnection(batchOptions, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConnectionApi.deleteConnection']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Delete datasource connection.
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteConnection1(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageLong>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteConnection1(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConnectionApi.deleteConnection1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Find datasource of the model matched by filter from the data source.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findConnection(filter?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessagePageConnection>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findConnection(filter, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConnectionApi.findConnection']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Find datasource connection by id
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findConnectionById(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageConnection>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findConnectionById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConnectionApi.findConnectionById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Find table by connection id
         * @param {string} id 
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findTableByConnectionId(id: string, filter?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessagePageSchema>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findTableByConnectionId(id, filter, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ConnectionApi.findTableByConnectionId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ConnectionApi - factory interface
 * @export
 */
export const ConnectionApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ConnectionApiFp(configuration)
    return {
        /**
         * 
         * @summary Copy datasource connection by id
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        copyConnectionById(id: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageConnection> {
            return localVarFp.copyConnectionById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Create datasource connection.
         * @param {Connection} connection 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createConnection(connection: Connection, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageConnection> {
            return localVarFp.createConnection(connection, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete datasource connection.
         * @param {BatchOptions} batchOptions 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteConnection(batchOptions: BatchOptions, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageLong> {
            return localVarFp.deleteConnection(batchOptions, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete datasource connection.
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteConnection1(id: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageLong> {
            return localVarFp.deleteConnection1(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Find datasource of the model matched by filter from the data source.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findConnection(filter?: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessagePageConnection> {
            return localVarFp.findConnection(filter, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Find datasource connection by id
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findConnectionById(id: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageConnection> {
            return localVarFp.findConnectionById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Find table by connection id
         * @param {string} id 
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findTableByConnectionId(id: string, filter?: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessagePageSchema> {
            return localVarFp.findTableByConnectionId(id, filter, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ConnectionApi - object-oriented interface
 * @export
 * @class ConnectionApi
 * @extends {BaseAPI}
 */
export class ConnectionApi extends BaseAPI {
    /**
     * 
     * @summary Copy datasource connection by id
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConnectionApi
     */
    public copyConnectionById(id: string, options?: RawAxiosRequestConfig) {
        return ConnectionApiFp(this.configuration).copyConnectionById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Create datasource connection.
     * @param {Connection} connection 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConnectionApi
     */
    public createConnection(connection: Connection, options?: RawAxiosRequestConfig) {
        return ConnectionApiFp(this.configuration).createConnection(connection, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete datasource connection.
     * @param {BatchOptions} batchOptions 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConnectionApi
     */
    public deleteConnection(batchOptions: BatchOptions, options?: RawAxiosRequestConfig) {
        return ConnectionApiFp(this.configuration).deleteConnection(batchOptions, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete datasource connection.
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConnectionApi
     */
    public deleteConnection1(id: string, options?: RawAxiosRequestConfig) {
        return ConnectionApiFp(this.configuration).deleteConnection1(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Find datasource of the model matched by filter from the data source.
     * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConnectionApi
     */
    public findConnection(filter?: string, options?: RawAxiosRequestConfig) {
        return ConnectionApiFp(this.configuration).findConnection(filter, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Find datasource connection by id
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConnectionApi
     */
    public findConnectionById(id: string, options?: RawAxiosRequestConfig) {
        return ConnectionApiFp(this.configuration).findConnectionById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Find table by connection id
     * @param {string} id 
     * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConnectionApi
     */
    public findTableByConnectionId(id: string, filter?: string, options?: RawAxiosRequestConfig) {
        return ConnectionApiFp(this.configuration).findTableByConnectionId(id, filter, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * EngineApi - axios parameter creator
 * @export
 */
export const EngineApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary assign available engine
         * @param {Where} [where] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assignEngine: async (where?: Where, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1/engine/assign`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(where, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EngineApi - functional programming interface
 * @export
 */
export const EngineApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = EngineApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary assign available engine
         * @param {Where} [where] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assignEngine(where?: Where, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageEngine>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assignEngine(where, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EngineApi.assignEngine']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * EngineApi - factory interface
 * @export
 */
export const EngineApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = EngineApiFp(configuration)
    return {
        /**
         * 
         * @summary assign available engine
         * @param {Where} [where] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assignEngine(where?: Where, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageEngine> {
            return localVarFp.assignEngine(where, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EngineApi - object-oriented interface
 * @export
 * @class EngineApi
 * @extends {BaseAPI}
 */
export class EngineApi extends BaseAPI {
    /**
     * 
     * @summary assign available engine
     * @param {Where} [where] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EngineApi
     */
    public assignEngine(where?: Where, options?: RawAxiosRequestConfig) {
        return EngineApiFp(this.configuration).assignEngine(where, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * FileApi - axios parameter creator
 * @export
 */
export const FileApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cleanWaitingDeleteFiles: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1/file/cleanup`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        downloadFile: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('downloadFile', 'id', id)
            const localVarPath = `/api/v1/file/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadFile: async (file: File, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'file' is not null or undefined
            assertParamExists('uploadFile', 'file', file)
            const localVarPath = `/api/v1/file/`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * FileApi - functional programming interface
 * @export
 */
export const FileApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = FileApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async cleanWaitingDeleteFiles(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.cleanWaitingDeleteFiles(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['FileApi.cleanWaitingDeleteFiles']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async downloadFile(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.downloadFile(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['FileApi.downloadFile']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async uploadFile(file: File, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageFileItem>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.uploadFile(file, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['FileApi.uploadFile']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * FileApi - factory interface
 * @export
 */
export const FileApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = FileApiFp(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cleanWaitingDeleteFiles(options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.cleanWaitingDeleteFiles(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        downloadFile(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.downloadFile(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadFile(file: File, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageFileItem> {
            return localVarFp.uploadFile(file, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * FileApi - object-oriented interface
 * @export
 * @class FileApi
 * @extends {BaseAPI}
 */
export class FileApi extends BaseAPI {
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FileApi
     */
    public cleanWaitingDeleteFiles(options?: RawAxiosRequestConfig) {
        return FileApiFp(this.configuration).cleanWaitingDeleteFiles(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FileApi
     */
    public downloadFile(id: string, options?: RawAxiosRequestConfig) {
        return FileApiFp(this.configuration).downloadFile(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {File} file 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof FileApi
     */
    public uploadFile(file: File, options?: RawAxiosRequestConfig) {
        return FileApiFp(this.configuration).uploadFile(file, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * HealthApi - axios parameter creator
 * @export
 */
export const HealthApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        error: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/error`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 健康检查接口，返回200表示服务健康，否则服务有异常
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        index: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/health`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 管理端内部状态
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        status: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * HealthApi - functional programming interface
 * @export
 */
export const HealthApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = HealthApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async error(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageBoolean>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.error(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['HealthApi.error']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 健康检查接口，返回200表示服务健康，否则服务有异常
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async index(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.index(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['HealthApi.index']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 管理端内部状态
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async status(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageMapStringObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.status(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['HealthApi.status']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * HealthApi - factory interface
 * @export
 */
export const HealthApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = HealthApiFp(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        error(options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageBoolean> {
            return localVarFp.error(options).then((request) => request(axios, basePath));
        },
        /**
         * 健康检查接口，返回200表示服务健康，否则服务有异常
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        index(options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.index(options).then((request) => request(axios, basePath));
        },
        /**
         * 管理端内部状态
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        status(options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageMapStringObject> {
            return localVarFp.status(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * HealthApi - object-oriented interface
 * @export
 * @class HealthApi
 * @extends {BaseAPI}
 */
export class HealthApi extends BaseAPI {
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HealthApi
     */
    public error(options?: RawAxiosRequestConfig) {
        return HealthApiFp(this.configuration).error(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 健康检查接口，返回200表示服务健康，否则服务有异常
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HealthApi
     */
    public index(options?: RawAxiosRequestConfig) {
        return HealthApiFp(this.configuration).index(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 管理端内部状态
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HealthApi
     */
    public status(options?: RawAxiosRequestConfig) {
        return HealthApiFp(this.configuration).status(options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * JobApi - axios parameter creator
 * @export
 */
export const JobApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Create job.
         * @param {Job} job 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createJob: async (job: Job, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'job' is not null or undefined
            assertParamExists('createJob', 'job', job)
            const localVarPath = `/api/v1/job`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(job, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete jobs.
         * @param {BatchOptions} batchOptions 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteJob: async (batchOptions: BatchOptions, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'batchOptions' is not null or undefined
            assertParamExists('deleteJob', 'batchOptions', batchOptions)
            const localVarPath = `/api/v1/job`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(batchOptions, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete job by id
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteJobById: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteJobById', 'id', id)
            const localVarPath = `/api/v1/job/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Find job of the model matched by filter from the data source.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findJob: async (filter?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1/job`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Find job by id
         * @param {string} id 
         * @param {string} [fields] Set to return a specified field, or set to exclude a specified field: e.g. {name: 1}, return the name field; {name: 0} exclude the name field
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findJobById: async (id: string, fields?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('findJobById', 'id', id)
            const localVarPath = `/api/v1/job/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (fields !== undefined) {
                localVarQueryParameter['fields'] = fields;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Submit job to execution
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submitJob: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('submitJob', 'id', id)
            const localVarPath = `/api/v1/job/{id}/submit`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update job.
         * @param {string} id 
         * @param {Job} job 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateJob: async (id: string, job: Job, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateJob', 'id', id)
            // verify required parameter 'job' is not null or undefined
            assertParamExists('updateJob', 'job', job)
            const localVarPath = `/api/v1/job/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(job, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * JobApi - functional programming interface
 * @export
 */
export const JobApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = JobApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Create job.
         * @param {Job} job 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createJob(job: Job, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageJob>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createJob(job, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobApi.createJob']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Delete jobs.
         * @param {BatchOptions} batchOptions 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteJob(batchOptions: BatchOptions, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageLong>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteJob(batchOptions, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobApi.deleteJob']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Delete job by id
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteJobById(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageLong>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteJobById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobApi.deleteJobById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Find job of the model matched by filter from the data source.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findJob(filter?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessagePageJob>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findJob(filter, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobApi.findJob']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Find job by id
         * @param {string} id 
         * @param {string} [fields] Set to return a specified field, or set to exclude a specified field: e.g. {name: 1}, return the name field; {name: 0} exclude the name field
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findJobById(id: string, fields?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageJob>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findJobById(id, fields, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobApi.findJobById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Submit job to execution
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submitJob(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageBoolean>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submitJob(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobApi.submitJob']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Update job.
         * @param {string} id 
         * @param {Job} job 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateJob(id: string, job: Job, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageJob>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateJob(id, job, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['JobApi.updateJob']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * JobApi - factory interface
 * @export
 */
export const JobApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = JobApiFp(configuration)
    return {
        /**
         * 
         * @summary Create job.
         * @param {Job} job 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createJob(job: Job, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageJob> {
            return localVarFp.createJob(job, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete jobs.
         * @param {BatchOptions} batchOptions 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteJob(batchOptions: BatchOptions, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageLong> {
            return localVarFp.deleteJob(batchOptions, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete job by id
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteJobById(id: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageLong> {
            return localVarFp.deleteJobById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Find job of the model matched by filter from the data source.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findJob(filter?: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessagePageJob> {
            return localVarFp.findJob(filter, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Find job by id
         * @param {string} id 
         * @param {string} [fields] Set to return a specified field, or set to exclude a specified field: e.g. {name: 1}, return the name field; {name: 0} exclude the name field
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findJobById(id: string, fields?: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageJob> {
            return localVarFp.findJobById(id, fields, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Submit job to execution
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submitJob(id: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageBoolean> {
            return localVarFp.submitJob(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update job.
         * @param {string} id 
         * @param {Job} job 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateJob(id: string, job: Job, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageJob> {
            return localVarFp.updateJob(id, job, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * JobApi - object-oriented interface
 * @export
 * @class JobApi
 * @extends {BaseAPI}
 */
export class JobApi extends BaseAPI {
    /**
     * 
     * @summary Create job.
     * @param {Job} job 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobApi
     */
    public createJob(job: Job, options?: RawAxiosRequestConfig) {
        return JobApiFp(this.configuration).createJob(job, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete jobs.
     * @param {BatchOptions} batchOptions 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobApi
     */
    public deleteJob(batchOptions: BatchOptions, options?: RawAxiosRequestConfig) {
        return JobApiFp(this.configuration).deleteJob(batchOptions, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete job by id
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobApi
     */
    public deleteJobById(id: string, options?: RawAxiosRequestConfig) {
        return JobApiFp(this.configuration).deleteJobById(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Find job of the model matched by filter from the data source.
     * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobApi
     */
    public findJob(filter?: string, options?: RawAxiosRequestConfig) {
        return JobApiFp(this.configuration).findJob(filter, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Find job by id
     * @param {string} id 
     * @param {string} [fields] Set to return a specified field, or set to exclude a specified field: e.g. {name: 1}, return the name field; {name: 0} exclude the name field
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobApi
     */
    public findJobById(id: string, fields?: string, options?: RawAxiosRequestConfig) {
        return JobApiFp(this.configuration).findJobById(id, fields, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Submit job to execution
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobApi
     */
    public submitJob(id: string, options?: RawAxiosRequestConfig) {
        return JobApiFp(this.configuration).submitJob(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update job.
     * @param {string} id 
     * @param {Job} job 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JobApi
     */
    public updateJob(id: string, job: Job, options?: RawAxiosRequestConfig) {
        return JobApiFp(this.configuration).updateJob(id, job, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * MockApi - axios parameter creator
 * @export
 */
export const MockApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('analysis', 'type', type)
            const localVarPath = `/api/v1/analysis/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis1: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('analysis1', 'type', type)
            const localVarPath = `/api/v1/analysis/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis2: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('analysis2', 'type', type)
            const localVarPath = `/api/v1/analysis/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis3: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('analysis3', 'type', type)
            const localVarPath = `/api/v1/analysis/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis4: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('analysis4', 'type', type)
            const localVarPath = `/api/v1/analysis/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis5: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('analysis5', 'type', type)
            const localVarPath = `/api/v1/analysis/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'HEAD', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis6: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('analysis6', 'type', type)
            const localVarPath = `/api/v1/analysis/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'OPTIONS', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('workplace', 'type', type)
            const localVarPath = `/api/v1/workplace/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace1: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('workplace1', 'type', type)
            const localVarPath = `/api/v1/workplace/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace2: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('workplace2', 'type', type)
            const localVarPath = `/api/v1/workplace/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace3: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('workplace3', 'type', type)
            const localVarPath = `/api/v1/workplace/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace4: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('workplace4', 'type', type)
            const localVarPath = `/api/v1/workplace/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace5: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('workplace5', 'type', type)
            const localVarPath = `/api/v1/workplace/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'HEAD', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace6: async (type: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('workplace6', 'type', type)
            const localVarPath = `/api/v1/workplace/{type}`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'OPTIONS', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * MockApi - functional programming interface
 * @export
 */
export const MockApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = MockApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async analysis(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.analysis(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.analysis']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async analysis1(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.analysis1(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.analysis1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async analysis2(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.analysis2(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.analysis2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async analysis3(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.analysis3(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.analysis3']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async analysis4(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.analysis4(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.analysis4']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async analysis5(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.analysis5(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.analysis5']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async analysis6(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.analysis6(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.analysis6']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async workplace(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.workplace(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.workplace']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async workplace1(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.workplace1(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.workplace1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async workplace2(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.workplace2(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.workplace2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async workplace3(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.workplace3(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.workplace3']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async workplace4(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.workplace4(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.workplace4']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async workplace5(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.workplace5(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.workplace5']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async workplace6(type: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageObject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.workplace6(type, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MockApi.workplace6']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * MockApi - factory interface
 * @export
 */
export const MockApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = MockApiFp(configuration)
    return {
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.analysis(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis1(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.analysis1(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis2(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.analysis2(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis3(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.analysis3(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis4(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.analysis4(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis5(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.analysis5(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        analysis6(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.analysis6(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.workplace(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace1(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.workplace1(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace2(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.workplace2(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace3(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.workplace3(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace4(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.workplace4(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace5(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.workplace5(type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} type 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        workplace6(type: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageObject> {
            return localVarFp.workplace6(type, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * MockApi - object-oriented interface
 * @export
 * @class MockApi
 * @extends {BaseAPI}
 */
export class MockApi extends BaseAPI {
    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public analysis(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).analysis(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public analysis1(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).analysis1(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public analysis2(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).analysis2(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public analysis3(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).analysis3(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public analysis4(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).analysis4(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public analysis5(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).analysis5(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public analysis6(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).analysis6(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public workplace(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).workplace(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public workplace1(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).workplace1(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public workplace2(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).workplace2(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public workplace3(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).workplace3(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public workplace4(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).workplace4(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public workplace5(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).workplace5(type, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} type 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MockApi
     */
    public workplace6(type: string, options?: RawAxiosRequestConfig) {
        return MockApiFp(this.configuration).workplace6(type, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * PluginApi - axios parameter creator
 * @export
 */
export const PluginApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Find connector of the model matched by filter from the plugins.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findConnector: async (filter?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1/plugin/connector`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} name 
         * @param {string} version 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        findConnectorByNameAndVersion: async (name: string, version: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('findConnectorByNameAndVersion', 'name', name)
            // verify required parameter 'version' is not null or undefined
            assertParamExists('findConnectorByNameAndVersion', 'version', version)
            const localVarPath = `/api/v1/plugin/connector/first`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (version !== undefined) {
                localVarQueryParameter['version'] = version;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get plugin icon
         * @param {string} type 
         * @param {string} plugin 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findPluginIcon: async (type: string, plugin: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('findPluginIcon', 'type', type)
            // verify required parameter 'plugin' is not null or undefined
            assertParamExists('findPluginIcon', 'plugin', plugin)
            const localVarPath = `/api/v1/plugin/{type}/icon`
                .replace(`{${"type"}}`, encodeURIComponent(String(type)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (plugin !== undefined) {
                localVarQueryParameter['plugin'] = plugin;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Find transformer of the model matched by filter from the plugins.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findTransformer: async (filter?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1/plugin/transformer`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} name 
         * @param {string} version 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        findTransformerByNameAndVersion: async (name: string, version: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('findTransformerByNameAndVersion', 'name', name)
            // verify required parameter 'version' is not null or undefined
            assertParamExists('findTransformerByNameAndVersion', 'version', version)
            const localVarPath = `/api/v1/plugin/transformer/first`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }

            if (version !== undefined) {
                localVarQueryParameter['version'] = version;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadPlugin: async (file: File, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'file' is not null or undefined
            assertParamExists('uploadPlugin', 'file', file)
            const localVarPath = `/api/v1/plugin/upload`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PluginApi - functional programming interface
 * @export
 */
export const PluginApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = PluginApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Find connector of the model matched by filter from the plugins.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findConnector(filter?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessagePagePlugin>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findConnector(filter, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PluginApi.findConnector']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} name 
         * @param {string} version 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async findConnectorByNameAndVersion(name: string, version: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessagePlugin>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findConnectorByNameAndVersion(name, version, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PluginApi.findConnectorByNameAndVersion']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get plugin icon
         * @param {string} type 
         * @param {string} plugin 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findPluginIcon(type: string, plugin: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findPluginIcon(type, plugin, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PluginApi.findPluginIcon']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Find transformer of the model matched by filter from the plugins.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findTransformer(filter?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessagePagePlugin>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findTransformer(filter, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PluginApi.findTransformer']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} name 
         * @param {string} version 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async findTransformerByNameAndVersion(name: string, version: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessagePlugin>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findTransformerByNameAndVersion(name, version, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PluginApi.findTransformerByNameAndVersion']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async uploadPlugin(file: File, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessagePlugin>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.uploadPlugin(file, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PluginApi.uploadPlugin']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * PluginApi - factory interface
 * @export
 */
export const PluginApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = PluginApiFp(configuration)
    return {
        /**
         * 
         * @summary Find connector of the model matched by filter from the plugins.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findConnector(filter?: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessagePagePlugin> {
            return localVarFp.findConnector(filter, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} name 
         * @param {string} version 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        findConnectorByNameAndVersion(name: string, version: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessagePlugin> {
            return localVarFp.findConnectorByNameAndVersion(name, version, options).then((request) => request(axios, basePath));
        },
        /**
         * Get plugin icon
         * @param {string} type 
         * @param {string} plugin 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findPluginIcon(type: string, plugin: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.findPluginIcon(type, plugin, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Find transformer of the model matched by filter from the plugins.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findTransformer(filter?: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessagePagePlugin> {
            return localVarFp.findTransformer(filter, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} name 
         * @param {string} version 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        findTransformerByNameAndVersion(name: string, version: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessagePlugin> {
            return localVarFp.findTransformerByNameAndVersion(name, version, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadPlugin(file: File, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessagePlugin> {
            return localVarFp.uploadPlugin(file, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PluginApi - object-oriented interface
 * @export
 * @class PluginApi
 * @extends {BaseAPI}
 */
export class PluginApi extends BaseAPI {
    /**
     * 
     * @summary Find connector of the model matched by filter from the plugins.
     * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PluginApi
     */
    public findConnector(filter?: string, options?: RawAxiosRequestConfig) {
        return PluginApiFp(this.configuration).findConnector(filter, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} name 
     * @param {string} version 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof PluginApi
     */
    public findConnectorByNameAndVersion(name: string, version: string, options?: RawAxiosRequestConfig) {
        return PluginApiFp(this.configuration).findConnectorByNameAndVersion(name, version, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get plugin icon
     * @param {string} type 
     * @param {string} plugin 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PluginApi
     */
    public findPluginIcon(type: string, plugin: string, options?: RawAxiosRequestConfig) {
        return PluginApiFp(this.configuration).findPluginIcon(type, plugin, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Find transformer of the model matched by filter from the plugins.
     * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PluginApi
     */
    public findTransformer(filter?: string, options?: RawAxiosRequestConfig) {
        return PluginApiFp(this.configuration).findTransformer(filter, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} name 
     * @param {string} version 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof PluginApi
     */
    public findTransformerByNameAndVersion(name: string, version: string, options?: RawAxiosRequestConfig) {
        return PluginApiFp(this.configuration).findTransformerByNameAndVersion(name, version, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {File} file 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PluginApi
     */
    public uploadPlugin(file: File, options?: RawAxiosRequestConfig) {
        return PluginApiFp(this.configuration).uploadPlugin(file, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * ProjectApi - axios parameter creator
 * @export
 */
export const ProjectApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Find project of the model matched by filter from the data source.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findProject: async (filter?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1/project`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProjectApi - functional programming interface
 * @export
 */
export const ProjectApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProjectApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Find project of the model matched by filter from the data source.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findProject(filter?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessagePageProject>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findProject(filter, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProjectApi.findProject']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProjectApi - factory interface
 * @export
 */
export const ProjectApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProjectApiFp(configuration)
    return {
        /**
         * 
         * @summary Find project of the model matched by filter from the data source.
         * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findProject(filter?: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessagePageProject> {
            return localVarFp.findProject(filter, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ProjectApi - object-oriented interface
 * @export
 * @class ProjectApi
 * @extends {BaseAPI}
 */
export class ProjectApi extends BaseAPI {
    /**
     * 
     * @summary Find project of the model matched by filter from the data source.
     * @param {string} [filter] Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (&#x60;{\&quot;where\&quot;:{\&quot;something\&quot;:\&quot;value\&quot;},\&quot;field\&quot;:{\&quot;something\&quot;:true|false},\&quot;sort\&quot;: [\&quot;name desc\&quot;],\&quot;skip\&quot;:1,\&quot;limit\&quot;:20}&#x60;).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectApi
     */
    public findProject(filter?: string, options?: RawAxiosRequestConfig) {
        return ProjectApiFp(this.configuration).findProject(filter, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * UserApi - axios parameter creator
 * @export
 */
export const UserApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 查询当前登录用户菜单
         * @param {string} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getMenus: async (userId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('getMenus', 'userId', userId)
            const localVarPath = `/api/v1/user/{userId}/menus`
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 查询当前登录用户信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserInfo: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1/user`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * UserApi - functional programming interface
 * @export
 */
export const UserApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = UserApiAxiosParamCreator(configuration)
    return {
        /**
         * 查询当前登录用户菜单
         * @param {string} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getMenus(userId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageListMenu>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getMenus(userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserApi.getMenus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 查询当前登录用户信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUserInfo(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResponseMessageUser>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getUserInfo(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserApi.getUserInfo']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * UserApi - factory interface
 * @export
 */
export const UserApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = UserApiFp(configuration)
    return {
        /**
         * 查询当前登录用户菜单
         * @param {string} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getMenus(userId: string, options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageListMenu> {
            return localVarFp.getMenus(userId, options).then((request) => request(axios, basePath));
        },
        /**
         * 查询当前登录用户信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserInfo(options?: RawAxiosRequestConfig): AxiosPromise<ResponseMessageUser> {
            return localVarFp.getUserInfo(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * UserApi - object-oriented interface
 * @export
 * @class UserApi
 * @extends {BaseAPI}
 */
export class UserApi extends BaseAPI {
    /**
     * 查询当前登录用户菜单
     * @param {string} userId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public getMenus(userId: string, options?: RawAxiosRequestConfig) {
        return UserApiFp(this.configuration).getMenus(userId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 查询当前登录用户信息
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public getUserInfo(options?: RawAxiosRequestConfig) {
        return UserApiFp(this.configuration).getUserInfo(options).then((request) => request(this.axios, this.basePath));
    }
}



