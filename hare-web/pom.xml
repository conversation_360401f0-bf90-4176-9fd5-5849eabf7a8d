<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.qz.hare</groupId>
        <artifactId>hare</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>hare-web</artifactId>
    <name>hare-web</name>
    <packaging>jar</packaging>

    <properties>
        <frontend-maven-plugin.version>1.15.1</frontend-maven-plugin.version>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>node_modules</directory>
                        </fileset>
                        <fileset>
                            <directory>apps/cloud-ts/node_modules</directory>
                        </fileset>
                        <fileset>
                            <directory>packages/server-sdk/node_modules</directory>
                        </fileset>
                        <fileset>
                            <directory>packages/server-sdk/dist</directory>
                        </fileset>
                        <fileset>
                            <directory>packages/dag/node_modules</directory>
                        </fileset>
                        <fileset>
                            <directory>packages/utils/node_modules</directory>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.5.1</version>
                <executions>
                    <execution>
                        <id>pnpm build</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>pnpm</executable>
                            <arguments>
                                <argument>run</argument>
                                <argument>build</argument>
                            </arguments>
                            <workingDirectory>${project.basedir}</workingDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!--<plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <version>${frontend-maven-plugin.version}</version>

                <executions>
                    &lt;!&ndash;首次打包需要放开&ndash;&gt;
                    &lt;!&ndash;<execution>
                        <id>install node and npm</id>
                        <goals>
                            <goal>install-node-and-npm</goal>
                        </goals>
                        <configuration>
                            <nodeVersion>v20.9.0</nodeVersion>
                            <npmVersion>10.1.0</npmVersion>
                        </configuration>
                    </execution>
                    <execution>
                        <id>npm install pnpm</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <arguments>install -g pnpm</arguments>
                        </configuration>
                    </execution>&ndash;&gt;

                    <execution>
                        <id>pnpm run build</id>
                        <goals>
                            <goal>pnpm</goal>
                        </goals>
                        <phase>generate-sources</phase>
                        <configuration>
                            <arguments>run build</arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>-->
        </plugins>
    </build>


</project>
