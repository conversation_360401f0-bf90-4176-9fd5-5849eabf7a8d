<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spring-boot-starter-parent</artifactId>
        <groupId>org.springframework.boot</groupId>
        <version>3.5.4</version>
        <relativePath/>
    </parent>

    <groupId>com.qz.hare</groupId>
    <artifactId>hare</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <packaging>pom</packaging>

    <name>hare</name>

    <modules>
        <!--<module>hare-web</module>-->
        <module>hare-build-tools</module>

        <module>hare-common</module>
        <module>hare-schema-model</module>

        <module>hare-manager</module>
        <module>hare-engine</module>

        <module>hare-plugin-parent</module>
        <module>hare-integration-test</module>
    </modules>

    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>

        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <project-sourceEncoding>UTF-8</project-sourceEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <project.build.dist.name>hare-${project.version}</project.build.dist.name>
        <project.build.dist.engine.name>hare-engine-${project.version}</project.build.dist.engine.name>
        <project.build.dist.engine.plugin.path>${project.build.dist.name}/${project.build.dist.engine.name}/plugin</project.build.dist.engine.plugin.path>

        <shedlock.version>5.13.0</shedlock.version>
        <commons-text.version>1.12.0</commons-text.version>
        <commons-collections.version>4.4</commons-collections.version>
        <commons-io.version>2.16.1</commons-io.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <fastjson.version>2.0.49</fastjson.version>

        <springdoc.version>2.8.8</springdoc.version>
        <swagger-annotations.version>2.2.31</swagger-annotations.version>

        <graphlib.version>1.0.0</graphlib.version>

        <java-websocket.version>1.5.4</java-websocket.version>

        <mockito.version>5.12.0</mockito.version>
        <fastutil.version>8.5.13</fastutil.version>
        <oshi-core.version>6.1.5</oshi-core.version>
        <dependency-check.version>12.1.1</dependency-check.version>
        <exec-maven-plugin.version>3.5.0</exec-maven-plugin.version>
        <jacoco-maven-plugin.version>0.8.13</jacoco-maven-plugin.version>

        <mysql.version>9.3.0</mysql.version>

        <json-schema-validator.version>1.5.7</json-schema-validator.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>

            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-spring</artifactId>
                <version>${shedlock.version}</version>
            </dependency>
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-provider-mongo</artifactId>
                <version>${shedlock.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mongodb</groupId>
                        <artifactId>mongodb-driver-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mongodb</groupId>
                        <artifactId>mongodb-driver-sync</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.openlg</groupId>
                <artifactId>graphlib</artifactId>
                <version>${graphlib.version}</version>
            </dependency>

            <dependency>
                <groupId>org.java-websocket</groupId>
                <artifactId>Java-WebSocket</artifactId>
                <version>${java-websocket.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>it.unimi.dsi</groupId>
                <artifactId>fastutil</artifactId>
                <version>${fastutil.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.networknt</groupId>
                <artifactId>json-schema-validator</artifactId>
                <version>${json-schema-validator.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.owasp</groupId>
                    <artifactId>dependency-check-maven</artifactId>
                    <version>${dependency-check.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>${exec-maven-plugin.version}</version>
                </plugin>
                <!--<plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                </plugin>-->
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>${project.basedir}/dist/${project.build.dist.name}</directory>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.owasp</groupId>
                <artifactId>dependency-check-maven</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>aggregate</goal>
                        </goals>
                        <phase>verify</phase>
                    </execution>
                </executions>
                <configuration>
                    <!--<failBuildOnCVSS>8</failBuildOnCVSS>-->
                    <name>${project.artifactId}-dependency-check-report-${project.version}</name>
                    <autoUpdate>true</autoUpdate>
                    <formats>HTML,JSON</formats>
                    <prettyPrint>true</prettyPrint>
                    <nvdApiKey>07c60e1b-6e60-40d8-a0fe-29e290caf00c</nvdApiKey>
                    <skipProvidedScope>true</skipProvidedScope>
                    <skipSystemScope>true</skipSystemScope>
                    <assemblyAnalyzerEnabled>false</assemblyAnalyzerEnabled>
                </configuration>
            </plugin>

            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>compile</phase>
                    </execution>
                </executions>
                <configuration>
                    <offline>true</offline>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <failOnUnableToExtractRepoInfo>false</failOnUnableToExtractRepoInfo>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties</generateGitPropertiesFilename>
                    <!--<includeOnlyProperties>
                        <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
                        <includeOnlyProperty>^git.commit.id.(abbrev|full)$</includeOnlyProperty>
                    </includeOnlyProperties>-->
                    <commitIdGenerationMode>full</commitIdGenerationMode>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
